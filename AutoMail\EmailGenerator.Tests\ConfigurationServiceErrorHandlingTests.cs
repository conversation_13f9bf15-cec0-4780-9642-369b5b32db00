using System;
using System.IO;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using EmailGenerator.Services;
using EmailGenerator.Models;
using Newtonsoft.Json;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// ConfigurationService错误处理测试类
    /// </summary>
    [TestClass]
    public class ConfigurationServiceErrorHandlingTests
    {
        private ConfigurationService _configService;
        private PathManagerService _pathManager;
        private string _testDirectory;
        private string _testConfigFile;

        [TestInitialize]
        public void Setup()
        {
            _pathManager = new PathManagerService();
            _configService = new ConfigurationService(_pathManager);
            
            // 创建测试目录
            _testDirectory = Path.Combine(Path.GetTempPath(), "EmailGeneratorConfigTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);
            
            _testConfigFile = Path.Combine(_testDirectory, "config.json");
        }

        [TestCleanup]
        public void Cleanup()
        {
            try
            {
                if (File.Exists(_testConfigFile))
                    File.Delete(_testConfigFile);
                
                if (Directory.Exists(_testDirectory))
                    Directory.Delete(_testDirectory, true);
            }
            catch
            {
                // 清理失败不影响测试结果
            }
        }

        #region LoadDomain 错误处理测试

        [TestMethod]
        public void LoadDomain_NonExistentConfigFile_ReturnsDefaultDomain()
        {
            // Arrange
            string nonExistentPath = Path.Combine(_testDirectory, "nonexistent.json");
            Assert.IsFalse(File.Exists(nonExistentPath));

            // Act
            string result = _configService.LoadDomain();

            // Assert
            Assert.AreEqual("example.com", result);
        }

        [TestMethod]
        public void LoadDomain_EmptyConfigFile_ReturnsDefaultDomain()
        {
            // Arrange
            File.WriteAllText(_testConfigFile, "");

            // Act
            string result = _configService.LoadDomain();

            // Assert
            Assert.AreEqual("example.com", result);
        }

        [TestMethod]
        public void LoadDomain_WhitespaceConfigFile_ReturnsDefaultDomain()
        {
            // Arrange
            File.WriteAllText(_testConfigFile, "   \n\t  ");

            // Act
            string result = _configService.LoadDomain();

            // Assert
            Assert.AreEqual("example.com", result);
        }

        [TestMethod]
        public void LoadDomain_InvalidJsonConfigFile_ThrowsJsonException()
        {
            // Arrange
            File.WriteAllText(_testConfigFile, "{ invalid json content");

            // Act & Assert
            Assert.ThrowsException<JsonException>(() => _configService.LoadDomain());
        }

        [TestMethod]
        public void LoadDomain_CorruptedJsonConfigFile_ThrowsJsonException()
        {
            // Arrange
            File.WriteAllText(_testConfigFile, "{ \"domain\": \"test.com\", \"invalid\": }");

            // Act & Assert
            Assert.ThrowsException<JsonException>(() => _configService.LoadDomain());
        }

        #endregion

        #region SaveDomain 错误处理测试

        [TestMethod]
        public void SaveDomain_EmptyDomain_ThrowsArgumentException()
        {
            // Arrange
            string emptyDomain = "";

            // Act & Assert
            var exception = Assert.ThrowsException<ArgumentException>(() => _configService.SaveDomain(emptyDomain));
            Assert.AreEqual(ErrorMessages.DOMAIN_EMPTY, exception.Message);
        }

        [TestMethod]
        public void SaveDomain_NullDomain_ThrowsArgumentException()
        {
            // Arrange
            string nullDomain = null;

            // Act & Assert
            var exception = Assert.ThrowsException<ArgumentException>(() => _configService.SaveDomain(nullDomain));
            Assert.AreEqual(ErrorMessages.DOMAIN_EMPTY, exception.Message);
        }

        [TestMethod]
        public void SaveDomain_WhitespaceDomain_ThrowsArgumentException()
        {
            // Arrange
            string whitespaceDomain = "   ";

            // Act & Assert
            var exception = Assert.ThrowsException<ArgumentException>(() => _configService.SaveDomain(whitespaceDomain));
            Assert.AreEqual(ErrorMessages.DOMAIN_EMPTY, exception.Message);
        }

        [TestMethod]
        public void SaveDomain_InvalidDomainFormat_ThrowsArgumentException()
        {
            // Arrange
            string invalidDomain = "invalid-domain";

            // Act & Assert
            var exception = Assert.ThrowsException<ArgumentException>(() => _configService.SaveDomain(invalidDomain));
            Assert.AreEqual(ErrorMessages.DOMAIN_INVALID_FORMAT, exception.Message);
        }

        [TestMethod]
        public void SaveDomain_DomainWithInvalidChars_ThrowsArgumentException()
        {
            // Arrange
            string invalidDomain = "test<>.com";

            // Act & Assert
            var exception = Assert.ThrowsException<ArgumentException>(() => _configService.SaveDomain(invalidDomain));
            Assert.AreEqual(ErrorMessages.DOMAIN_INVALID_FORMAT, exception.Message);
        }

        [TestMethod]
        public void SaveDomain_DomainStartingWithDot_ThrowsArgumentException()
        {
            // Arrange
            string invalidDomain = ".test.com";

            // Act & Assert
            var exception = Assert.ThrowsException<ArgumentException>(() => _configService.SaveDomain(invalidDomain));
            Assert.AreEqual(ErrorMessages.DOMAIN_INVALID_FORMAT, exception.Message);
        }

        [TestMethod]
        public void SaveDomain_DomainEndingWithDot_ThrowsArgumentException()
        {
            // Arrange
            string invalidDomain = "test.com.";

            // Act & Assert
            var exception = Assert.ThrowsException<ArgumentException>(() => _configService.SaveDomain(invalidDomain));
            Assert.AreEqual(ErrorMessages.DOMAIN_INVALID_FORMAT, exception.Message);
        }

        [TestMethod]
        public void SaveDomain_DomainWithConsecutiveDots_ThrowsArgumentException()
        {
            // Arrange
            string invalidDomain = "test..com";

            // Act & Assert
            var exception = Assert.ThrowsException<ArgumentException>(() => _configService.SaveDomain(invalidDomain));
            Assert.AreEqual(ErrorMessages.DOMAIN_INVALID_FORMAT, exception.Message);
        }

        [TestMethod]
        public void SaveDomain_DomainWithoutDot_ThrowsArgumentException()
        {
            // Arrange
            string invalidDomain = "testcom";

            // Act & Assert
            var exception = Assert.ThrowsException<ArgumentException>(() => _configService.SaveDomain(invalidDomain));
            Assert.AreEqual(ErrorMessages.DOMAIN_INVALID_FORMAT, exception.Message);
        }

        [TestMethod]
        public void SaveDomain_ValidDomain_SavesSuccessfully()
        {
            // Arrange
            string validDomain = "test.com";

            // Act
            bool result = _configService.SaveDomain(validDomain);

            // Assert
            Assert.IsTrue(result);
            
            // 验证保存的内容
            string loadedDomain = _configService.LoadDomain();
            Assert.AreEqual(validDomain, loadedDomain);
        }

        [TestMethod]
        public void SaveDomain_ValidDomainWithSpaces_TrimsAndSaves()
        {
            // Arrange
            string domainWithSpaces = "  test.com  ";
            string expectedDomain = "test.com";

            // Act
            bool result = _configService.SaveDomain(domainWithSpaces);

            // Assert
            Assert.IsTrue(result);
            
            // 验证保存的内容被正确修剪
            string loadedDomain = _configService.LoadDomain();
            Assert.AreEqual(expectedDomain, loadedDomain);
        }

        [TestMethod]
        public void SaveDomain_ValidDomainWithUpperCase_ConvertsToLowerCase()
        {
            // Arrange
            string upperCaseDomain = "TEST.COM";
            string expectedDomain = "test.com";

            // Act
            bool result = _configService.SaveDomain(upperCaseDomain);

            // Assert
            Assert.IsTrue(result);
            
            // 验证保存的内容被转换为小写
            string loadedDomain = _configService.LoadDomain();
            Assert.AreEqual(expectedDomain, loadedDomain);
        }

        #endregion

        #region ValidatePath 错误处理测试

        [TestMethod]
        public void ValidatePath_EmptyPath_ReturnsTrue()
        {
            // Arrange
            string emptyPath = "";

            // Act
            bool result = _configService.ValidatePath(emptyPath);

            // Assert
            Assert.IsTrue(result); // 空路径被认为是有效的（使用默认路径）
        }

        [TestMethod]
        public void ValidatePath_NullPath_ReturnsTrue()
        {
            // Arrange
            string nullPath = null;

            // Act
            bool result = _configService.ValidatePath(nullPath);

            // Assert
            Assert.IsTrue(result); // null路径被认为是有效的（使用默认路径）
        }

        [TestMethod]
        public void ValidatePath_WhitespacePath_ReturnsTrue()
        {
            // Arrange
            string whitespacePath = "   ";

            // Act
            bool result = _configService.ValidatePath(whitespacePath);

            // Assert
            Assert.IsTrue(result); // 空白路径被认为是有效的（使用默认路径）
        }

        [TestMethod]
        public void ValidatePath_ValidPath_ReturnsTrue()
        {
            // Arrange
            string validPath = _testDirectory;

            // Act
            bool result = _configService.ValidatePath(validPath);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void ValidatePath_InvalidPath_ReturnsFalse()
        {
            // Arrange
            string invalidPath = "C:\\test<>path";

            // Act
            bool result = _configService.ValidatePath(invalidPath);

            // Assert
            Assert.IsFalse(result);
        }

        #endregion

        #region ValidatePathWithDetails 错误处理测试

        [TestMethod]
        public void ValidatePathWithDetails_EmptyPath_ReturnsValidResult()
        {
            // Arrange
            string emptyPath = "";

            // Act
            var result = _configService.ValidatePathWithDetails(emptyPath);

            // Assert
            Assert.IsTrue(result.IsValid);
            Assert.AreEqual(emptyPath, result.Path);
            Assert.AreEqual(string.Empty, result.ErrorMessage);
            Assert.AreEqual(string.Empty, result.Guidance);
        }

        [TestMethod]
        public void ValidatePathWithDetails_ValidPath_ReturnsValidResult()
        {
            // Arrange
            string validPath = _testDirectory;

            // Act
            var result = _configService.ValidatePathWithDetails(validPath);

            // Assert
            Assert.IsTrue(result.IsValid);
            Assert.AreEqual(validPath, result.Path);
            Assert.AreEqual(string.Empty, result.ErrorMessage);
            Assert.AreEqual(string.Empty, result.Guidance);
        }

        [TestMethod]
        public void ValidatePathWithDetails_InvalidPath_ReturnsInvalidResult()
        {
            // Arrange
            string invalidPath = "C:\\test<>path";

            // Act
            var result = _configService.ValidatePathWithDetails(invalidPath);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual(invalidPath, result.Path);
            Assert.IsNotNull(result.ErrorMessage);
            Assert.IsTrue(result.ErrorMessage.Length > 0);
            Assert.IsNotNull(result.Guidance);
        }

        #endregion

        #region SetCustomConfigPath 回退机制测试

        [TestMethod]
        public void SetCustomConfigPathWithFallback_InvalidPath_EnabledFallback_ReturnsTrue()
        {
            // Arrange
            string invalidPath = "C:\\test<>path";

            // Act
            bool result = _configService.SetCustomConfigPathWithFallback(invalidPath, true);

            // Assert
            Assert.IsTrue(result); // 启用回退机制时应该返回true
        }

        [TestMethod]
        public void SetCustomConfigPathWithFallback_InvalidPath_DisabledFallback_ReturnsFalse()
        {
            // Arrange
            string invalidPath = "C:\\test<>path";

            // Act
            bool result = _configService.SetCustomConfigPathWithFallback(invalidPath, false);

            // Assert
            Assert.IsFalse(result); // 禁用回退机制时应该返回false
        }

        [TestMethod]
        public void SetCustomConfigPathWithFallback_ValidPath_ReturnsTrue()
        {
            // Arrange
            string validPath = _testDirectory;

            // Act
            bool result = _configService.SetCustomConfigPathWithFallback(validPath, true);

            // Assert
            Assert.IsTrue(result);
            
            // 验证路径设置成功
            string customPath = _configService.GetCustomConfigPath();
            Assert.AreEqual(validPath, customPath);
        }

        #endregion

        #region SetCustomDataPath 回退机制测试

        [TestMethod]
        public void SetCustomDataPathWithFallback_InvalidPath_EnabledFallback_ReturnsTrue()
        {
            // Arrange
            string invalidPath = "C:\\test<>path";

            // Act
            bool result = _configService.SetCustomDataPathWithFallback(invalidPath, true);

            // Assert
            Assert.IsTrue(result); // 启用回退机制时应该返回true
        }

        [TestMethod]
        public void SetCustomDataPathWithFallback_InvalidPath_DisabledFallback_ReturnsFalse()
        {
            // Arrange
            string invalidPath = "C:\\test<>path";

            // Act
            bool result = _configService.SetCustomDataPathWithFallback(invalidPath, false);

            // Assert
            Assert.IsFalse(result); // 禁用回退机制时应该返回false
        }

        [TestMethod]
        public void SetCustomDataPathWithFallback_ValidPath_ReturnsTrue()
        {
            // Arrange
            string validPath = _testDirectory;

            // Act
            bool result = _configService.SetCustomDataPathWithFallback(validPath, true);

            // Assert
            Assert.IsTrue(result);
            
            // 验证路径设置成功
            string customPath = _configService.GetCustomDataPath();
            Assert.AreEqual(validPath, customPath);
        }

        #endregion

        #region ResetToDefaultPaths 测试

        [TestMethod]
        public void ResetToDefaultPaths_AfterSettingCustomPaths_ResetsSuccessfully()
        {
            // Arrange
            string customPath = _testDirectory;
            _configService.SetCustomConfigPath(customPath);
            _configService.SetCustomDataPath(customPath);
            
            // 验证自定义路径已设置
            Assert.IsTrue(_configService.GetUseCustomPaths());
            Assert.AreEqual(customPath, _configService.GetCustomConfigPath());
            Assert.AreEqual(customPath, _configService.GetCustomDataPath());

            // Act
            bool result = _configService.ResetToDefaultPaths();

            // Assert
            Assert.IsTrue(result);
            Assert.IsFalse(_configService.GetUseCustomPaths());
            Assert.AreEqual("", _configService.GetCustomConfigPath());
            Assert.AreEqual("", _configService.GetCustomDataPath());
        }

        #endregion

        #region SavePathConfiguration 测试

        [TestMethod]
        public void SavePathConfiguration_ValidPaths_SavesSuccessfully()
        {
            // Arrange
            bool useCustomPaths = true;
            string configPath = _testDirectory;
            string dataPath = _testDirectory;

            // Act
            bool result = _configService.SavePathConfiguration(useCustomPaths, configPath, dataPath);

            // Assert
            Assert.IsTrue(result);
            Assert.AreEqual(useCustomPaths, _configService.GetUseCustomPaths());
            Assert.AreEqual(configPath, _configService.GetCustomConfigPath());
            Assert.AreEqual(dataPath, _configService.GetCustomDataPath());
        }

        [TestMethod]
        public void SavePathConfiguration_DisableCustomPaths_SavesSuccessfully()
        {
            // Arrange
            // 先设置自定义路径
            _configService.SavePathConfiguration(true, _testDirectory, _testDirectory);
            Assert.IsTrue(_configService.GetUseCustomPaths());

            // Act - 禁用自定义路径
            bool result = _configService.SavePathConfiguration(false, "", "");

            // Assert
            Assert.IsTrue(result);
            Assert.IsFalse(_configService.GetUseCustomPaths());
            Assert.AreEqual("", _configService.GetCustomConfigPath());
            Assert.AreEqual("", _configService.GetCustomDataPath());
        }

        [TestMethod]
        public void SavePathConfiguration_NullPaths_TreatsAsEmpty()
        {
            // Arrange
            bool useCustomPaths = false;
            string configPath = null;
            string dataPath = null;

            // Act
            bool result = _configService.SavePathConfiguration(useCustomPaths, configPath, dataPath);

            // Assert
            Assert.IsTrue(result);
            Assert.IsFalse(_configService.GetUseCustomPaths());
            Assert.AreEqual("", _configService.GetCustomConfigPath());
            Assert.AreEqual("", _configService.GetCustomDataPath());
        }

        #endregion

        #region GetCurrentConfigPath 和 GetCurrentDataPath 测试

        [TestMethod]
        public void GetCurrentConfigPath_NoCustomPath_ReturnsDefaultPath()
        {
            // Arrange
            _configService.ResetToDefaultPaths();

            // Act
            string currentPath = _configService.GetCurrentConfigPath();

            // Assert
            Assert.IsNotNull(currentPath);
            Assert.IsTrue(currentPath.EndsWith("config.json"));
        }

        [TestMethod]
        public void GetCurrentDataPath_NoCustomPath_ReturnsDefaultPath()
        {
            // Arrange
            _configService.ResetToDefaultPaths();

            // Act
            string currentPath = _configService.GetCurrentDataPath();

            // Assert
            Assert.IsNotNull(currentPath);
            Assert.IsTrue(currentPath.EndsWith("emails.json"));
        }

        [TestMethod]
        public void GetCurrentConfigPath_WithCustomPath_ReturnsCustomPath()
        {
            // Arrange
            string customPath = _testDirectory;
            _configService.SavePathConfiguration(true, customPath, customPath);

            // Act
            string currentPath = _configService.GetCurrentConfigPath();

            // Assert
            Assert.IsNotNull(currentPath);
            Assert.IsTrue(currentPath.StartsWith(customPath));
            Assert.IsTrue(currentPath.EndsWith("config.json"));
        }

        [TestMethod]
        public void GetCurrentDataPath_WithCustomPath_ReturnsCustomPath()
        {
            // Arrange
            string customPath = _testDirectory;
            _configService.SavePathConfiguration(true, customPath, customPath);

            // Act
            string currentPath = _configService.GetCurrentDataPath();

            // Assert
            Assert.IsNotNull(currentPath);
            Assert.IsTrue(currentPath.StartsWith(customPath));
            Assert.IsTrue(currentPath.EndsWith("emails.json"));
        }

        #endregion
    }
}