using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using EmailGenerator.Models;
using EmailGenerator.Services;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// DataService的单元测试
    /// </summary>
    [TestClass]
    public class DataServiceTests
    {
        private DataService _dataService;
        private string _testDataPath;

        [TestInitialize]
        public void Setup()
        {
            _testDataPath = Path.Combine(Path.GetTempPath(), "test_emails.json");
            _dataService = new DataService(_testDataPath);
            
            // 清理测试环境
            if (File.Exists(_testDataPath))
            {
                File.Delete(_testDataPath);
            }
        }

        [TestCleanup]
        public void Cleanup()
        {
            // 清理测试文件
            if (File.Exists(_testDataPath))
            {
                File.Delete(_testDataPath);
            }
        }

        [TestMethod]
        public void LoadEmails_FileNotExists_ReturnsEmptyList()
        {
            // Act
            var emails = _dataService.LoadEmails();

            // Assert
            Assert.IsNotNull(emails);
            Assert.AreEqual(0, emails.Count);
        }

        [TestMethod]
        public void SaveEmails_ValidList_ReturnsTrue()
        {
            // Arrange
            var emails = new List<EmailRecord>
            {
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Domain = "example.com",
                    Username = "test",
                    CreatedDate = DateTime.Now,
                    Status = EmailStatus.Active
                }
            };

            // Act
            bool result = _dataService.SaveEmails(emails);

            // Assert
            Assert.IsTrue(result);
            Assert.IsTrue(File.Exists(_testDataPath));
        }

        [TestMethod]
        public void AddEmail_ValidEmail_ReturnsTrue()
        {
            // Arrange
            var email = new EmailRecord
            {
                Email = "<EMAIL>",
                Domain = "test.com",
                Username = "new",
                CreatedDate = DateTime.Now,
                Status = EmailStatus.Active
            };

            // Act
            bool result = _dataService.AddEmail(email);

            // Assert
            Assert.IsTrue(result);
            
            var loadedEmails = _dataService.LoadEmails();
            Assert.AreEqual(1, loadedEmails.Count);
            Assert.AreEqual("<EMAIL>", loadedEmails[0].Email);
        }

        [TestMethod]
        public void AddEmail_DuplicateEmail_ReturnsFalse()
        {
            // Arrange
            var email1 = new EmailRecord
            {
                Email = "<EMAIL>",
                Domain = "test.com",
                Username = "duplicate",
                CreatedDate = DateTime.Now,
                Status = EmailStatus.Active
            };

            var email2 = new EmailRecord
            {
                Email = "<EMAIL>", // 相同的邮箱
                Domain = "test.com",
                Username = "duplicate2",
                CreatedDate = DateTime.Now,
                Status = EmailStatus.Active
            };

            // Act
            bool result1 = _dataService.AddEmail(email1);
            bool result2 = _dataService.AddEmail(email2);

            // Assert
            Assert.IsTrue(result1);
            Assert.IsFalse(result2); // 重复邮箱应该添加失败
            
            var loadedEmails = _dataService.LoadEmails();
            Assert.AreEqual(1, loadedEmails.Count);
        }

        [TestMethod]
        public void DeleteEmail_ExistingEmail_ReturnsTrue()
        {
            // Arrange
            var email = new EmailRecord
            {
                Email = "<EMAIL>",
                Domain = "test.com",
                Username = "delete",
                CreatedDate = DateTime.Now,
                Status = EmailStatus.Active
            };
            _dataService.AddEmail(email);

            // Act
            bool result = _dataService.DeleteEmail("<EMAIL>");

            // Assert
            Assert.IsTrue(result);
            
            var loadedEmails = _dataService.LoadEmails();
            Assert.AreEqual(0, loadedEmails.Count);
        }

        [TestMethod]
        public void DeleteEmail_NonExistingEmail_ReturnsFalse()
        {
            // Act
            bool result = _dataService.DeleteEmail("<EMAIL>");

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void DeactivateEmail_ExistingEmail_ReturnsTrue()
        {
            // Arrange
            var email = new EmailRecord
            {
                Email = "<EMAIL>",
                Domain = "test.com",
                Username = "deactivate",
                CreatedDate = DateTime.Now,
                Status = EmailStatus.Active
            };
            _dataService.AddEmail(email);

            // Act
            bool result = _dataService.DeactivateEmail("<EMAIL>");

            // Assert
            Assert.IsTrue(result);
            
            var loadedEmails = _dataService.LoadEmails();
            var deactivatedEmail = loadedEmails.FirstOrDefault(e => e.Email == "<EMAIL>");
            Assert.IsNotNull(deactivatedEmail);
            Assert.AreEqual(EmailStatus.Deactivated, deactivatedEmail.Status);
            Assert.IsNotNull(deactivatedEmail.DeactivatedDate);
        }

        [TestMethod]
        public void UpdateEmailStatus_ExistingEmail_ReturnsTrue()
        {
            // Arrange
            var email = new EmailRecord
            {
                Email = "<EMAIL>",
                Domain = "test.com",
                Username = "update",
                CreatedDate = DateTime.Now,
                Status = EmailStatus.Active
            };
            _dataService.AddEmail(email);

            // Act
            bool result = _dataService.UpdateEmailStatus("<EMAIL>", EmailStatus.Deactivated);

            // Assert
            Assert.IsTrue(result);
            
            var loadedEmails = _dataService.LoadEmails();
            var updatedEmail = loadedEmails.FirstOrDefault(e => e.Email == "<EMAIL>");
            Assert.IsNotNull(updatedEmail);
            Assert.AreEqual(EmailStatus.Deactivated, updatedEmail.Status);
        }

        [TestMethod]
        public void EmailExists_ExistingEmail_ReturnsTrue()
        {
            // Arrange
            var email = new EmailRecord
            {
                Email = "<EMAIL>",
                Domain = "test.com",
                Username = "exists",
                CreatedDate = DateTime.Now,
                Status = EmailStatus.Active
            };
            _dataService.AddEmail(email);

            // Act
            bool result = _dataService.EmailExists("<EMAIL>");

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void EmailExists_NonExistingEmail_ReturnsFalse()
        {
            // Act
            bool result = _dataService.EmailExists("<EMAIL>");

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void GetActiveEmails_MixedStatuses_ReturnsOnlyActive()
        {
            // Arrange
            var emails = new List<EmailRecord>
            {
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Domain = "test.com",
                    Username = "active1",
                    CreatedDate = DateTime.Now,
                    Status = EmailStatus.Active
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Domain = "test.com",
                    Username = "deactivated",
                    CreatedDate = DateTime.Now,
                    Status = EmailStatus.Deactivated
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Domain = "test.com",
                    Username = "active2",
                    CreatedDate = DateTime.Now,
                    Status = EmailStatus.Active
                }
            };
            _dataService.SaveEmails(emails);

            // Act
            var activeEmails = _dataService.GetActiveEmails();

            // Assert
            Assert.AreEqual(2, activeEmails.Count);
            Assert.IsTrue(activeEmails.Contains("<EMAIL>"));
            Assert.IsTrue(activeEmails.Contains("<EMAIL>"));
            Assert.IsFalse(activeEmails.Contains("<EMAIL>"));
        }
    }
}