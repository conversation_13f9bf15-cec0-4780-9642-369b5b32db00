using Microsoft.VisualStudio.TestTools.UnitTesting;
using EmailGenerator.Services;
using System;
using System.IO;

namespace EmailGenerator.Tests
{
    [TestClass]
    public class PathManagerServiceTests
    {
        private PathManagerService pathManager;
        private string testDirectory;

        [TestInitialize]
        public void Setup()
        {
            pathManager = new PathManagerService();
            testDirectory = Path.Combine(Path.GetTempPath(), "EmailGeneratorTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(testDirectory);
        }

        [TestCleanup]
        public void Cleanup()
        {
            if (Directory.Exists(testDirectory))
            {
                try
                {
                    Directory.Delete(testDirectory, true);
                }
                catch
                {
                    // 忽略清理错误
                }
            }
        }

        [TestMethod]
        public void GetExecutableDirectory_ShouldReturnValidPath()
        {
            // Act
            string result = pathManager.GetExecutableDirectory();

            // Assert
            Assert.IsNotNull(result, "可执行文件目录不应为null");
            Assert.IsTrue(Directory.Exists(result), "可执行文件目录应该存在");
            Assert.IsFalse(string.IsNullOrWhiteSpace(result), "可执行文件目录不应为空");
        }

        [TestMethod]
        public void GetDefaultConfigPath_ShouldReturnValidConfigPath()
        {
            // Act
            string result = pathManager.GetDefaultConfigPath();

            // Assert
            Assert.IsNotNull(result, "默认配置路径不应为null");
            Assert.IsTrue(result.EndsWith("config.json"), "默认配置路径应以config.json结尾");
            Assert.IsTrue(Path.IsPathRooted(result), "默认配置路径应为绝对路径");
        }

        [TestMethod]
        public void GetDefaultDataPath_ShouldReturnValidDataPath()
        {
            // Act
            string result = pathManager.GetDefaultDataPath();

            // Assert
            Assert.IsNotNull(result, "默认数据路径不应为null");
            Assert.IsTrue(result.EndsWith("emails.json"), "默认数据路径应以emails.json结尾");
            Assert.IsTrue(Path.IsPathRooted(result), "默认数据路径应为绝对路径");
        }

        [TestMethod]
        public void IsValidPath_WithValidPath_ShouldReturnTrue()
        {
            // Arrange
            string validPath = @"C:\ValidPath\test.txt";

            // Act
            bool result = pathManager.IsValidPath(validPath);

            // Assert
            Assert.IsTrue(result, "有效路径应返回true");
        }

        [TestMethod]
        public void IsValidPath_WithInvalidPath_ShouldReturnFalse()
        {
            // Arrange
            string invalidPath = @"C:\Invalid|Path\test.txt";

            // Act
            bool result = pathManager.IsValidPath(invalidPath);

            // Assert
            Assert.IsFalse(result, "无效路径应返回false");
        }

        [TestMethod]
        public void IsValidPath_WithNullOrEmpty_ShouldReturnFalse()
        {
            // Act & Assert
            Assert.IsFalse(pathManager.IsValidPath(null), "null路径应返回false");
            Assert.IsFalse(pathManager.IsValidPath(""), "空字符串路径应返回false");
            Assert.IsFalse(pathManager.IsValidPath("   "), "空白字符串路径应返回false");
        }

        [TestMethod]
        public void IsWritablePath_WithWritableDirectory_ShouldReturnTrue()
        {
            // Act
            bool result = pathManager.IsWritablePath(testDirectory);

            // Assert
            Assert.IsTrue(result, "可写目录应返回true");
        }

        [TestMethod]
        public void IsWritablePath_WithWritableFilePath_ShouldReturnTrue()
        {
            // Arrange
            string testFilePath = Path.Combine(testDirectory, "test.txt");

            // Act
            bool result = pathManager.IsWritablePath(testFilePath);

            // Assert
            Assert.IsTrue(result, "可写文件路径应返回true");
        }

        [TestMethod]
        public void IsWritablePath_WithInvalidPath_ShouldReturnFalse()
        {
            // Arrange
            string invalidPath = @"C:\Invalid|Path";

            // Act
            bool result = pathManager.IsWritablePath(invalidPath);

            // Assert
            Assert.IsFalse(result, "无效路径应返回false");
        }

        [TestMethod]
        public void IsWritablePath_WithNonExistentParentDirectory_ShouldReturnFalse()
        {
            // Arrange
            // 使用一个不存在的驱动器路径
            string nonExistentPath = @"Z:\NonExistentDirectory\SubDirectory\test.txt";

            // Act
            bool result = pathManager.IsWritablePath(nonExistentPath);

            // Assert
            Assert.IsFalse(result, "不存在的父目录路径应返回false");
        }

        [TestMethod]
        public void CreateDirectoryIfNotExists_WithNewDirectory_ShouldCreateAndReturnTrue()
        {
            // Arrange
            string newDirectory = Path.Combine(testDirectory, "NewSubDirectory");

            // Act
            bool result = pathManager.CreateDirectoryIfNotExists(newDirectory);

            // Assert
            Assert.IsTrue(result, "创建新目录应返回true");
            Assert.IsTrue(Directory.Exists(newDirectory), "新目录应该被创建");
        }

        [TestMethod]
        public void CreateDirectoryIfNotExists_WithExistingDirectory_ShouldReturnTrue()
        {
            // Act
            bool result = pathManager.CreateDirectoryIfNotExists(testDirectory);

            // Assert
            Assert.IsTrue(result, "已存在的目录应返回true");
            Assert.IsTrue(Directory.Exists(testDirectory), "目录应该仍然存在");
        }

        [TestMethod]
        public void CreateDirectoryIfNotExists_WithFilePath_ShouldCreateParentDirectory()
        {
            // Arrange
            string subDirectory = Path.Combine(testDirectory, "SubDir");
            string filePath = Path.Combine(subDirectory, "test.txt");

            // Act
            bool result = pathManager.CreateDirectoryIfNotExists(filePath);

            // Assert
            Assert.IsTrue(result, "为文件路径创建父目录应返回true");
            Assert.IsTrue(Directory.Exists(subDirectory), "父目录应该被创建");
        }

        [TestMethod]
        public void CreateDirectoryIfNotExists_WithInvalidPath_ShouldReturnFalse()
        {
            // Arrange
            string invalidPath = @"C:\Invalid|Path";

            // Act
            bool result = pathManager.CreateDirectoryIfNotExists(invalidPath);

            // Assert
            Assert.IsFalse(result, "无效路径应返回false");
        }

        [TestMethod]
        public void CreateDirectoryIfNotExists_WithNullOrEmpty_ShouldReturnFalse()
        {
            // Act & Assert
            Assert.IsFalse(pathManager.CreateDirectoryIfNotExists(null), "null路径应返回false");
            Assert.IsFalse(pathManager.CreateDirectoryIfNotExists(""), "空字符串路径应返回false");
            Assert.IsFalse(pathManager.CreateDirectoryIfNotExists("   "), "空白字符串路径应返回false");
        }

        [TestMethod]
        public void SelectFolderDialog_ShouldReturnNullWhenCancelled()
        {
            // 注意：这个测试无法自动化，因为它需要用户交互
            // 在实际应用中，可以通过依赖注入或接口抽象来使其可测试
            // 这里只是验证方法存在且可以调用
            
            // Act & Assert
            // 由于需要用户交互，我们只验证方法不会抛出异常
            Assert.IsNotNull(pathManager, "PathManagerService应该被正确初始化");
        }

        [TestMethod]
        public void PathOperations_IntegrationTest()
        {
            // Arrange
            string testPath = Path.Combine(testDirectory, "IntegrationTest", "test.json");
            
            // Act & Assert
            // 1. 验证路径有效性
            Assert.IsTrue(pathManager.IsValidPath(testPath), "测试路径应该有效");
            
            // 2. 创建目录
            Assert.IsTrue(pathManager.CreateDirectoryIfNotExists(testPath), "应该能够创建目录");
            
            // 3. 验证路径可写性
            Assert.IsTrue(pathManager.IsWritablePath(testPath), "创建的路径应该可写");
            
            // 4. 验证目录确实存在
            string directoryPath = Path.GetDirectoryName(testPath);
            Assert.IsTrue(Directory.Exists(directoryPath), "目录应该存在");
        }
    }
}