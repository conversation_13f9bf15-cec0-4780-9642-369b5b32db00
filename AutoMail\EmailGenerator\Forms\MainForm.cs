using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using EmailGenerator.Models;
using EmailGenerator.Services;
using EmailGenerator.Helpers;
using Newtonsoft.Json;

namespace EmailGenerator.Forms
{
    /// <summary>
    /// 主窗体类
    /// </summary>
    public partial class MainForm : Form
    {
        private EmailService _emailService;
        private ConfigurationService _configService;
        private DataService _dataService;
        private PathManagerService _pathManager;
        private List<EmailRecord> _emailHistory;

        // 防重复点击标志
        private bool _isOperationInProgress = false;

        public MainForm()
        {
            InitializeComponent();
            InitializeServices();
            
            // 注册窗体加载事件
            this.Load += MainForm_Load;
            
            // 启用键盘快捷键
            this.KeyPreview = true;
            this.KeyDown += MainForm_KeyDown;
        }

        /// <summary>
        /// 初始化服务实例
        /// </summary>
        private void InitializeServices()
        {
            _pathManager = new PathManagerService();
            _configService = new ConfigurationService(_pathManager);
            _dataService = new DataService(_configService, _pathManager);
            _emailService = new EmailService(_dataService);
            _emailHistory = new List<EmailRecord>();
        }



        /// <summary>
        /// 键盘快捷键处理
        /// </summary>
        private void MainForm_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                // 如果正在进行操作，忽略快捷键
                if (_isOperationInProgress)
                    return;

                // Ctrl+C 复制选中的邮箱
                if (e.Control && e.KeyCode == Keys.C && btnCopy.Enabled)
                {
                    e.Handled = true;
                    BtnCopy_Click(sender, e);
                }
                // Delete 删除选中的邮箱
                else if (e.KeyCode == Keys.Delete && btnDelete.Enabled)
                {
                    e.Handled = true;
                    BtnDelete_Click(sender, e);
                }
                // F5 刷新邮箱列表
                else if (e.KeyCode == Keys.F5)
                {
                    e.Handled = true;
                    _ = RefreshEmailListAsync();
                }
                // Enter 生成新邮箱（当焦点在域名输入框时）
                else if (e.KeyCode == Keys.Enter && txtDomain.Focused)
                {
                    e.Handled = true;
                    BtnGenerate_Click(sender, e);
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowExceptionMessage(ex, "处理快捷键时发生错误", this);
            }
        }

        /// <summary>
        /// 窗体加载事件处理器
        /// </summary>
        private async void MainForm_Load(object sender, EventArgs e)
        {
            try
            {
                ShowProgress("正在初始化程序...");
                
                // 加载配置信息
                LoadConfiguration();
                
                // 异步加载邮箱历史记录
                await LoadEmailHistoryAsync();
                
                // 设置窗体默认状态
                SetDefaultFormState();
                
                // 更新路径状态显示
                UpdatePathStatusDisplay();
                
                HideProgress("程序初始化完成，就绪");
            }
            catch (Exception ex)
            {
                HideProgress();
                MessageHelper.ShowExceptionMessage(ex, "程序初始化失败", this);
                UpdateStatus("程序初始化失败");
            }
        }

        /// <summary>
        /// 设置窗体默认状态
        /// </summary>
        private void SetDefaultFormState()
        {
            // 设置默认焦点
            if (string.IsNullOrWhiteSpace(txtDomain.Text))
            {
                txtDomain.Focus();
            }
            else
            {
                btnGenerate.Focus();
            }

            // 确保按钮状态正确
            bool hasSelection = lstEmails.SelectedIndex >= 0 && _emailHistory.Count > 0;
            btnCopy.Enabled = hasSelection;
            btnDeactivate.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection;

            // 设置窗体居中显示
            this.CenterToScreen();
        }

        /// <summary>
        /// 加载配置信息
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                string domain = _configService.LoadDomain();
                txtDomain.Text = domain;
                UpdateStatus($"已加载域名配置: {domain}");
            }
            catch (InvalidOperationException ex)
            {
                MessageHelper.ShowErrorMessage(ex.Message, "配置加载失败", this);
                UpdateStatus(ErrorMessages.CONFIG_LOAD_FAILED);
            }
            catch (JsonException ex)
            {
                MessageHelper.ShowErrorMessage(ex.Message, "配置文件错误", this);
                UpdateStatus(ErrorMessages.CONFIG_INVALID);
            }
            catch (Exception ex)
            {
                MessageHelper.ShowExceptionMessage(ex, ErrorMessages.CONFIG_LOAD_FAILED, this);
                UpdateStatus(ErrorMessages.CONFIG_LOAD_FAILED);
            }
        }

        /// <summary>
        /// 加载邮箱历史记录（异步版本）
        /// </summary>
        private async Task LoadEmailHistoryAsync()
        {
            try
            {
                _emailHistory = await _dataService.LoadEmailsAsync();
                RefreshEmailList();
                UpdateStatus($"已加载 {_emailHistory.Count} 条邮箱记录");
            }
            catch (InvalidOperationException ex)
            {
                MessageHelper.ShowErrorMessage(ex.Message, "数据加载失败", this);
                UpdateStatus(ErrorMessages.DATA_LOAD_FAILED);
                _emailHistory = new List<EmailRecord>(); // 确保列表不为null
            }
            catch (JsonException ex)
            {
                MessageHelper.ShowErrorMessage(ex.Message, "数据文件错误", this);
                UpdateStatus(ErrorMessages.DATA_PARSE_ERROR);
                _emailHistory = new List<EmailRecord>();
            }
            catch (Exception ex)
            {
                MessageHelper.ShowExceptionMessage(ex, ErrorMessages.DATA_LOAD_FAILED, this);
                UpdateStatus(ErrorMessages.DATA_LOAD_FAILED);
                _emailHistory = new List<EmailRecord>();
            }
        }

        /// <summary>
        /// 加载邮箱历史记录（同步版本，保持向后兼容）
        /// </summary>
        private void LoadEmailHistory()
        {
            try
            {
                _emailHistory = _dataService.LoadEmails();
                RefreshEmailList();
                UpdateStatus($"已加载 {_emailHistory.Count} 条邮箱记录");
            }
            catch (InvalidOperationException ex)
            {
                MessageHelper.ShowErrorMessage(ex.Message, "数据加载失败", this);
                UpdateStatus(ErrorMessages.DATA_LOAD_FAILED);
                _emailHistory = new List<EmailRecord>(); // 确保列表不为null
            }
            catch (JsonException ex)
            {
                MessageHelper.ShowErrorMessage(ex.Message, "数据文件错误", this);
                UpdateStatus(ErrorMessages.DATA_PARSE_ERROR);
                _emailHistory = new List<EmailRecord>();
            }
            catch (Exception ex)
            {
                MessageHelper.ShowExceptionMessage(ex, ErrorMessages.DATA_LOAD_FAILED, this);
                UpdateStatus(ErrorMessages.DATA_LOAD_FAILED);
                _emailHistory = new List<EmailRecord>();
            }
        }

        /// <summary>
        /// 刷新邮箱列表显示（异步版本）
        /// </summary>
        private async Task RefreshEmailListAsync()
        {
            try
            {
                ShowProgress("正在刷新邮箱列表...");
                
                // 重新加载数据
                _emailHistory = await _dataService.LoadEmailsAsync();
                
                // 更新UI
                RefreshEmailList();
                
                HideProgress($"邮箱列表已刷新，共 {_emailHistory.Count} 条记录");
            }
            catch (Exception ex)
            {
                HideProgress();
                MessageHelper.ShowExceptionMessage(ex, "刷新邮箱列表失败", this);
                UpdateStatus("刷新失败");
            }
        }

        /// <summary>
        /// 刷新邮箱列表显示（同步版本）
        /// </summary>
        private void RefreshEmailList()
        {
            // 使用BeginUpdate/EndUpdate优化ListBox性能
            lstEmails.BeginUpdate();
            try
            {
                lstEmails.Items.Clear();
                
                if (_emailHistory == null || _emailHistory.Count == 0)
                {
                    lstEmails.Items.Add("暂无邮箱记录");
                    return;
                }

                foreach (var email in _emailHistory.OrderByDescending(e => e.CreatedDate))
                {
                    string statusIcon = email.Status == EmailStatus.Active ? "✓" : "✗";
                    string statusText = email.Status == EmailStatus.Active ? "" : " (已作废)";
                    string createdTime = email.CreatedDate.ToString("yyyy-MM-dd HH:mm");
                    string displayText = $"{statusIcon} {email.Email} - {createdTime}{statusText}";
                    
                    lstEmails.Items.Add(displayText);
                }
            }
            finally
            {
                lstEmails.EndUpdate();
            }
        }

        /// <summary>
        /// 更新状态栏信息
        /// </summary>
        /// <param name="message">状态消息</param>
        private void UpdateStatus(string message)
        {
            if (statusLabel != null)
            {
                statusLabel.Text = message;
                statusStrip.Refresh();
            }
        }

        /// <summary>
        /// 显示进度指示器
        /// </summary>
        /// <param name="message">状态消息</param>
        private void ShowProgress(string message = null)
        {
            if (progressBar != null)
            {
                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;
                progressBar.MarqueeAnimationSpeed = 30;
            }
            
            if (!string.IsNullOrEmpty(message))
            {
                UpdateStatus(message);
            }
            
            // 设置等待光标
            this.Cursor = Cursors.WaitCursor;
            Application.DoEvents();
        }

        /// <summary>
        /// 隐藏进度指示器
        /// </summary>
        /// <param name="message">状态消息</param>
        private void HideProgress(string message = null)
        {
            if (progressBar != null)
            {
                progressBar.Visible = false;
            }
            
            if (!string.IsNullOrEmpty(message))
            {
                UpdateStatus(message);
            }
            
            // 恢复默认光标
            this.Cursor = Cursors.Default;
        }

        /// <summary>
        /// 保存域名按钮点击事件
        /// </summary>
        private void BtnSaveDomain_Click(object sender, EventArgs e)
        {
            try
            {
                string domain = txtDomain.Text.Trim();
                
                if (string.IsNullOrWhiteSpace(domain))
                {
                    MessageHelper.ShowWarningMessage(ErrorMessages.DOMAIN_EMPTY, "提示", this);
                    txtDomain.Focus();
                    return;
                }

                if (!_emailService.IsValidDomain(domain))
                {
                    MessageHelper.ShowWarningMessage(ErrorMessages.DOMAIN_INVALID_FORMAT, "域名格式错误", this);
                    txtDomain.Focus();
                    return;
                }

                bool success = _configService.SaveDomain(domain);
                if (success)
                {
                    MessageHelper.ShowSuccessMessage(SuccessMessages.DOMAIN_SAVED, "成功", this);
                    UpdateStatus($"域名已保存: {domain}");
                }
                else
                {
                    MessageHelper.ShowErrorMessage(ErrorMessages.DOMAIN_SAVE_FAILED, "保存失败", this);
                    UpdateStatus(ErrorMessages.DOMAIN_SAVE_FAILED);
                }
            }
            catch (ArgumentException ex)
            {
                MessageHelper.ShowWarningMessage(ex.Message, "输入错误", this);
                txtDomain.Focus();
            }
            catch (InvalidOperationException ex)
            {
                MessageHelper.ShowErrorMessage(ex.Message, "操作失败", this);
                UpdateStatus(ErrorMessages.DOMAIN_SAVE_FAILED);
            }
            catch (JsonException ex)
            {
                MessageHelper.ShowErrorMessage(ex.Message, "配置文件错误", this);
                UpdateStatus(ErrorMessages.CONFIG_INVALID);
            }
            catch (Exception ex)
            {
                MessageHelper.ShowExceptionMessage(ex, "保存域名时发生错误", this);
                UpdateStatus(ErrorMessages.DOMAIN_SAVE_FAILED);
            }
        }

        /// <summary>
        /// 生成邮箱按钮点击事件
        /// </summary>
        private async void BtnGenerate_Click(object sender, EventArgs e)
        {
            // 防重复点击
            if (_isOperationInProgress)
                return;

            try
            {
                _isOperationInProgress = true;
                string domain = txtDomain.Text.Trim();
                
                if (string.IsNullOrWhiteSpace(domain))
                {
                    MessageHelper.ShowWarningMessage(ErrorMessages.DOMAIN_EMPTY, "提示", this);
                    txtDomain.Focus();
                    return;
                }

                if (!_emailService.IsValidDomain(domain))
                {
                    MessageHelper.ShowWarningMessage(ErrorMessages.DOMAIN_INVALID_FORMAT, "域名格式错误", this);
                    txtDomain.Focus();
                    return;
                }

                ShowProgress("正在生成邮箱...");
                btnGenerate.Enabled = false;

                var emailRecord = _emailService.CreateEmailRecord(domain);
                if (emailRecord == null)
                {
                    MessageHelper.ShowErrorMessage(ErrorMessages.EMAIL_GENERATION_FAILED, "生成失败", this);
                    HideProgress(ErrorMessages.EMAIL_GENERATION_FAILED);
                    return;
                }

                bool success = await _dataService.AddEmailAsync(emailRecord);
                if (success)
                {
                    _emailHistory.Add(emailRecord);
                    RefreshEmailList();
                    
                    MessageHelper.ShowSuccessMessage($"{SuccessMessages.EMAIL_GENERATED}\n\n{emailRecord.Email}", "生成成功", this);
                    HideProgress($"已生成邮箱: {emailRecord.Email}");
                }
                else
                {
                    MessageHelper.ShowErrorMessage(ErrorMessages.EMAIL_SAVE_FAILED, "保存失败", this);
                    HideProgress(ErrorMessages.EMAIL_SAVE_FAILED);
                }
            }
            catch (ArgumentException ex)
            {
                HideProgress();
                MessageHelper.ShowWarningMessage(ex.Message, "输入错误", this);
                txtDomain.Focus();
            }
            catch (InvalidOperationException ex)
            {
                HideProgress();
                MessageHelper.ShowErrorMessage(ex.Message, "操作失败", this);
                UpdateStatus(ErrorMessages.EMAIL_GENERATION_FAILED);
            }
            catch (JsonException ex)
            {
                HideProgress();
                MessageHelper.ShowErrorMessage(ex.Message, "数据文件错误", this);
                UpdateStatus(ErrorMessages.DATA_SAVE_FAILED);
            }
            catch (Exception ex)
            {
                HideProgress();
                MessageHelper.ShowExceptionMessage(ex, "生成邮箱时发生错误", this);
                UpdateStatus(ErrorMessages.EMAIL_GENERATION_FAILED);
            }
            finally
            {
                btnGenerate.Enabled = true;
                _isOperationInProgress = false;
            }
        }

        /// <summary>
        /// 邮箱列表选择变化事件
        /// </summary>
        private void LstEmails_SelectedIndexChanged(object sender, EventArgs e)
        {
            bool hasSelection = lstEmails.SelectedIndex >= 0 && _emailHistory.Count > 0;
            
            btnCopy.Enabled = hasSelection;
            btnDeactivate.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection;

            if (hasSelection && lstEmails.SelectedItem.ToString() != "暂无邮箱记录")
            {
                var selectedEmail = GetSelectedEmailRecord();
                if (selectedEmail != null)
                {
                    // 如果邮箱已作废，禁用作废按钮
                    btnDeactivate.Enabled = selectedEmail.Status == EmailStatus.Active;
                    UpdateStatus($"已选择: {selectedEmail.Email}");
                }
            }
            else
            {
                UpdateStatus("就绪");
            }
        }

        /// <summary>
        /// 获取当前选中的邮箱记录
        /// </summary>
        /// <returns>选中的邮箱记录，如果没有选中则返回null</returns>
        private EmailRecord GetSelectedEmailRecord()
        {
            if (lstEmails.SelectedIndex < 0 || _emailHistory.Count == 0)
                return null;

            var orderedEmails = _emailHistory.OrderByDescending(e => e.CreatedDate).ToList();
            if (lstEmails.SelectedIndex < orderedEmails.Count)
            {
                return orderedEmails[lstEmails.SelectedIndex];
            }

            return null;
        }

        /// <summary>
        /// 复制按钮点击事件
        /// </summary>
        private void BtnCopy_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedEmail = GetSelectedEmailRecord();
                if (selectedEmail == null)
                {
                    MessageHelper.ShowWarningMessage(ErrorMessages.EMAIL_NOT_SELECTED, "提示", this);
                    return;
                }

                Clipboard.SetText(selectedEmail.Email);
                MessageHelper.ShowSuccessMessage($"{SuccessMessages.EMAIL_COPIED}\n\n{selectedEmail.Email}", "复制成功", this);
                UpdateStatus($"已复制: {selectedEmail.Email}");
            }
            catch (System.Runtime.InteropServices.ExternalException ex)
            {
                MessageHelper.ShowErrorMessage($"无法访问剪贴板: {ex.Message}", "复制失败", this);
                UpdateStatus(ErrorMessages.EMAIL_COPY_FAILED);
            }
            catch (Exception ex)
            {
                MessageHelper.ShowExceptionMessage(ex, ErrorMessages.EMAIL_COPY_FAILED, this);
                UpdateStatus(ErrorMessages.EMAIL_COPY_FAILED);
            }
        }

        /// <summary>
        /// 作废按钮点击事件
        /// </summary>
        private async void BtnDeactivate_Click(object sender, EventArgs e)
        {
            // 防重复点击
            if (_isOperationInProgress)
                return;

            try
            {
                _isOperationInProgress = true;
                var selectedEmail = GetSelectedEmailRecord();
                if (selectedEmail == null)
                {
                    MessageHelper.ShowWarningMessage(ErrorMessages.EMAIL_NOT_SELECTED, "提示", this);
                    return;
                }

                if (selectedEmail.Status == EmailStatus.Deactivated)
                {
                    MessageHelper.ShowInfoMessage(ErrorMessages.EMAIL_ALREADY_DEACTIVATED, "提示", this);
                    return;
                }

                var result = MessageHelper.ShowConfirmationMessage(
                    $"确定要作废邮箱 {selectedEmail.Email} 吗？\n\n作废后邮箱将标记为失效状态，但仍会保留在历史记录中。", 
                    "确认作废", this);

                if (result == DialogResult.Yes)
                {
                    ShowProgress("正在作废邮箱...");
                    btnDeactivate.Enabled = false;

                    bool success = await _dataService.DeactivateEmailAsync(selectedEmail.Email);
                    if (success)
                    {
                        selectedEmail.Status = EmailStatus.Deactivated;
                        selectedEmail.DeactivatedDate = DateTime.Now;
                        RefreshEmailList();
                        
                        MessageHelper.ShowSuccessMessage(SuccessMessages.EMAIL_DEACTIVATED, "作废成功", this);
                        HideProgress($"已作废: {selectedEmail.Email}");
                    }
                    else
                    {
                        MessageHelper.ShowErrorMessage(ErrorMessages.EMAIL_DEACTIVATE_FAILED, "作废失败", this);
                        HideProgress(ErrorMessages.EMAIL_DEACTIVATE_FAILED);
                    }
                }
            }
            catch (ArgumentException ex)
            {
                HideProgress();
                MessageHelper.ShowWarningMessage(ex.Message, "输入错误", this);
            }
            catch (InvalidOperationException ex)
            {
                HideProgress();
                MessageHelper.ShowErrorMessage(ex.Message, "操作失败", this);
                UpdateStatus(ErrorMessages.EMAIL_DEACTIVATE_FAILED);
            }
            catch (JsonException ex)
            {
                HideProgress();
                MessageHelper.ShowErrorMessage(ex.Message, "数据文件错误", this);
                UpdateStatus(ErrorMessages.DATA_SAVE_FAILED);
            }
            catch (Exception ex)
            {
                HideProgress();
                MessageHelper.ShowExceptionMessage(ex, "作废邮箱时发生错误", this);
                UpdateStatus(ErrorMessages.EMAIL_DEACTIVATE_FAILED);
            }
            finally
            {
                btnDeactivate.Enabled = true;
                _isOperationInProgress = false;
            }
        }

        /// <summary>
        /// 删除按钮点击事件
        /// </summary>
        private async void BtnDelete_Click(object sender, EventArgs e)
        {
            // 防重复点击
            if (_isOperationInProgress)
                return;

            try
            {
                _isOperationInProgress = true;
                var selectedEmail = GetSelectedEmailRecord();
                if (selectedEmail == null)
                {
                    MessageHelper.ShowWarningMessage(ErrorMessages.EMAIL_NOT_SELECTED, "提示", this);
                    return;
                }

                var result = MessageHelper.ShowConfirmationMessage(
                    $"确定要删除邮箱 {selectedEmail.Email} 吗？\n\n删除后将无法恢复！", 
                    "确认删除", this);

                if (result == DialogResult.Yes)
                {
                    ShowProgress("正在删除邮箱...");
                    btnDelete.Enabled = false;

                    bool success = await _dataService.DeleteEmailAsync(selectedEmail.Email);
                    if (success)
                    {
                        _emailHistory.Remove(selectedEmail);
                        RefreshEmailList();
                        
                        MessageHelper.ShowSuccessMessage(SuccessMessages.EMAIL_DELETED, "删除成功", this);
                        HideProgress($"已删除: {selectedEmail.Email}");
                    }
                    else
                    {
                        MessageHelper.ShowErrorMessage(ErrorMessages.EMAIL_DELETE_FAILED, "删除失败", this);
                        HideProgress(ErrorMessages.EMAIL_DELETE_FAILED);
                    }
                }
            }
            catch (ArgumentException ex)
            {
                HideProgress();
                MessageHelper.ShowWarningMessage(ex.Message, "输入错误", this);
            }
            catch (InvalidOperationException ex)
            {
                HideProgress();
                MessageHelper.ShowErrorMessage(ex.Message, "操作失败", this);
                UpdateStatus(ErrorMessages.EMAIL_DELETE_FAILED);
            }
            catch (JsonException ex)
            {
                HideProgress();
                MessageHelper.ShowErrorMessage(ex.Message, "数据文件错误", this);
                UpdateStatus(ErrorMessages.DATA_SAVE_FAILED);
            }
            catch (Exception ex)
            {
                HideProgress();
                MessageHelper.ShowExceptionMessage(ex, "删除邮箱时发生错误", this);
                UpdateStatus(ErrorMessages.EMAIL_DELETE_FAILED);
            }
            finally
            {
                btnDelete.Enabled = true;
                _isOperationInProgress = false;
            }
        }

        /// <summary>
        /// 设置菜单项点击事件
        /// </summary>
        private void MenuSettings_Click(object sender, EventArgs e)
        {
            try
            {
                using (var settingsForm = new SettingsForm(_configService, _pathManager))
                {
                    if (settingsForm.ShowDialog(this) == DialogResult.OK)
                    {
                        // 路径配置变更确认对话框
                        var result = MessageHelper.ShowConfirmationMessage(
                            "路径设置已保存。\n\n是否立即应用新的路径设置？\n\n选择\"是\"将重新初始化服务并加载数据，\n选择\"否\"将在下次启动程序时生效。", 
                            "应用路径设置", this);

                        if (result == DialogResult.Yes)
                        {
                            // 立即应用新设置
                            if (ApplyPathSettings())
                            {
                                MessageHelper.ShowSuccessMessage("路径设置已成功应用！", "设置已更新", this);
                                UpdatePathStatusDisplay();
                            }
                            else
                            {
                                MessageHelper.ShowWarningMessage("应用路径设置时遇到问题，程序将继续使用当前设置。", "设置应用失败", this);
                            }
                        }
                        else
                        {
                            MessageHelper.ShowInfoMessage("路径设置已保存，将在下次启动程序时生效。", "设置已保存", this);
                        }
                        
                        UpdateStatus("路径设置已更新");
                    }
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowExceptionMessage(ex, "打开设置窗体时发生错误", this);
                UpdateStatus("打开设置失败");
            }
        }

        /// <summary>
        /// 退出菜单项点击事件
        /// </summary>
        private void MenuExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 应用路径设置
        /// </summary>
        /// <returns>应用是否成功</returns>
        private bool ApplyPathSettings()
        {
            try
            {
                ShowProgress("正在应用路径设置...");

                // 备份当前服务状态
                var backupEmailHistory = new List<EmailRecord>(_emailHistory);
                var backupConfigService = _configService;
                var backupDataService = _dataService;

                try
                {
                    // 重新初始化服务，使用新的路径配置
                    var newConfigService = new ConfigurationService(_pathManager);
                    var newDataService = new DataService(newConfigService, _pathManager);
                    var newEmailService = new EmailService(newDataService);

                    // 测试新服务是否能正常工作
                    string testDomain = newConfigService.LoadDomain();
                    var testEmails = newDataService.LoadEmails();

                    // 如果测试成功，替换旧服务
                    _configService = newConfigService;
                    _dataService = newDataService;
                    _emailService = newEmailService;

                    // 重新加载配置和数据
                    LoadConfiguration();
                    LoadEmailHistory();

                    HideProgress("路径设置应用成功");
                    return true;
                }
                catch (Exception ex)
                {
                    // 路径变更失败时的错误恢复
                    HideProgress();
                    
                    try
                    {
                        // 恢复原始服务状态
                        _configService = backupConfigService;
                        _dataService = backupDataService;
                        _emailService = new EmailService(_dataService);
                        _emailHistory = backupEmailHistory;
                        RefreshEmailList();

                        MessageHelper.ShowErrorMessage(
                            $"应用新路径设置失败，已恢复到原始设置。\n\n错误详情：{ex.Message}", 
                            "路径设置失败", this);
                    }
                    catch (Exception rollbackEx)
                    {
                        MessageHelper.ShowErrorMessage(
                            $"应用新路径设置失败，且无法恢复原始设置。\n\n建议重启程序。\n\n错误详情：{rollbackEx.Message}", 
                            "严重错误", this);
                    }

                    UpdateStatus("路径设置应用失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                HideProgress();
                MessageHelper.ShowExceptionMessage(ex, "应用路径设置时发生错误", this);
                UpdateStatus("路径设置应用失败");
                return false;
            }
        }

        /// <summary>
        /// 更新状态栏显示当前使用的路径信息
        /// </summary>
        private void UpdatePathStatusDisplay()
        {
            try
            {
                string configPath = _configService.GetCurrentConfigPath();
                string dataPath = _configService.GetCurrentDataPath();
                
                // 获取路径的简短显示名称
                string configDisplayName = GetPathDisplayName(configPath);
                string dataDisplayName = GetPathDisplayName(dataPath);
                
                string pathInfo = $"配置: {configDisplayName} | 数据: {dataDisplayName}";
                UpdateStatus(pathInfo);

                // 设置工具提示显示完整路径
                statusLabel.ToolTipText = $"配置文件: {configPath}\n数据文件: {dataPath}";
            }
            catch (Exception ex)
            {
                UpdateStatus("无法获取路径信息");
            }
        }

        /// <summary>
        /// 获取路径的简短显示名称
        /// </summary>
        /// <param name="fullPath">完整路径</param>
        /// <returns>简短显示名称</returns>
        private string GetPathDisplayName(string fullPath)
        {
            try
            {
                if (string.IsNullOrEmpty(fullPath))
                    return "未知";

                string exeDirectory = _pathManager.GetExecutableDirectory();
                
                // 如果是默认路径（exe目录），显示"默认"
                if (fullPath.StartsWith(exeDirectory, StringComparison.OrdinalIgnoreCase))
                {
                    return "默认路径";
                }
                
                // 否则显示目录名
                string directory = Path.GetDirectoryName(fullPath);
                if (!string.IsNullOrEmpty(directory))
                {
                    return Path.GetFileName(directory);
                }
                
                return "自定义路径";
            }
            catch
            {
                return "未知";
            }
        }
    }
}