using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using EmailGenerator.Models;
using EmailGenerator.Services;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// 邮箱删除功能的集成测试
    /// </summary>
    [TestClass]
    public class EmailDeletionIntegrationTests
    {
        private DataService _dataService;
        private EmailService _emailService;
        private string _testDataPath;

        [TestInitialize]
        public void Setup()
        {
            // 设置测试文件路径
            _testDataPath = Path.Combine(Path.GetTempPath(), "test_deletion_emails.json");
            
            // 初始化服务
            _dataService = new DataService(_testDataPath);
            _emailService = new EmailService(_dataService);
            
            // 清理测试环境
            CleanupTestFiles();
        }

        [TestCleanup]
        public void Cleanup()
        {
            CleanupTestFiles();
        }

        private void CleanupTestFiles()
        {
            if (File.Exists(_testDataPath))
            {
                File.Delete(_testDataPath);
            }
        }

        [TestMethod]
        public void DeleteEmail_CompleteWorkflow_Success()
        {
            // Arrange - 创建测试邮箱记录
            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                Domain = "example.com",
                Username = "test",
                CreatedDate = DateTime.Now.AddDays(-1),
                Status = EmailStatus.Active
            };

            // 添加邮箱到数据存储
            bool addResult = _dataService.AddEmail(testEmail);
            Assert.IsTrue(addResult, "添加测试邮箱失败");

            // 验证邮箱已添加
            var loadedEmails = _dataService.LoadEmails();
            Assert.AreEqual(1, loadedEmails.Count, "邮箱数量不正确");

            // Act - 执行删除操作
            bool deleteResult = _dataService.DeleteEmail("<EMAIL>");

            // Assert - 验证删除结果
            Assert.IsTrue(deleteResult, "删除操作失败");

            // 重新加载数据验证持久化
            var updatedEmails = _dataService.LoadEmails();
            Assert.AreEqual(0, updatedEmails.Count, "删除后邮箱应该不存在");
        }

        [TestMethod]
        public void DeleteEmail_NonExistentEmail_ReturnsFalse()
        {
            // Act - 尝试删除不存在的邮箱
            bool result = _dataService.DeleteEmail("<EMAIL>");

            // Assert
            Assert.IsFalse(result, "删除不存在的邮箱应该返回false");
        }

        [TestMethod]
        public void DeleteEmail_MultipleEmails_OnlyTargetDeleted()
        {
            // Arrange - 创建多个邮箱记录
            var emails = new List<EmailRecord>
            {
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Domain = "example.com",
                    Username = "email1",
                    CreatedDate = DateTime.Now.AddDays(-3),
                    Status = EmailStatus.Active
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Domain = "example.com",
                    Username = "email2",
                    CreatedDate = DateTime.Now.AddDays(-2),
                    Status = EmailStatus.Active
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Domain = "example.com",
                    Username = "email3",
                    CreatedDate = DateTime.Now.AddDays(-1),
                    Status = EmailStatus.Active
                }
            };

            foreach (var email in emails)
            {
                _dataService.AddEmail(email);
            }

            // Act - 只删除中间的邮箱
            bool result = _dataService.DeleteEmail("<EMAIL>");

            // Assert
            Assert.IsTrue(result, "删除操作失败");

            var updatedEmails = _dataService.LoadEmails();
            Assert.AreEqual(2, updatedEmails.Count, "删除后应该剩余2个邮箱");

            var email1 = updatedEmails.FirstOrDefault(e => e.Email == "<EMAIL>");
            var email2 = updatedEmails.FirstOrDefault(e => e.Email == "<EMAIL>");
            var email3 = updatedEmails.FirstOrDefault(e => e.Email == "<EMAIL>");

            Assert.IsNotNull(email1, "email1应该存在");
            Assert.IsNull(email2, "email2应该被删除");
            Assert.IsNotNull(email3, "email3应该存在");
        }

        [TestMethod]
        public void DeleteEmail_DeactivatedEmail_Success()
        {
            // Arrange - 创建已作废的邮箱记录
            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                Domain = "example.com",
                Username = "deactivated",
                CreatedDate = DateTime.Now.AddDays(-2),
                Status = EmailStatus.Deactivated,
                DeactivatedDate = DateTime.Now.AddDays(-1)
            };

            _dataService.AddEmail(testEmail);

            // 验证邮箱已添加
            var loadedEmails = _dataService.LoadEmails();
            Assert.AreEqual(1, loadedEmails.Count, "邮箱数量不正确");
            Assert.AreEqual(EmailStatus.Deactivated, loadedEmails[0].Status, "邮箱状态不正确");

            // Act - 删除已作废的邮箱
            bool result = _dataService.DeleteEmail("<EMAIL>");

            // Assert
            Assert.IsTrue(result, "删除已作废邮箱应该成功");
            
            var updatedEmails = _dataService.LoadEmails();
            Assert.AreEqual(0, updatedEmails.Count, "删除后邮箱应该不存在");
        }

        [TestMethod]
        public void DeleteEmail_DataPersistence_SurvivesReload()
        {
            // Arrange - 创建多个测试邮箱
            var emails = new List<EmailRecord>
            {
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Domain = "example.com",
                    Username = "keep",
                    CreatedDate = DateTime.Now.AddDays(-2),
                    Status = EmailStatus.Active
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Domain = "example.com",
                    Username = "delete",
                    CreatedDate = DateTime.Now.AddDays(-1),
                    Status = EmailStatus.Active
                }
            };

            foreach (var email in emails)
            {
                _dataService.AddEmail(email);
            }

            // Act - 删除其中一个邮箱
            bool deleteResult = _dataService.DeleteEmail("<EMAIL>");
            Assert.IsTrue(deleteResult, "删除操作失败");

            // 创建新的DataService实例来模拟程序重启
            var newDataService = new DataService(_testDataPath);

            // Assert - 验证数据持久化
            var reloadedEmails = newDataService.LoadEmails();
            Assert.AreEqual(1, reloadedEmails.Count, "重新加载后应该只有1个邮箱");
            
            var remainingEmail = reloadedEmails[0];
            Assert.AreEqual("<EMAIL>", remainingEmail.Email, "剩余的邮箱地址不正确");
            Assert.AreEqual(EmailStatus.Active, remainingEmail.Status, "剩余邮箱状态不正确");
        }

        [TestMethod]
        public void DeleteEmail_CaseInsensitive_Success()
        {
            // Arrange - 创建测试邮箱
            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                Domain = "Example.Com",
                Username = "CaseTest",
                CreatedDate = DateTime.Now.AddDays(-1),
                Status = EmailStatus.Active
            };

            _dataService.AddEmail(testEmail);

            // Act - 使用不同大小写删除邮箱
            bool result = _dataService.DeleteEmail("<EMAIL>");

            // Assert
            Assert.IsTrue(result, "大小写不敏感的删除操作应该成功");
            
            var emails = _dataService.LoadEmails();
            Assert.AreEqual(0, emails.Count, "邮箱应该被删除");
        }

        [TestMethod]
        public void DeleteEmail_EmptyOrNullEmail_ReturnsFalse()
        {
            // Act & Assert - 测试空字符串
            bool result1 = _dataService.DeleteEmail("");
            Assert.IsFalse(result1, "空字符串应该返回false");

            // Act & Assert - 测试null
            bool result2 = _dataService.DeleteEmail(null);
            Assert.IsFalse(result2, "null应该返回false");

            // Act & Assert - 测试空白字符串
            bool result3 = _dataService.DeleteEmail("   ");
            Assert.IsFalse(result3, "空白字符串应该返回false");
        }

        [TestMethod]
        public void DeleteEmail_GetActiveEmails_UpdatesCorrectly()
        {
            // Arrange - 创建测试邮箱
            var emails = new List<EmailRecord>
            {
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Domain = "example.com",
                    Username = "active1",
                    CreatedDate = DateTime.Now.AddDays(-3),
                    Status = EmailStatus.Active
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Domain = "example.com",
                    Username = "active2",
                    CreatedDate = DateTime.Now.AddDays(-2),
                    Status = EmailStatus.Active
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Domain = "example.com",
                    Username = "deactivated",
                    CreatedDate = DateTime.Now.AddDays(-1),
                    Status = EmailStatus.Deactivated,
                    DeactivatedDate = DateTime.Now
                }
            };

            foreach (var email in emails)
            {
                _dataService.AddEmail(email);
            }

            // 验证初始状态
            var initialActiveEmails = _dataService.GetActiveEmails();
            Assert.AreEqual(2, initialActiveEmails.Count, "初始应该有2个活跃邮箱");

            // Act - 删除一个活跃邮箱
            bool deleteResult = _dataService.DeleteEmail("<EMAIL>");
            Assert.IsTrue(deleteResult, "删除操作失败");

            // Assert - 验证活跃邮箱列表更新
            var finalActiveEmails = _dataService.GetActiveEmails();
            Assert.AreEqual(1, finalActiveEmails.Count, "删除后应该只有1个活跃邮箱");
            Assert.IsTrue(finalActiveEmails.Contains("<EMAIL>"), "应该包含未删除的活跃邮箱");
            Assert.IsFalse(finalActiveEmails.Contains("<EMAIL>"), "不应该包含已删除的邮箱");
            Assert.IsFalse(finalActiveEmails.Contains("<EMAIL>"), "不应该包含已作废的邮箱");
        }

        [TestMethod]
        public void DeleteEmail_AllEmails_EmptyList()
        {
            // Arrange - 创建测试邮箱
            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                Domain = "example.com",
                Username = "onlyemail",
                CreatedDate = DateTime.Now.AddDays(-1),
                Status = EmailStatus.Active
            };

            _dataService.AddEmail(testEmail);

            // 验证邮箱已添加
            var loadedEmails = _dataService.LoadEmails();
            Assert.AreEqual(1, loadedEmails.Count, "应该有1个邮箱");

            // Act - 删除唯一的邮箱
            bool result = _dataService.DeleteEmail("<EMAIL>");

            // Assert
            Assert.IsTrue(result, "删除操作应该成功");
            
            var finalEmails = _dataService.LoadEmails();
            Assert.AreEqual(0, finalEmails.Count, "删除后应该没有邮箱");

            var activeEmails = _dataService.GetActiveEmails();
            Assert.AreEqual(0, activeEmails.Count, "活跃邮箱列表应该为空");
        }

        [TestMethod]
        public void DeleteEmail_EmailExists_BeforeAndAfterDeletion()
        {
            // Arrange - 创建测试邮箱
            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                Domain = "example.com",
                Username = "exists",
                CreatedDate = DateTime.Now.AddDays(-1),
                Status = EmailStatus.Active
            };

            _dataService.AddEmail(testEmail);

            // 验证邮箱存在
            bool existsBefore = _dataService.EmailExists("<EMAIL>");
            Assert.IsTrue(existsBefore, "删除前邮箱应该存在");

            // Act - 删除邮箱
            bool deleteResult = _dataService.DeleteEmail("<EMAIL>");
            Assert.IsTrue(deleteResult, "删除操作失败");

            // Assert - 验证邮箱不再存在
            bool existsAfter = _dataService.EmailExists("<EMAIL>");
            Assert.IsFalse(existsAfter, "删除后邮箱不应该存在");
        }
    }
}