using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using EmailGenerator.Models;
using EmailGenerator.Services;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// 邮箱作废功能的集成测试
    /// </summary>
    [TestClass]
    public class EmailDeactivationIntegrationTests
    {
        private DataService _dataService;
        private EmailService _emailService;
        private string _testDataPath;

        [TestInitialize]
        public void Setup()
        {
            // 设置测试文件路径
            _testDataPath = Path.Combine(Path.GetTempPath(), "test_deactivation_emails.json");
            
            // 初始化服务
            _dataService = new DataService(_testDataPath);
            _emailService = new EmailService(_dataService);
            
            // 清理测试环境
            CleanupTestFiles();
        }

        [TestCleanup]
        public void Cleanup()
        {
            CleanupTestFiles();
        }

        private void CleanupTestFiles()
        {
            if (File.Exists(_testDataPath))
            {
                File.Delete(_testDataPath);
            }
        }

        [TestMethod]
        public void DeactivateEmail_CompleteWorkflow_Success()
        {
            // Arrange - 创建测试邮箱记录
            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                Domain = "example.com",
                Username = "test",
                CreatedDate = DateTime.Now.AddDays(-1),
                Status = EmailStatus.Active
            };

            // 添加邮箱到数据存储
            bool addResult = _dataService.AddEmail(testEmail);
            Assert.IsTrue(addResult, "添加测试邮箱失败");

            // 验证邮箱已添加且状态为Active
            var loadedEmails = _dataService.LoadEmails();
            Assert.AreEqual(1, loadedEmails.Count, "邮箱数量不正确");
            Assert.AreEqual(EmailStatus.Active, loadedEmails[0].Status, "初始状态不正确");

            // Act - 执行作废操作
            bool deactivateResult = _dataService.DeactivateEmail("<EMAIL>");

            // Assert - 验证作废结果
            Assert.IsTrue(deactivateResult, "作废操作失败");

            // 重新加载数据验证持久化
            var updatedEmails = _dataService.LoadEmails();
            Assert.AreEqual(1, updatedEmails.Count, "作废后邮箱数量不正确");
            
            var deactivatedEmail = updatedEmails[0];
            Assert.AreEqual(EmailStatus.Deactivated, deactivatedEmail.Status, "邮箱状态未更新为Deactivated");
            Assert.IsNotNull(deactivatedEmail.DeactivatedDate, "作废日期未设置");
            Assert.IsTrue(deactivatedEmail.DeactivatedDate.Value.Date == DateTime.Now.Date, "作废日期不正确");
        }

        [TestMethod]
        public void DeactivateEmail_AlreadyDeactivated_ReturnsTrue()
        {
            // Arrange - 创建已作废的邮箱记录
            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                Domain = "example.com",
                Username = "already",
                CreatedDate = DateTime.Now.AddDays(-2),
                Status = EmailStatus.Deactivated,
                DeactivatedDate = DateTime.Now.AddDays(-1)
            };

            _dataService.AddEmail(testEmail);

            // Act - 尝试再次作废
            bool result = _dataService.DeactivateEmail("<EMAIL>");

            // Assert - 应该成功（幂等操作）
            Assert.IsTrue(result, "重复作废操作应该成功");
            
            var emails = _dataService.LoadEmails();
            Assert.AreEqual(EmailStatus.Deactivated, emails[0].Status, "状态应该保持为Deactivated");
        }

        [TestMethod]
        public void DeactivateEmail_NonExistentEmail_ReturnsFalse()
        {
            // Act - 尝试作废不存在的邮箱
            bool result = _dataService.DeactivateEmail("<EMAIL>");

            // Assert
            Assert.IsFalse(result, "作废不存在的邮箱应该返回false");
        }

        [TestMethod]
        public void DeactivateEmail_MultipleEmails_OnlyTargetDeactivated()
        {
            // Arrange - 创建多个邮箱记录
            var emails = new List<EmailRecord>
            {
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Domain = "example.com",
                    Username = "email1",
                    CreatedDate = DateTime.Now.AddDays(-3),
                    Status = EmailStatus.Active
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Domain = "example.com",
                    Username = "email2",
                    CreatedDate = DateTime.Now.AddDays(-2),
                    Status = EmailStatus.Active
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Domain = "example.com",
                    Username = "email3",
                    CreatedDate = DateTime.Now.AddDays(-1),
                    Status = EmailStatus.Active
                }
            };

            foreach (var email in emails)
            {
                _dataService.AddEmail(email);
            }

            // Act - 只作废中间的邮箱
            bool result = _dataService.DeactivateEmail("<EMAIL>");

            // Assert
            Assert.IsTrue(result, "作废操作失败");

            var updatedEmails = _dataService.LoadEmails();
            Assert.AreEqual(3, updatedEmails.Count, "邮箱总数不应该改变");

            var email1 = updatedEmails.FirstOrDefault(e => e.Email == "<EMAIL>");
            var email2 = updatedEmails.FirstOrDefault(e => e.Email == "<EMAIL>");
            var email3 = updatedEmails.FirstOrDefault(e => e.Email == "<EMAIL>");

            Assert.IsNotNull(email1, "email1应该存在");
            Assert.IsNotNull(email2, "email2应该存在");
            Assert.IsNotNull(email3, "email3应该存在");

            Assert.AreEqual(EmailStatus.Active, email1.Status, "email1应该保持Active状态");
            Assert.AreEqual(EmailStatus.Deactivated, email2.Status, "email2应该是Deactivated状态");
            Assert.AreEqual(EmailStatus.Active, email3.Status, "email3应该保持Active状态");

            Assert.IsNull(email1.DeactivatedDate, "email1不应该有作废日期");
            Assert.IsNotNull(email2.DeactivatedDate, "email2应该有作废日期");
            Assert.IsNull(email3.DeactivatedDate, "email3不应该有作废日期");
        }

        [TestMethod]
        public void GetActiveEmails_AfterDeactivation_ExcludesDeactivated()
        {
            // Arrange - 创建测试邮箱
            var emails = new List<EmailRecord>
            {
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Domain = "example.com",
                    Username = "active",
                    CreatedDate = DateTime.Now.AddDays(-2),
                    Status = EmailStatus.Active
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Domain = "example.com",
                    Username = "todeactivate",
                    CreatedDate = DateTime.Now.AddDays(-1),
                    Status = EmailStatus.Active
                }
            };

            foreach (var email in emails)
            {
                _dataService.AddEmail(email);
            }

            // 验证初始状态
            var initialActiveEmails = _dataService.GetActiveEmails();
            Assert.AreEqual(2, initialActiveEmails.Count, "初始应该有2个活跃邮箱");

            // Act - 作废其中一个邮箱
            bool deactivateResult = _dataService.DeactivateEmail("<EMAIL>");
            Assert.IsTrue(deactivateResult, "作废操作失败");

            // Assert - 验证活跃邮箱列表
            var finalActiveEmails = _dataService.GetActiveEmails();
            Assert.AreEqual(1, finalActiveEmails.Count, "作废后应该只有1个活跃邮箱");
            Assert.IsTrue(finalActiveEmails.Contains("<EMAIL>"), "应该包含未作废的邮箱");
            Assert.IsFalse(finalActiveEmails.Contains("<EMAIL>"), "不应该包含已作废的邮箱");
        }

        [TestMethod]
        public void DeactivateEmail_DataPersistence_SurvivesReload()
        {
            // Arrange - 创建测试邮箱
            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                Domain = "example.com",
                Username = "persistence",
                CreatedDate = DateTime.Now.AddDays(-1),
                Status = EmailStatus.Active
            };

            _dataService.AddEmail(testEmail);

            // Act - 作废邮箱
            bool deactivateResult = _dataService.DeactivateEmail("<EMAIL>");
            Assert.IsTrue(deactivateResult, "作废操作失败");

            // 创建新的DataService实例来模拟程序重启
            var newDataService = new DataService(_testDataPath);

            // Assert - 验证数据持久化
            var reloadedEmails = newDataService.LoadEmails();
            Assert.AreEqual(1, reloadedEmails.Count, "重新加载后邮箱数量不正确");
            
            var reloadedEmail = reloadedEmails[0];
            Assert.AreEqual("<EMAIL>", reloadedEmail.Email, "邮箱地址不匹配");
            Assert.AreEqual(EmailStatus.Deactivated, reloadedEmail.Status, "重新加载后状态不正确");
            Assert.IsNotNull(reloadedEmail.DeactivatedDate, "重新加载后作废日期丢失");
        }

        [TestMethod]
        public void DeactivateEmail_CaseInsensitive_Success()
        {
            // Arrange - 创建测试邮箱
            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                Domain = "Example.Com",
                Username = "CaseTest",
                CreatedDate = DateTime.Now.AddDays(-1),
                Status = EmailStatus.Active
            };

            _dataService.AddEmail(testEmail);

            // Act - 使用不同大小写作废邮箱
            bool result = _dataService.DeactivateEmail("<EMAIL>");

            // Assert
            Assert.IsTrue(result, "大小写不敏感的作废操作应该成功");
            
            var emails = _dataService.LoadEmails();
            Assert.AreEqual(EmailStatus.Deactivated, emails[0].Status, "邮箱应该被作废");
        }

        [TestMethod]
        public void DeactivateEmail_EmptyOrNullEmail_ReturnsFalse()
        {
            // Act & Assert - 测试空字符串
            bool result1 = _dataService.DeactivateEmail("");
            Assert.IsFalse(result1, "空字符串应该返回false");

            // Act & Assert - 测试null
            bool result2 = _dataService.DeactivateEmail(null);
            Assert.IsFalse(result2, "null应该返回false");

            // Act & Assert - 测试空白字符串
            bool result3 = _dataService.DeactivateEmail("   ");
            Assert.IsFalse(result3, "空白字符串应该返回false");
        }
    }
}