using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using EmailGenerator.Models;
using Newtonsoft.Json;

namespace EmailGenerator.Services
{
    /// <summary>
    /// 数据迁移服务，负责处理数据和配置文件的迁移、备份和恢复
    /// </summary>
    public class DataMigrationService
    {
        private readonly PathManagerService _pathManager;
        private readonly ConfigurationService _configService;
        private readonly DataService _dataService;

        /// <summary>
        /// 迁移进度回调委托
        /// </summary>
        /// <param name="progress">进度百分比 (0-100)</param>
        /// <param name="message">进度消息</param>
        public delegate void MigrationProgressCallback(int progress, string message);

        /// <summary>
        /// 迁移结果
        /// </summary>
        public class MigrationResult
        {
            public bool Success { get; set; }
            public string ErrorMessage { get; set; }
            public List<string> BackupFiles { get; set; } = new List<string>();
            public string OldConfigPath { get; set; }
            public string NewConfigPath { get; set; }
            public string OldDataPath { get; set; }
            public string NewDataPath { get; set; }
        }

        public DataMigrationService(PathManagerService pathManager, ConfigurationService configService, DataService dataService)
        {
            _pathManager = pathManager ?? throw new ArgumentNullException(nameof(pathManager));
            _configService = configService ?? throw new ArgumentNullException(nameof(configService));
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
        }

        /// <summary>
        /// 执行完整的数据迁移流程
        /// </summary>
        /// <param name="newConfigPath">新的配置文件路径</param>
        /// <param name="newDataPath">新的数据文件路径</param>
        /// <param name="progressCallback">进度回调</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>迁移结果</returns>
        public async Task<MigrationResult> MigrateDataAsync(string newConfigPath, string newDataPath, MigrationProgressCallback progressCallback = null, CancellationToken cancellationToken = default)
        {
            var result = new MigrationResult();
            
            try
            {
                // 检查取消请求
                cancellationToken.ThrowIfCancellationRequested();

                // 获取当前路径
                result.OldConfigPath = _configService.GetCurrentConfigPath();
                result.OldDataPath = _configService.GetCurrentDataPath();
                result.NewConfigPath = newConfigPath;
                result.NewDataPath = newDataPath;

                progressCallback?.Invoke(0, "开始数据迁移...");

                // 验证新路径
                if (!ValidateMigrationPaths(newConfigPath, newDataPath))
                {
                    result.ErrorMessage = "新路径验证失败";
                    return result;
                }

                cancellationToken.ThrowIfCancellationRequested();
                progressCallback?.Invoke(10, "验证路径完成");

                // 创建备份
                var backupResult = await CreateBackupsAsync(result.OldConfigPath, result.OldDataPath, cancellationToken);
                if (!backupResult.Success)
                {
                    result.ErrorMessage = $"创建备份失败: {backupResult.ErrorMessage}";
                    return result;
                }
                result.BackupFiles.AddRange(backupResult.BackupFiles);

                cancellationToken.ThrowIfCancellationRequested();
                progressCallback?.Invoke(30, "创建备份完成");

                // 执行迁移
                var migrationSuccess = await PerformMigrationAsync(result, progressCallback, cancellationToken);
                if (!migrationSuccess)
                {
                    // 迁移失败，执行回滚
                    progressCallback?.Invoke(80, "迁移失败，正在回滚...");
                    await RollbackMigrationAsync(result, cancellationToken);
                    result.ErrorMessage = "数据迁移失败，已回滚到原路径";
                    return result;
                }

                cancellationToken.ThrowIfCancellationRequested();
                progressCallback?.Invoke(90, "迁移完成，清理备份文件...");

                // 清理备份文件
                CleanupBackupFiles(result.BackupFiles);

                progressCallback?.Invoke(100, "数据迁移成功完成");
                result.Success = true;
                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"迁移过程中发生异常: {ex.Message}";
                
                // 尝试回滚
                try
                {
                    await RollbackMigrationAsync(result);
                }
                catch (Exception rollbackEx)
                {
                    result.ErrorMessage += $"; 回滚失败: {rollbackEx.Message}";
                }
                
                return result;
            }
        }

        /// <summary>
        /// 验证迁移路径的有效性
        /// </summary>
        /// <param name="configPath">配置文件路径</param>
        /// <param name="dataPath">数据文件路径</param>
        /// <returns>验证是否通过</returns>
        private bool ValidateMigrationPaths(string configPath, string dataPath)
        {
            try
            {
                // 验证配置文件路径
                if (!string.IsNullOrEmpty(configPath))
                {
                    string configDir = Path.GetDirectoryName(configPath);
                    if (!_pathManager.IsValidPath(configDir) || !_pathManager.IsWritablePath(configDir))
                    {
                        return false;
                    }
                }

                // 验证数据文件路径
                if (!string.IsNullOrEmpty(dataPath))
                {
                    string dataDir = Path.GetDirectoryName(dataPath);
                    if (!_pathManager.IsValidPath(dataDir) || !_pathManager.IsWritablePath(dataDir))
                    {
                        return false;
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 创建数据和配置文件的备份
        /// </summary>
        /// <param name="configPath">配置文件路径</param>
        /// <param name="dataPath">数据文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>备份结果</returns>
        private async Task<MigrationResult> CreateBackupsAsync(string configPath, string dataPath, CancellationToken cancellationToken = default)
        {
            var result = new MigrationResult();
            
            try
            {
                string timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");

                // 备份配置文件
                if (File.Exists(configPath))
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    string configBackup = configPath + $".backup.{timestamp}";
                    await Task.Run(() => File.Copy(configPath, configBackup, true), cancellationToken);
                    result.BackupFiles.Add(configBackup);
                }

                // 备份数据文件
                if (File.Exists(dataPath))
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    string dataBackup = dataPath + $".backup.{timestamp}";
                    await Task.Run(() => File.Copy(dataPath, dataBackup, true), cancellationToken);
                    result.BackupFiles.Add(dataBackup);
                }

                result.Success = true;
                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        /// <summary>
        /// 执行实际的迁移操作
        /// </summary>
        /// <param name="migrationResult">迁移结果对象</param>
        /// <param name="progressCallback">进度回调</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>迁移是否成功</returns>
        private async Task<bool> PerformMigrationAsync(MigrationResult migrationResult, MigrationProgressCallback progressCallback, CancellationToken cancellationToken = default)
        {
            try
            {
                // 创建目标目录
                if (!string.IsNullOrEmpty(migrationResult.NewConfigPath))
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    string configDir = Path.GetDirectoryName(migrationResult.NewConfigPath);
                    if (!_pathManager.CreateDirectoryIfNotExists(configDir))
                    {
                        return false;
                    }
                }

                if (!string.IsNullOrEmpty(migrationResult.NewDataPath))
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    string dataDir = Path.GetDirectoryName(migrationResult.NewDataPath);
                    if (!_pathManager.CreateDirectoryIfNotExists(dataDir))
                    {
                        return false;
                    }
                }

                progressCallback?.Invoke(40, "创建目标目录完成");

                // 迁移配置文件
                if (File.Exists(migrationResult.OldConfigPath) && !string.IsNullOrEmpty(migrationResult.NewConfigPath))
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    await Task.Run(() => File.Copy(migrationResult.OldConfigPath, migrationResult.NewConfigPath, true), cancellationToken);
                }

                progressCallback?.Invoke(60, "迁移配置文件完成");

                // 迁移数据文件
                if (File.Exists(migrationResult.OldDataPath) && !string.IsNullOrEmpty(migrationResult.NewDataPath))
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    await Task.Run(() => File.Copy(migrationResult.OldDataPath, migrationResult.NewDataPath, true), cancellationToken);
                }

                progressCallback?.Invoke(70, "迁移数据文件完成");

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"迁移执行失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 回滚迁移操作
        /// </summary>
        /// <param name="migrationResult">迁移结果对象</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>回滚任务</returns>
        private async Task RollbackMigrationAsync(MigrationResult migrationResult, CancellationToken cancellationToken = default)
        {
            try
            {
                // 从备份文件恢复
                foreach (string backupFile in migrationResult.BackupFiles)
                {
                    if (File.Exists(backupFile))
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        string originalFile = backupFile.Substring(0, backupFile.LastIndexOf(".backup."));
                        await Task.Run(() => File.Copy(backupFile, originalFile, true), cancellationToken);
                    }
                }

                // 删除可能已创建的新文件
                if (!string.IsNullOrEmpty(migrationResult.NewConfigPath) && File.Exists(migrationResult.NewConfigPath))
                {
                    try
                    {
                        File.Delete(migrationResult.NewConfigPath);
                    }
                    catch
                    {
                        // 删除失败不影响回滚结果
                    }
                }

                if (!string.IsNullOrEmpty(migrationResult.NewDataPath) && File.Exists(migrationResult.NewDataPath))
                {
                    try
                    {
                        File.Delete(migrationResult.NewDataPath);
                    }
                    catch
                    {
                        // 删除失败不影响回滚结果
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"回滚操作失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 清理备份文件
        /// </summary>
        /// <param name="backupFiles">备份文件列表</param>
        private void CleanupBackupFiles(List<string> backupFiles)
        {
            foreach (string backupFile in backupFiles)
            {
                try
                {
                    if (File.Exists(backupFile))
                    {
                        File.Delete(backupFile);
                    }
                }
                catch
                {
                    // 清理失败不影响整体结果
                    System.Diagnostics.Debug.WriteLine($"清理备份文件失败: {backupFile}");
                }
            }
        }

        /// <summary>
        /// 显示迁移确认对话框
        /// </summary>
        /// <param name="oldConfigPath">原配置文件路径</param>
        /// <param name="newConfigPath">新配置文件路径</param>
        /// <param name="oldDataPath">原数据文件路径</param>
        /// <param name="newDataPath">新数据文件路径</param>
        /// <returns>用户是否确认迁移</returns>
        public bool ShowMigrationConfirmation(string oldConfigPath, string newConfigPath, string oldDataPath, string newDataPath)
        {
            string message = "确认要执行数据迁移吗？\n\n";
            message += "迁移详情：\n";
            message += $"配置文件：\n  从: {oldConfigPath}\n  到: {newConfigPath}\n\n";
            message += $"数据文件：\n  从: {oldDataPath}\n  到: {newDataPath}\n\n";
            message += "注意：迁移过程中会自动创建备份文件，如果迁移失败将自动回滚。";

            DialogResult result = MessageBox.Show(
                message,
                "确认数据迁移",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2
            );

            return result == DialogResult.Yes;
        }

        /// <summary>
        /// 显示迁移进度对话框
        /// </summary>
        /// <param name="migrationTask">迁移任务</param>
        /// <returns>迁移结果</returns>
        public async Task<MigrationResult> ShowMigrationProgressDialog(Task<MigrationResult> migrationTask)
        {
            using (var progressForm = new Form())
            {
                progressForm.Text = "数据迁移进度";
                progressForm.Size = new System.Drawing.Size(400, 150);
                progressForm.StartPosition = FormStartPosition.CenterParent;
                progressForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                progressForm.MaximizeBox = false;
                progressForm.MinimizeBox = false;

                var progressBar = new ProgressBar
                {
                    Location = new System.Drawing.Point(20, 20),
                    Size = new System.Drawing.Size(340, 23),
                    Style = ProgressBarStyle.Continuous
                };

                var statusLabel = new Label
                {
                    Location = new System.Drawing.Point(20, 50),
                    Size = new System.Drawing.Size(340, 20),
                    Text = "准备开始迁移..."
                };

                var cancelButton = new Button
                {
                    Location = new System.Drawing.Point(160, 80),
                    Size = new System.Drawing.Size(75, 23),
                    Text = "取消",
                    DialogResult = DialogResult.Cancel
                };

                progressForm.Controls.AddRange(new Control[] { progressBar, statusLabel, cancelButton });

                bool migrationCompleted = false;
                MigrationResult result = null;

                // 启动迁移任务
                var migrationWithProgress = Task.Run(async () =>
                {
                    try
                    {
                        result = await migrationTask;
                        migrationCompleted = true;
                        
                        // 在UI线程上更新界面
                        progressForm.Invoke(new Action(() =>
                        {
                            progressBar.Value = 100;
                            statusLabel.Text = result.Success ? "迁移完成" : $"迁移失败: {result.ErrorMessage}";
                            cancelButton.Text = "关闭";
                        }));
                    }
                    catch (Exception ex)
                    {
                        migrationCompleted = true;
                        result = new MigrationResult { ErrorMessage = ex.Message };
                        
                        progressForm.Invoke(new Action(() =>
                        {
                            statusLabel.Text = $"迁移异常: {ex.Message}";
                            cancelButton.Text = "关闭";
                        }));
                    }
                });

                // 显示进度对话框
                var dialogResult = progressForm.ShowDialog();
                
                // 等待迁移完成
                if (!migrationCompleted)
                {
                    // 用户取消了迁移
                    result = new MigrationResult { ErrorMessage = "用户取消了迁移操作" };
                }

                await migrationWithProgress;
                return result ?? new MigrationResult { ErrorMessage = "未知错误" };
            }
        }

        /// <summary>
        /// 验证迁移后的数据完整性
        /// </summary>
        /// <param name="originalDataPath">原数据文件路径</param>
        /// <param name="newDataPath">新数据文件路径</param>
        /// <returns>数据完整性验证结果</returns>
        public async Task<bool> ValidateDataIntegrityAsync(string originalDataPath, string newDataPath)
        {
            try
            {
                if (!File.Exists(originalDataPath) && !File.Exists(newDataPath))
                {
                    // 两个文件都不存在，认为是正常的
                    return true;
                }

                if (!File.Exists(originalDataPath) || !File.Exists(newDataPath))
                {
                    // 只有一个文件存在，数据不完整
                    return false;
                }

                // 读取并比较两个文件的内容
                string originalContent = await Task.Run(() => File.ReadAllText(originalDataPath));
                string newContent = await Task.Run(() => File.ReadAllText(newDataPath));

                // 解析JSON并比较数据
                try
                {
                    var originalData = JsonConvert.DeserializeObject<List<EmailRecord>>(originalContent);
                    var newData = JsonConvert.DeserializeObject<List<EmailRecord>>(newContent);

                    if (originalData == null && newData == null)
                        return true;

                    if (originalData == null || newData == null)
                        return false;

                    if (originalData.Count != newData.Count)
                        return false;

                    // 比较每个邮箱记录
                    for (int i = 0; i < originalData.Count; i++)
                    {
                        if (!CompareEmailRecords(originalData[i], newData[i]))
                            return false;
                    }

                    return true;
                }
                catch (JsonException)
                {
                    // JSON解析失败，直接比较文件内容
                    return string.Equals(originalContent, newContent, StringComparison.Ordinal);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据完整性验证失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 比较两个邮箱记录是否相同
        /// </summary>
        /// <param name="record1">邮箱记录1</param>
        /// <param name="record2">邮箱记录2</param>
        /// <returns>是否相同</returns>
        private bool CompareEmailRecords(EmailRecord record1, EmailRecord record2)
        {
            if (record1 == null && record2 == null)
                return true;

            if (record1 == null || record2 == null)
                return false;

            return string.Equals(record1.Email, record2.Email, StringComparison.OrdinalIgnoreCase) &&
                   record1.CreatedDate == record2.CreatedDate &&
                   string.Equals(record1.Domain, record2.Domain, StringComparison.OrdinalIgnoreCase) &&
                   string.Equals(record1.Username, record2.Username, StringComparison.OrdinalIgnoreCase) &&
                   record1.Status == record2.Status &&
                   record1.DeactivatedDate == record2.DeactivatedDate;
        }

        /// <summary>
        /// 获取备份文件列表
        /// </summary>
        /// <param name="filePath">原文件路径</param>
        /// <returns>备份文件列表</returns>
        public List<string> GetBackupFiles(string filePath)
        {
            var backupFiles = new List<string>();
            
            try
            {
                if (string.IsNullOrEmpty(filePath))
                    return backupFiles;

                string directory = Path.GetDirectoryName(filePath);
                string fileName = Path.GetFileName(filePath);

                if (string.IsNullOrEmpty(directory) || !Directory.Exists(directory))
                    return backupFiles;

                string searchPattern = fileName + ".backup.*";
                string[] files = Directory.GetFiles(directory, searchPattern);
                
                backupFiles.AddRange(files);
                backupFiles.Sort((x, y) => File.GetCreationTime(y).CompareTo(File.GetCreationTime(x))); // 按创建时间倒序排列
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取备份文件列表失败: {ex.Message}");
            }

            return backupFiles;
        }

        /// <summary>
        /// 从备份文件恢复数据
        /// </summary>
        /// <param name="backupFilePath">备份文件路径</param>
        /// <param name="targetFilePath">目标文件路径</param>
        /// <returns>恢复是否成功</returns>
        public async Task<bool> RestoreFromBackupAsync(string backupFilePath, string targetFilePath)
        {
            try
            {
                if (!File.Exists(backupFilePath))
                {
                    return false;
                }

                // 确保目标目录存在
                string targetDirectory = Path.GetDirectoryName(targetFilePath);
                if (!string.IsNullOrEmpty(targetDirectory) && !Directory.Exists(targetDirectory))
                {
                    _pathManager.CreateDirectoryIfNotExists(targetDirectory);
                }

                // 复制备份文件到目标位置
                await Task.Run(() => File.Copy(backupFilePath, targetFilePath, true));

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"从备份恢复失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 清理过期的备份文件
        /// </summary>
        /// <param name="filePath">原文件路径</param>
        /// <param name="keepDays">保留天数</param>
        /// <returns>清理的文件数量</returns>
        public int CleanupOldBackups(string filePath, int keepDays = 7)
        {
            int cleanedCount = 0;
            
            try
            {
                var backupFiles = GetBackupFiles(filePath);
                DateTime cutoffDate = DateTime.Now.AddDays(-keepDays);

                foreach (string backupFile in backupFiles)
                {
                    try
                    {
                        if (File.GetCreationTime(backupFile) < cutoffDate)
                        {
                            File.Delete(backupFile);
                            cleanedCount++;
                        }
                    }
                    catch
                    {
                        // 删除单个文件失败不影响整体清理
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清理过期备份失败: {ex.Message}");
            }

            return cleanedCount;
        }
    }
}