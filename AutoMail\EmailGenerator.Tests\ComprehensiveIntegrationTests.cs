using Microsoft.VisualStudio.TestTools.UnitTesting;
using EmailGenerator.Services;
using EmailGenerator.Models;
using EmailGenerator.Helpers;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Linq;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// 综合集成测试类
    /// 验证所有需求的完整实现和系统整体功能
    /// </summary>
    [TestClass]
    public class ComprehensiveIntegrationTests
    {
        private string testConfigPath;
        private string testDataPath;
        private ConfigurationService configService;
        private DataService dataService;
        private EmailService emailService;

        [TestInitialize]
        public void Setup()
        {
            // 创建测试专用的临时文件路径
            testConfigPath = Path.Combine(Path.GetTempPath(), $"comprehensive_config_{Guid.NewGuid()}.json");
            testDataPath = Path.Combine(Path.GetTempPath(), $"comprehensive_emails_{Guid.NewGuid()}.json");
            
            // 初始化服务实例
            configService = new ConfigurationService();
            dataService = new DataService(testDataPath);
            emailService = new EmailService(dataService);
        }

        [TestCleanup]
        public void Cleanup()
        {
            // 清理测试文件
            if (File.Exists(testConfigPath))
                File.Delete(testConfigPath);
            if (File.Exists(testDataPath))
                File.Delete(testDataPath);
        }

        /// <summary>
        /// 综合测试：完整的用户工作流程
        /// 模拟用户从启动程序到完成所有操作的完整流程
        /// </summary>
        [TestMethod]
        public void ComprehensiveTest_CompleteUserWorkflow()
        {
            // 阶段1: 程序启动和初始化
            var initialEmails = dataService.LoadEmails();
            var initialDomain = configService.LoadDomain();
            
            Assert.IsNotNull(initialEmails, "程序启动时应该能加载邮箱历史");
            Assert.IsNotNull(initialDomain, "程序启动时应该能加载域名配置");

            // 阶段2: 用户设置域名
            string userDomain = "comprehensive-test.com";
            Assert.IsTrue(emailService.IsValidDomain(userDomain), "用户输入的域名应该有效");
            
            configService.SaveDomain(userDomain);
            string savedDomain = configService.LoadDomain();
            Assert.AreEqual(userDomain, savedDomain, "域名应该正确保存");

            // 阶段3: 生成多个邮箱
            var generatedEmails = new List<EmailRecord>();
            for (int i = 0; i < 5; i++)
            {
                var existingEmails = generatedEmails.ConvertAll(e => e.Email);
                string newEmail = emailService.GenerateUniqueEmail(userDomain, existingEmails);
                
                Assert.IsNotNull(newEmail, $"第{i+1}个邮箱应该成功生成");
                Assert.IsTrue(newEmail.EndsWith($"@{userDomain}"), "生成的邮箱应该使用用户设置的域名");
                Assert.IsFalse(existingEmails.Contains(newEmail), "生成的邮箱应该是唯一的");

                var emailRecord = new EmailRecord
                {
                    Email = newEmail,
                    CreatedDate = DateTime.Now.AddMinutes(-i),
                    Domain = userDomain,
                    Username = newEmail.Split('@')[0],
                    Status = EmailStatus.Active
                };
                
                generatedEmails.Add(emailRecord);
                dataService.AddEmail(emailRecord);
            }

            // 阶段4: 验证邮箱历史记录
            var savedEmails = dataService.LoadEmails();
            Assert.AreEqual(5, savedEmails.Count, "应该保存了5个邮箱");
            
            foreach (var email in generatedEmails)
            {
                var savedEmail = savedEmails.Find(e => e.Email == email.Email);
                Assert.IsNotNull(savedEmail, $"邮箱 {email.Email} 应该被正确保存");
                Assert.AreEqual(EmailStatus.Active, savedEmail.Status, "新生成的邮箱应该是有效状态");
            }

            // 阶段5: 邮箱管理操作
            // 作废第一个邮箱
            string emailToDeactivate = generatedEmails[0].Email;
            dataService.DeactivateEmail(emailToDeactivate);
            
            // 删除第二个邮箱
            string emailToDelete = generatedEmails[1].Email;
            dataService.DeleteEmail(emailToDelete);

            // 阶段6: 验证管理操作结果
            var finalEmails = dataService.LoadEmails();
            Assert.AreEqual(4, finalEmails.Count, "删除一个邮箱后应该剩余4个");
            
            var deactivatedEmail = finalEmails.Find(e => e.Email == emailToDeactivate);
            Assert.IsNotNull(deactivatedEmail, "作废的邮箱应该仍然存在");
            Assert.AreEqual(EmailStatus.Deactivated, deactivatedEmail.Status, "邮箱状态应该是失效");
            Assert.IsNotNull(deactivatedEmail.DeactivatedDate, "失效邮箱应该有失效日期");
            
            var deletedEmail = finalEmails.Find(e => e.Email == emailToDelete);
            Assert.IsNull(deletedEmail, "删除的邮箱不应该存在");

            // 阶段7: 程序重启模拟
            var newConfigService = new ConfigurationService();
            var newDataService = new DataService(testDataPath);
            
            string reloadedDomain = newConfigService.LoadDomain();
            var reloadedEmails = newDataService.LoadEmails();
            
            Assert.AreEqual(userDomain, reloadedDomain, "重启后应该正确加载域名配置");
            Assert.AreEqual(4, reloadedEmails.Count, "重启后应该正确加载邮箱数据");
            
            var reloadedDeactivatedEmail = reloadedEmails.Find(e => e.Email == emailToDeactivate);
            Assert.IsNotNull(reloadedDeactivatedEmail, "重启后应该保持邮箱失效状态");
            Assert.AreEqual(EmailStatus.Deactivated, reloadedDeactivatedEmail.Status, "重启后邮箱状态应该保持失效");
        }

        /// <summary>
        /// 压力测试：大量数据处理性能
        /// </summary>
        [TestMethod]
        public void StressTest_LargeDataHandling()
        {
            string testDomain = "stress-test.com";
            int emailCount = 1000;
            
            var stopwatch = Stopwatch.StartNew();
            
            // 生成大量邮箱
            var generatedEmails = new List<EmailRecord>();
            for (int i = 0; i < emailCount; i++)
            {
                var existingEmails = generatedEmails.ConvertAll(e => e.Email);
                string email = emailService.GenerateUniqueEmail(testDomain, existingEmails);
                
                var record = new EmailRecord
                {
                    Email = email,
                    CreatedDate = DateTime.Now.AddMinutes(-i),
                    Domain = testDomain,
                    Username = email.Split('@')[0],
                    Status = i % 10 == 0 ? EmailStatus.Deactivated : EmailStatus.Active,
                    DeactivatedDate = i % 10 == 0 ? DateTime.Now : null
                };
                
                generatedEmails.Add(record);
                dataService.AddEmail(record);
            }
            
            stopwatch.Stop();
            
            // 验证性能
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 30000, $"生成和保存{emailCount}个邮箱应该在30秒内完成，实际用时: {stopwatch.ElapsedMilliseconds}ms");
            
            // 验证数据完整性
            var savedEmails = dataService.LoadEmails();
            Assert.AreEqual(emailCount, savedEmails.Count, "所有邮箱都应该被正确保存");
            
            var activeEmails = savedEmails.FindAll(e => e.Status == EmailStatus.Active);
            var deactivatedEmails = savedEmails.FindAll(e => e.Status == EmailStatus.Deactivated);
            
            Assert.AreEqual(emailCount - emailCount / 10, activeEmails.Count, "有效邮箱数量应该正确");
            Assert.AreEqual(emailCount / 10, deactivatedEmails.Count, "失效邮箱数量应该正确");
        }

        /// <summary>
        /// 边界条件测试：各种异常情况处理
        /// </summary>
        [TestMethod]
        public void BoundaryTest_ExceptionHandling()
        {
            // 测试无效域名处理
            string[] invalidDomains = { "", "invalid", ".com", "test.", "test..com", "test.c" };
            
            foreach (string invalidDomain in invalidDomains)
            {
                Assert.IsFalse(emailService.IsValidDomain(invalidDomain), $"域名 '{invalidDomain}' 应该被识别为无效");
                
                Assert.ThrowsException<ArgumentException>(() =>
                {
                    emailService.GenerateUniqueEmail(invalidDomain, new List<string>());
                }, $"使用无效域名 '{invalidDomain}' 应该抛出异常");
            }

            // 测试文件权限问题
            string readOnlyPath = Path.Combine(Path.GetTempPath(), $"readonly_{Guid.NewGuid()}.json");
            File.WriteAllText(readOnlyPath, "{}");
            File.SetAttributes(readOnlyPath, FileAttributes.ReadOnly);
            
            try
            {
                var readOnlyDataService = new DataService(readOnlyPath);
                var testEmail = new EmailRecord
                {
                    Email = "<EMAIL>",
                    CreatedDate = DateTime.Now,
                    Domain = "example.com",
                    Username = "test",
                    Status = EmailStatus.Active
                };
                
                // 应该能处理文件写入失败的情况
                Assert.ThrowsException<UnauthorizedAccessException>(() =>
                {
                    readOnlyDataService.AddEmail(testEmail);
                });
            }
            finally
            {
                // 清理只读文件
                File.SetAttributes(readOnlyPath, FileAttributes.Normal);
                File.Delete(readOnlyPath);
            }

            // 测试空数据处理
            var emptyDataService = new DataService(Path.Combine(Path.GetTempPath(), $"empty_{Guid.NewGuid()}.json"));
            var emptyEmails = emptyDataService.LoadEmails();
            Assert.IsNotNull(emptyEmails, "空文件应该返回空列表而不是null");
            Assert.AreEqual(0, emptyEmails.Count, "空文件应该返回空列表");

            // 测试损坏的JSON文件
            string corruptedPath = Path.Combine(Path.GetTempPath(), $"corrupted_{Guid.NewGuid()}.json");
            File.WriteAllText(corruptedPath, "{ invalid json content");
            
            var corruptedDataService = new DataService(corruptedPath);
            var corruptedEmails = corruptedDataService.LoadEmails();
            Assert.IsNotNull(corruptedEmails, "损坏的文件应该返回空列表");
            Assert.AreEqual(0, corruptedEmails.Count, "损坏的文件应该返回空列表");
            
            File.Delete(corruptedPath);
        }

        /// <summary>
        /// 并发测试：多线程操作安全性
        /// </summary>
        [TestMethod]
        public async Task ConcurrencyTest_ThreadSafety()
        {
            string testDomain = "concurrency-test.com";
            int taskCount = 10;
            int emailsPerTask = 10;
            
            var tasks = new List<Task<List<EmailRecord>>>();
            
            // 创建多个并发任务
            for (int i = 0; i < taskCount; i++)
            {
                int taskId = i;
                tasks.Add(Task.Run(() =>
                {
                    var taskEmails = new List<EmailRecord>();
                    for (int j = 0; j < emailsPerTask; j++)
                    {
                        var existingEmails = dataService.LoadEmails().ConvertAll(e => e.Email);
                        existingEmails.AddRange(taskEmails.ConvertAll(e => e.Email));
                        
                        string email = emailService.GenerateUniqueEmail(testDomain, existingEmails);
                        var record = new EmailRecord
                        {
                            Email = email,
                            CreatedDate = DateTime.Now,
                            Domain = testDomain,
                            Username = email.Split('@')[0],
                            Status = EmailStatus.Active
                        };
                        
                        taskEmails.Add(record);
                        dataService.AddEmail(record);
                        
                        // 添加小延迟以增加并发冲突的可能性
                        Task.Delay(10).Wait();
                    }
                    return taskEmails;
                }));
            }
            
            // 等待所有任务完成
            var results = await Task.WhenAll(tasks);
            
            // 验证结果
            var allGeneratedEmails = results.SelectMany(r => r).ToList();
            Assert.AreEqual(taskCount * emailsPerTask, allGeneratedEmails.Count, "应该生成预期数量的邮箱");
            
            // 验证唯一性
            var uniqueEmails = allGeneratedEmails.Select(e => e.Email).Distinct().ToList();
            Assert.AreEqual(allGeneratedEmails.Count, uniqueEmails.Count, "所有生成的邮箱应该是唯一的");
            
            // 验证数据持久化
            var savedEmails = dataService.LoadEmails();
            Assert.AreEqual(taskCount * emailsPerTask, savedEmails.Count, "所有邮箱都应该被正确保存");
        }

        /// <summary>
        /// 数据一致性测试：验证数据完整性
        /// </summary>
        [TestMethod]
        public void DataConsistencyTest_IntegrityValidation()
        {
            string testDomain = "consistency-test.com";
            
            // 创建测试数据
            var testEmails = new List<EmailRecord>();
            for (int i = 0; i < 20; i++)
            {
                var existingEmails = testEmails.ConvertAll(e => e.Email);
                string email = emailService.GenerateUniqueEmail(testDomain, existingEmails);
                
                var record = new EmailRecord
                {
                    Email = email,
                    CreatedDate = DateTime.Now.AddMinutes(-i),
                    Domain = testDomain,
                    Username = email.Split('@')[0],
                    Status = EmailStatus.Active
                };
                
                testEmails.Add(record);
                dataService.AddEmail(record);
            }

            // 执行各种操作
            dataService.DeactivateEmail(testEmails[0].Email);
            dataService.DeactivateEmail(testEmails[2].Email);
            dataService.DeleteEmail(testEmails[1].Email);
            dataService.DeleteEmail(testEmails[3].Email);

            // 验证数据一致性
            var finalEmails = dataService.LoadEmails();
            Assert.AreEqual(18, finalEmails.Count, "删除2个邮箱后应该剩余18个");

            // 验证状态一致性
            var activeEmails = finalEmails.FindAll(e => e.Status == EmailStatus.Active);
            var deactivatedEmails = finalEmails.FindAll(e => e.Status == EmailStatus.Deactivated);
            
            Assert.AreEqual(16, activeEmails.Count, "应该有16个有效邮箱");
            Assert.AreEqual(2, deactivatedEmails.Count, "应该有2个失效邮箱");

            // 验证失效日期一致性
            foreach (var deactivatedEmail in deactivatedEmails)
            {
                Assert.IsNotNull(deactivatedEmail.DeactivatedDate, "失效邮箱应该有失效日期");
                Assert.IsTrue(deactivatedEmail.DeactivatedDate <= DateTime.Now, "失效日期应该不晚于当前时间");
            }

            // 验证域名一致性
            foreach (var email in finalEmails)
            {
                Assert.AreEqual(testDomain, email.Domain, "所有邮箱的域名应该一致");
                Assert.IsTrue(email.Email.EndsWith($"@{testDomain}"), "邮箱地址应该使用正确的域名");
                Assert.AreEqual(email.Email.Split('@')[0], email.Username, "用户名应该与邮箱地址匹配");
            }
        }

        /// <summary>
        /// 性能基准测试：建立性能基线
        /// </summary>
        [TestMethod]
        public void PerformanceBenchmarkTest_EstablishBaseline()
        {
            string testDomain = "benchmark-test.com";
            var performanceMetrics = new Dictionary<string, long>();

            // 测试邮箱生成性能
            var stopwatch = Stopwatch.StartNew();
            var existingEmails = new List<string>();
            for (int i = 0; i < 100; i++)
            {
                string email = emailService.GenerateUniqueEmail(testDomain, existingEmails);
                existingEmails.Add(email);
            }
            stopwatch.Stop();
            performanceMetrics["EmailGeneration_100"] = stopwatch.ElapsedMilliseconds;

            // 测试数据保存性能
            stopwatch.Restart();
            var emailRecords = existingEmails.Select((email, index) => new EmailRecord
            {
                Email = email,
                CreatedDate = DateTime.Now.AddMinutes(-index),
                Domain = testDomain,
                Username = email.Split('@')[0],
                Status = EmailStatus.Active
            }).ToList();

            foreach (var record in emailRecords)
            {
                dataService.AddEmail(record);
            }
            stopwatch.Stop();
            performanceMetrics["DataSave_100"] = stopwatch.ElapsedMilliseconds;

            // 测试数据加载性能
            stopwatch.Restart();
            var loadedEmails = dataService.LoadEmails();
            stopwatch.Stop();
            performanceMetrics["DataLoad_100"] = stopwatch.ElapsedMilliseconds;

            // 验证性能基线
            Assert.IsTrue(performanceMetrics["EmailGeneration_100"] < 5000, $"生成100个邮箱应该在5秒内完成，实际: {performanceMetrics["EmailGeneration_100"]}ms");
            Assert.IsTrue(performanceMetrics["DataSave_100"] < 10000, $"保存100个邮箱应该在10秒内完成，实际: {performanceMetrics["DataSave_100"]}ms");
            Assert.IsTrue(performanceMetrics["DataLoad_100"] < 1000, $"加载100个邮箱应该在1秒内完成，实际: {performanceMetrics["DataLoad_100"]}ms");

            // 输出性能指标（在实际测试中可以记录到日志）
            Console.WriteLine("性能基准测试结果:");
            foreach (var metric in performanceMetrics)
            {
                Console.WriteLine($"{metric.Key}: {metric.Value}ms");
            }
        }
    }
}