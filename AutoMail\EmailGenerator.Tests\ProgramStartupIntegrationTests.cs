using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.IO;
using System.Threading;
using System.Windows.Forms;
using EmailGenerator.Forms;
using EmailGenerator.Services;
using EmailGenerator.Models;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// 程序启动流程集成测试
    /// </summary>
    [TestClass]
    public class ProgramStartupIntegrationTests
    {
        private string _testDataDirectory;
        private string _testConfigFile;
        private string _testDataFile;

        [TestInitialize]
        public void Setup()
        {
            // 创建测试数据目录
            _testDataDirectory = Path.Combine(Path.GetTempPath(), "EmailGeneratorTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDataDirectory);

            _testConfigFile = Path.Combine(_testDataDirectory, "config.json");
            _testDataFile = Path.Combine(_testDataDirectory, "emails.json");
        }

        [TestCleanup]
        public void Cleanup()
        {
            try
            {
                if (Directory.Exists(_testDataDirectory))
                {
                    Directory.Delete(_testDataDirectory, true);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清理测试数据失败: {ex.Message}");
            }
        }

        #region 程序启动测试

        [TestMethod]
        public void MainForm_Constructor_ShouldInitializeSuccessfully()
        {
            // Arrange & Act
            MainForm mainForm = null;
            Exception thrownException = null;

            try
            {
                mainForm = new MainForm();
            }
            catch (Exception ex)
            {
                thrownException = ex;
            }

            // Assert
            Assert.IsNull(thrownException, $"MainForm构造函数不应抛出异常: {thrownException?.Message}");
            Assert.IsNotNull(mainForm, "MainForm应该成功创建");
            
            // 清理
            mainForm?.Dispose();
        }

        [TestMethod]
        public void MainForm_Load_ShouldInitializeServicesAndLoadData()
        {
            // Arrange
            using (var mainForm = new MainForm())
            {
                bool loadEventFired = false;
                Exception loadException = null;

                // 监听Load事件
                mainForm.Load += (sender, e) =>
                {
                    try
                    {
                        loadEventFired = true;
                        // 验证窗体已正确初始化
                        Assert.IsNotNull(sender);
                        Assert.AreEqual(mainForm, sender);
                    }
                    catch (Exception ex)
                    {
                        loadException = ex;
                    }
                };

                // Act
                // 模拟窗体显示过程
                mainForm.Show();
                Application.DoEvents(); // 处理待处理的Windows消息
                Thread.Sleep(100); // 给Load事件时间执行

                // Assert
                Assert.IsTrue(loadEventFired, "Load事件应该被触发");
                Assert.IsNull(loadException, $"Load事件处理不应抛出异常: {loadException?.Message}");
                
                mainForm.Hide();
            }
        }

        [TestMethod]
        public void MainForm_InitializeServices_ShouldCreateAllRequiredServices()
        {
            // Arrange & Act
            using (var mainForm = new MainForm())
            {
                // 通过反射检查私有字段是否已初始化
                var emailServiceField = typeof(MainForm).GetField("_emailService", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var configServiceField = typeof(MainForm).GetField("_configService", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var dataServiceField = typeof(MainForm).GetField("_dataService", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var emailHistoryField = typeof(MainForm).GetField("_emailHistory", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                // Assert
                Assert.IsNotNull(emailServiceField?.GetValue(mainForm), "EmailService应该被初始化");
                Assert.IsNotNull(configServiceField?.GetValue(mainForm), "ConfigurationService应该被初始化");
                Assert.IsNotNull(dataServiceField?.GetValue(mainForm), "DataService应该被初始化");
                Assert.IsNotNull(emailHistoryField?.GetValue(mainForm), "EmailHistory列表应该被初始化");
            }
        }

        #endregion

        #region 配置加载测试

        [TestMethod]
        public void MainForm_LoadConfiguration_WithValidConfig_ShouldLoadSuccessfully()
        {
            // Arrange
            var testDomain = "test.example.com";
            var configService = new ConfigurationService();
            configService.SaveDomain(testDomain);

            // Act & Assert
            using (var mainForm = new MainForm())
            {
                mainForm.Show();
                Application.DoEvents();
                Thread.Sleep(100);

                // 通过反射获取域名输入框的值
                var txtDomainField = typeof(MainForm).GetField("txtDomain", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var txtDomain = txtDomainField?.GetValue(mainForm) as TextBox;

                Assert.IsNotNull(txtDomain, "域名输入框应该存在");
                Assert.AreEqual(testDomain, txtDomain.Text, "应该加载保存的域名配置");
                
                mainForm.Hide();
            }
        }

        [TestMethod]
        public void MainForm_LoadConfiguration_WithInvalidConfig_ShouldHandleGracefully()
        {
            // Arrange
            // 创建无效的配置文件
            var configPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                "EmailGenerator", "config.json");
            var configDir = Path.GetDirectoryName(configPath);
            
            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }
            
            File.WriteAllText(configPath, "invalid json content");

            try
            {
                // Act & Assert
                using (var mainForm = new MainForm())
                {
                    mainForm.Show();
                    Application.DoEvents();
                    Thread.Sleep(100);

                    // 程序应该能够处理无效配置而不崩溃
                    Assert.IsTrue(true, "程序应该能够处理无效配置文件");
                    
                    mainForm.Hide();
                }
            }
            finally
            {
                // 清理
                try
                {
                    if (File.Exists(configPath))
                    {
                        File.Delete(configPath);
                    }
                }
                catch { }
            }
        }

        #endregion

        #region 数据加载测试

        [TestMethod]
        public void MainForm_LoadEmailHistory_WithValidData_ShouldLoadSuccessfully()
        {
            // Arrange
            var testEmails = new[]
            {
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    CreatedDate = DateTime.Now.AddDays(-1),
                    Domain = "example.com",
                    Username = "test1",
                    Status = EmailStatus.Active
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    CreatedDate = DateTime.Now.AddDays(-2),
                    Domain = "example.com",
                    Username = "test2",
                    Status = EmailStatus.Deactivated,
                    DeactivatedDate = DateTime.Now.AddHours(-1)
                }
            };

            var dataService = new DataService();
            foreach (var email in testEmails)
            {
                dataService.AddEmail(email);
            }

            // Act & Assert
            using (var mainForm = new MainForm())
            {
                mainForm.Show();
                Application.DoEvents();
                Thread.Sleep(100);

                // 通过反射获取邮箱历史列表
                var emailHistoryField = typeof(MainForm).GetField("_emailHistory", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var emailHistory = emailHistoryField?.GetValue(mainForm) as System.Collections.Generic.List<EmailRecord>;

                Assert.IsNotNull(emailHistory, "邮箱历史列表应该存在");
                Assert.AreEqual(testEmails.Length, emailHistory.Count, "应该加载所有邮箱记录");
                
                mainForm.Hide();
            }
        }

        [TestMethod]
        public void MainForm_LoadEmailHistory_WithNoData_ShouldHandleGracefully()
        {
            // Arrange
            // 确保没有数据文件
            var dataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                "EmailGenerator", "emails.json");
            if (File.Exists(dataPath))
            {
                File.Delete(dataPath);
            }

            // Act & Assert
            using (var mainForm = new MainForm())
            {
                mainForm.Show();
                Application.DoEvents();
                Thread.Sleep(100);

                // 通过反射获取邮箱历史列表
                var emailHistoryField = typeof(MainForm).GetField("_emailHistory", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var emailHistory = emailHistoryField?.GetValue(mainForm) as System.Collections.Generic.List<EmailRecord>;

                Assert.IsNotNull(emailHistory, "邮箱历史列表应该存在");
                Assert.AreEqual(0, emailHistory.Count, "空数据时历史列表应该为空");
                
                mainForm.Hide();
            }
        }

        #endregion

        #region 窗体状态测试

        [TestMethod]
        public void MainForm_DefaultState_ShouldBeConfiguredCorrectly()
        {
            // Act
            using (var mainForm = new MainForm())
            {
                mainForm.Show();
                Application.DoEvents();
                Thread.Sleep(100);

                // Assert
                Assert.AreEqual("邮箱生成器", mainForm.Text, "窗体标题应该正确");
                Assert.AreEqual(FormBorderStyle.FixedSingle, mainForm.FormBorderStyle, "窗体边框样式应该正确");
                Assert.IsFalse(mainForm.MaximizeBox, "最大化按钮应该被禁用");
                Assert.IsTrue(mainForm.MinimizeBox, "最小化按钮应该被启用");
                
                mainForm.Hide();
            }
        }

        [TestMethod]
        public void MainForm_ControlsInitialization_ShouldCreateAllRequiredControls()
        {
            // Act
            using (var mainForm = new MainForm())
            {
                mainForm.Show();
                Application.DoEvents();
                Thread.Sleep(100);

                // Assert - 检查关键控件是否存在
                var txtDomainField = typeof(MainForm).GetField("txtDomain", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var btnGenerateField = typeof(MainForm).GetField("btnGenerate", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var lstEmailsField = typeof(MainForm).GetField("lstEmails", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                Assert.IsNotNull(txtDomainField?.GetValue(mainForm), "域名输入框应该存在");
                Assert.IsNotNull(btnGenerateField?.GetValue(mainForm), "生成按钮应该存在");
                Assert.IsNotNull(lstEmailsField?.GetValue(mainForm), "邮箱列表应该存在");
                
                mainForm.Hide();
            }
        }

        #endregion

        #region 异常处理测试

        [TestMethod]
        public void MainForm_WithCorruptedData_ShouldHandleGracefully()
        {
            // Arrange
            var dataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                "EmailGenerator", "emails.json");
            var dataDir = Path.GetDirectoryName(dataPath);
            
            if (!Directory.Exists(dataDir))
            {
                Directory.CreateDirectory(dataDir);
            }
            
            File.WriteAllText(dataPath, "corrupted json data");

            try
            {
                // Act & Assert
                using (var mainForm = new MainForm())
                {
                    mainForm.Show();
                    Application.DoEvents();
                    Thread.Sleep(100);

                    // 程序应该能够处理损坏的数据文件而不崩溃
                    Assert.IsTrue(true, "程序应该能够处理损坏的数据文件");
                    
                    mainForm.Hide();
                }
            }
            finally
            {
                // 清理
                try
                {
                    if (File.Exists(dataPath))
                    {
                        File.Delete(dataPath);
                    }
                }
                catch { }
            }
        }

        #endregion

        #region 完整启动流程测试

        [TestMethod]
        public void CompleteStartupFlow_ShouldExecuteWithoutErrors()
        {
            // Arrange
            var testDomain = "startup.test.com";
            var configService = new ConfigurationService();
            configService.SaveDomain(testDomain);

            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                CreatedDate = DateTime.Now,
                Domain = "test.com",
                Username = "startup",
                Status = EmailStatus.Active
            };

            var dataService = new DataService();
            dataService.AddEmail(testEmail);

            // Act & Assert
            Exception startupException = null;
            
            try
            {
                using (var mainForm = new MainForm())
                {
                    // 模拟完整的启动流程
                    mainForm.Show();
                    Application.DoEvents();
                    Thread.Sleep(200); // 给足够时间完成初始化

                    // 验证启动后的状态
                    var txtDomainField = typeof(MainForm).GetField("txtDomain", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    var txtDomain = txtDomainField?.GetValue(mainForm) as TextBox;

                    var emailHistoryField = typeof(MainForm).GetField("_emailHistory", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    var emailHistory = emailHistoryField?.GetValue(mainForm) as System.Collections.Generic.List<EmailRecord>;

                    Assert.IsNotNull(txtDomain, "域名输入框应该存在");
                    Assert.AreEqual(testDomain, txtDomain.Text, "应该加载配置的域名");
                    Assert.IsNotNull(emailHistory, "邮箱历史应该存在");
                    Assert.IsTrue(emailHistory.Count > 0, "应该加载历史邮箱记录");
                    
                    mainForm.Hide();
                }
            }
            catch (Exception ex)
            {
                startupException = ex;
            }

            // Assert
            Assert.IsNull(startupException, $"完整启动流程不应抛出异常: {startupException?.Message}");
        }

        #endregion
    }
}