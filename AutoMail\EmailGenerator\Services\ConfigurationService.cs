using System;
using System.IO;
using EmailGenerator.Models;
using EmailGenerator.Helpers;
using Newtonsoft.Json;

namespace EmailGenerator.Services
{
    /// <summary>
    /// 配置服务类，负责域名配置和路径管理的加载和保存
    /// </summary>
    public class ConfigurationService
    {
        private readonly PathManagerService _pathManager;
        private string _configFilePath;
        private const string DEFAULT_DOMAIN = "example.com";

        /// <summary>
        /// 路径配置数据模型
        /// </summary>
        internal class PathsConfig
        {
            public bool UseCustomPaths { get; set; } = false;
            public string CustomConfigPath { get; set; } = "";
            public string CustomDataPath { get; set; } = "";
        }

        /// <summary>
        /// 配置数据模型
        /// </summary>
        internal class ConfigData
        {
            public string Domain { get; set; } = DEFAULT_DOMAIN;
            public string DataFilePath { get; set; } = "emails.json";
            public DateTime LastUsed { get; set; } = DateTime.Now;
            public PathsConfig Paths { get; set; } = new PathsConfig();
        }

        public ConfigurationService()
        {
            _pathManager = new PathManagerService();
            // 初始时使用默认配置文件路径
            _configFilePath = _pathManager.GetDefaultConfigPath();
        }

        public ConfigurationService(PathManagerService pathManager)
        {
            _pathManager = pathManager ?? new PathManagerService();
            // 初始时使用默认配置文件路径
            _configFilePath = _pathManager.GetDefaultConfigPath();
        }

        /// <summary>
        /// 从配置文件加载域名设置
        /// </summary>
        /// <returns>保存的域名，如果文件不存在则返回默认域名</returns>
        /// <exception cref="InvalidOperationException">当文件访问失败时抛出</exception>
        /// <exception cref="JsonException">当JSON解析失败时抛出</exception>
        public string LoadDomain()
        {
            try
            {
                // 首先尝试从当前配置路径加载
                _configFilePath = GetCurrentConfigPath();
                
                if (!File.Exists(_configFilePath))
                {
                    // 配置文件不存在，返回默认域名
                    return DEFAULT_DOMAIN;
                }

                string jsonContent;
                try
                {
                    jsonContent = File.ReadAllText(_configFilePath);
                }
                catch (UnauthorizedAccessException)
                {
                    throw new InvalidOperationException(ErrorMessages.FILE_ACCESS_ERROR);
                }
                catch (IOException ex)
                {
                    throw new InvalidOperationException($"{ErrorMessages.FILE_READ_ERROR}: {ex.Message}");
                }

                if (string.IsNullOrWhiteSpace(jsonContent))
                {
                    return DEFAULT_DOMAIN;
                }

                try
                {
                    var configData = JsonConvert.DeserializeObject<ConfigData>(jsonContent);
                    return configData?.Domain ?? DEFAULT_DOMAIN;
                }
                catch (JsonException)
                {
                    throw new JsonException(ErrorMessages.CONFIG_INVALID);
                }
            }
            catch (Exception ex) when (!(ex is InvalidOperationException || ex is JsonException))
            {
                // 读取配置文件失败，记录错误但返回默认域名
                System.Diagnostics.Debug.WriteLine($"加载配置失败: {ex.Message}");
                throw new InvalidOperationException($"{ErrorMessages.CONFIG_LOAD_FAILED}: {ex.Message}");
            }
        }

        /// <summary>
        /// 将域名保存到配置文件
        /// </summary>
        /// <param name="domain">要保存的域名</param>
        /// <returns>保存是否成功</returns>
        /// <exception cref="ArgumentException">当域名无效时抛出</exception>
        /// <exception cref="InvalidOperationException">当文件操作失败时抛出</exception>
        /// <exception cref="JsonException">当JSON序列化失败时抛出</exception>
        public bool SaveDomain(string domain)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(domain))
                {
                    throw new ArgumentException(ErrorMessages.DOMAIN_EMPTY);
                }

                // 验证域名格式
                if (!IsValidDomain(domain))
                {
                    throw new ArgumentException(ErrorMessages.DOMAIN_INVALID_FORMAT);
                }

                // 加载现有配置数据以保留路径设置
                var configData = LoadConfigData();
                configData.Domain = domain.Trim().ToLower();
                configData.LastUsed = DateTime.Now;

                // 更新配置文件路径为当前路径
                _configFilePath = GetCurrentConfigPath();

                return SaveConfigData(configData);
            }
            catch (Exception ex) when (!(ex is ArgumentException || ex is InvalidOperationException || ex is JsonException))
            {
                System.Diagnostics.Debug.WriteLine($"保存配置失败: {ex.Message}");
                throw new InvalidOperationException($"{ErrorMessages.CONFIG_SAVE_FAILED}: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取默认数据文件路径
        /// </summary>
        /// <returns>数据文件的完整路径</returns>
        public string GetDefaultDataPath()
        {
            return _pathManager.GetDefaultDataPath();
        }

        /// <summary>
        /// 获取自定义配置文件路径
        /// </summary>
        /// <returns>自定义配置文件路径，如果未设置则返回空字符串</returns>
        public string GetCustomConfigPath()
        {
            try
            {
                var configData = LoadConfigData();
                return configData?.Paths?.CustomConfigPath ?? "";
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// 获取自定义数据文件路径
        /// </summary>
        /// <returns>自定义数据文件路径，如果未设置则返回空字符串</returns>
        public string GetCustomDataPath()
        {
            try
            {
                var configData = LoadConfigData();
                return configData?.Paths?.CustomDataPath ?? "";
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// 设置自定义配置文件路径
        /// </summary>
        /// <param name="path">自定义配置文件路径</param>
        /// <returns>设置是否成功</returns>
        public bool SetCustomConfigPath(string path)
        {
            return SetCustomConfigPathWithFallback(path, true);
        }

        /// <summary>
        /// 设置自定义配置文件路径，支持回退机制
        /// </summary>
        /// <param name="path">自定义配置文件路径</param>
        /// <param name="enableFallback">是否启用回退机制</param>
        /// <returns>设置是否成功</returns>
        public bool SetCustomConfigPathWithFallback(string path, bool enableFallback)
        {
            try
            {
                // 验证路径
                var validation = ValidatePathWithDetails(path);
                if (!validation.IsValid)
                {
                    if (enableFallback)
                    {
                        System.Diagnostics.Debug.WriteLine($"配置路径无效，回退到默认路径: {validation.ErrorMessage}");
                        return FallbackToDefaultConfig(ErrorMessages.PATH_CONFIG_INVALID_FALLBACK);
                    }
                    return false;
                }

                var configData = LoadConfigData();
                string originalConfigPath = configData.Paths.CustomConfigPath;
                
                configData.Paths.CustomConfigPath = path;
                configData.Paths.UseCustomPaths = !string.IsNullOrEmpty(path) || !string.IsNullOrEmpty(configData.Paths.CustomDataPath);
                
                if (SaveConfigData(configData))
                {
                    return true;
                }
                else if (enableFallback)
                {
                    // 保存失败，回退到原配置
                    configData.Paths.CustomConfigPath = originalConfigPath;
                    SaveConfigData(configData);
                    return FallbackToDefaultConfig(ErrorMessages.PATH_CONFIG_SAVE_FAILED_FALLBACK);
                }
                
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置自定义配置路径失败: {ex.Message}");
                
                if (enableFallback)
                {
                    return FallbackToDefaultConfig(ErrorMessages.PATH_CONFIG_SAVE_FAILED_FALLBACK);
                }
                
                return false;
            }
        }

        /// <summary>
        /// 设置自定义数据文件路径
        /// </summary>
        /// <param name="path">自定义数据文件路径</param>
        /// <returns>设置是否成功</returns>
        public bool SetCustomDataPath(string path)
        {
            return SetCustomDataPathWithFallback(path, true);
        }

        /// <summary>
        /// 设置自定义数据文件路径，支持回退机制
        /// </summary>
        /// <param name="path">自定义数据文件路径</param>
        /// <param name="enableFallback">是否启用回退机制</param>
        /// <returns>设置是否成功</returns>
        public bool SetCustomDataPathWithFallback(string path, bool enableFallback)
        {
            try
            {
                // 验证路径
                var validation = ValidatePathWithDetails(path);
                if (!validation.IsValid)
                {
                    if (enableFallback)
                    {
                        System.Diagnostics.Debug.WriteLine($"数据路径无效，回退到默认路径: {validation.ErrorMessage}");
                        return FallbackToDefaultConfig(ErrorMessages.PATH_CONFIG_INVALID_FALLBACK);
                    }
                    return false;
                }

                var configData = LoadConfigData();
                string originalDataPath = configData.Paths.CustomDataPath;
                
                configData.Paths.CustomDataPath = path;
                configData.Paths.UseCustomPaths = !string.IsNullOrEmpty(path) || !string.IsNullOrEmpty(configData.Paths.CustomConfigPath);
                
                if (SaveConfigData(configData))
                {
                    return true;
                }
                else if (enableFallback)
                {
                    // 保存失败，回退到原配置
                    configData.Paths.CustomDataPath = originalDataPath;
                    SaveConfigData(configData);
                    return FallbackToDefaultConfig(ErrorMessages.PATH_CONFIG_SAVE_FAILED_FALLBACK);
                }
                
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置自定义数据路径失败: {ex.Message}");
                
                if (enableFallback)
                {
                    return FallbackToDefaultConfig(ErrorMessages.PATH_CONFIG_SAVE_FAILED_FALLBACK);
                }
                
                return false;
            }
        }

        /// <summary>
        /// 验证路径的有效性
        /// </summary>
        /// <param name="path">要验证的路径</param>
        /// <returns>路径是否有效</returns>
        public bool ValidatePath(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
                return true; // 空路径被认为是有效的（表示使用默认路径）

            try
            {
                var result = _pathManager.ValidatePathWithDetails(path);
                return result.IsValid;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"路径验证失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 验证路径并返回详细结果
        /// </summary>
        /// <param name="path">要验证的路径</param>
        /// <returns>验证结果详情</returns>
        public PathValidationResult ValidatePathWithDetails(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
            {
                return new PathValidationResult
                {
                    IsValid = true,
                    Path = path,
                    ErrorMessage = string.Empty,
                    Guidance = string.Empty
                };
            }

            try
            {
                return _pathManager.ValidatePathWithDetails(path);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"路径详细验证失败: {ex.Message}");
                return new PathValidationResult
                {
                    IsValid = false,
                    Path = path,
                    ErrorMessage = ErrorMessages.PATH_INVALID,
                    Guidance = ErrorMessages.PATH_GUIDANCE_SELECT_VALID
                };
            }
        }

        /// <summary>
        /// 重置为默认路径
        /// </summary>
        /// <returns>重置是否成功</returns>
        public bool ResetToDefaultPaths()
        {
            try
            {
                var configData = LoadConfigData();
                configData.Paths.UseCustomPaths = false;
                configData.Paths.CustomConfigPath = "";
                configData.Paths.CustomDataPath = "";
                
                // 重置配置文件路径为默认路径
                _configFilePath = _pathManager.GetDefaultConfigPath();
                
                return SaveConfigData(configData);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取当前实际使用的配置文件路径
        /// </summary>
        /// <returns>当前配置文件路径</returns>
        public string GetCurrentConfigPath()
        {
            try
            {
                var configData = LoadConfigData();
                if (configData?.Paths?.UseCustomPaths == true && !string.IsNullOrEmpty(configData.Paths.CustomConfigPath))
                {
                    // 如果保存的是完整文件路径，直接返回
                    if (Path.HasExtension(configData.Paths.CustomConfigPath))
                    {
                        return configData.Paths.CustomConfigPath;
                    }
                    // 如果保存的是目录路径，添加文件名
                    return Path.Combine(configData.Paths.CustomConfigPath, "config.json");
                }
            }
            catch
            {
                // 如果加载失败，返回默认路径
            }
            
            return _pathManager.GetDefaultConfigPath();
        }

        /// <summary>
        /// 获取当前实际使用的数据文件路径
        /// </summary>
        /// <returns>当前数据文件路径</returns>
        public string GetCurrentDataPath()
        {
            try
            {
                var configData = LoadConfigData();
                if (configData?.Paths?.UseCustomPaths == true && !string.IsNullOrEmpty(configData.Paths.CustomDataPath))
                {
                    // 如果保存的是完整文件路径，直接返回
                    if (Path.HasExtension(configData.Paths.CustomDataPath))
                    {
                        return configData.Paths.CustomDataPath;
                    }
                    // 如果保存的是目录路径，添加文件名
                    return Path.Combine(configData.Paths.CustomDataPath, "emails.json");
                }
            }
            catch
            {
                // 如果加载失败，返回默认路径
            }
            
            return _pathManager.GetDefaultDataPath();
        }

        /// <summary>
        /// 获取是否使用自定义路径
        /// </summary>
        /// <returns>是否使用自定义路径</returns>
        public bool GetUseCustomPaths()
        {
            try
            {
                var configData = LoadConfigData();
                return configData?.Paths?.UseCustomPaths ?? false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 保存路径配置
        /// </summary>
        /// <param name="useCustomPaths">是否使用自定义路径</param>
        /// <param name="configPath">自定义配置文件路径</param>
        /// <param name="dataPath">自定义数据文件路径</param>
        /// <returns>保存是否成功</returns>
        public bool SavePathConfiguration(bool useCustomPaths, string configPath, string dataPath)
        {
            try
            {
                var configData = LoadConfigData();
                configData.Paths.UseCustomPaths = useCustomPaths;
                configData.Paths.CustomConfigPath = configPath ?? "";
                configData.Paths.CustomDataPath = dataPath ?? "";
                configData.LastUsed = DateTime.Now;

                return SaveConfigData(configData);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 加载完整的配置数据
        /// </summary>
        /// <returns>配置数据对象</returns>
        private ConfigData LoadConfigData()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    return new ConfigData();
                }

                string jsonContent = File.ReadAllText(_configFilePath);
                if (string.IsNullOrWhiteSpace(jsonContent))
                {
                    return new ConfigData();
                }

                var configData = JsonConvert.DeserializeObject<ConfigData>(jsonContent);
                return configData ?? new ConfigData();
            }
            catch
            {
                return new ConfigData();
            }
        }

        /// <summary>
        /// 保存完整的配置数据
        /// </summary>
        /// <param name="configData">要保存的配置数据</param>
        /// <returns>保存是否成功</returns>
        private bool SaveConfigData(ConfigData configData)
        {
            try
            {
                // 确保配置文件目录存在
                string configDir = Path.GetDirectoryName(_configFilePath);
                if (!string.IsNullOrEmpty(configDir) && !Directory.Exists(configDir))
                {
                    _pathManager.CreateDirectoryIfNotExists(configDir);
                }

                string jsonContent = JsonConvert.SerializeObject(configData, Formatting.Indented);
                File.WriteAllText(_configFilePath, jsonContent);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 回退到默认配置
        /// </summary>
        /// <param name="reason">回退原因</param>
        /// <returns>回退是否成功</returns>
        private bool FallbackToDefaultConfig(string reason)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"配置回退: {reason}");
                
                // 重置配置文件路径为默认路径
                _configFilePath = _pathManager.GetDefaultConfigPath();
                
                // 创建默认配置
                var defaultConfig = new ConfigData
                {
                    Domain = DEFAULT_DOMAIN,
                    DataFilePath = "emails.json",
                    LastUsed = DateTime.Now,
                    Paths = new PathsConfig
                    {
                        UseCustomPaths = false,
                        CustomConfigPath = "",
                        CustomDataPath = ""
                    }
                };
                
                return SaveConfigData(defaultConfig);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"回退到默认配置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 安全加载配置数据，失败时回退到默认配置
        /// </summary>
        /// <returns>配置数据</returns>
        private ConfigData SafeLoadConfigData()
        {
            try
            {
                return LoadConfigData();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载配置数据失败，使用默认配置: {ex.Message}");
                
                // 尝试回退到默认配置
                FallbackToDefaultConfig(ErrorMessages.PATH_CONFIG_CORRUPTED_FALLBACK);
                
                return new ConfigData();
            }
        }

        /// <summary>
        /// 安全保存配置数据，失败时记录错误
        /// </summary>
        /// <param name="configData">要保存的配置数据</param>
        /// <returns>保存是否成功</returns>
        private bool SafeSaveConfigData(ConfigData configData)
        {
            try
            {
                return SaveConfigData(configData);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存配置数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 安全保存配置数据并返回详细结果
        /// </summary>
        /// <param name="configData">要保存的配置数据</param>
        /// <returns>保存结果详情</returns>
        private ConfigOperationResult SafeSaveConfigDataWithDetails(ConfigData configData)
        {
            var result = new ConfigOperationResult
            {
                Success = false,
                ErrorMessage = string.Empty,
                ConfigPath = _configFilePath
            };

            try
            {
                // 验证配置文件路径
                var pathValidation = _pathManager.ValidatePathWithDetails(_configFilePath);
                if (!pathValidation.IsValid)
                {
                    result.ErrorMessage = pathValidation.ErrorMessage;
                    return result;
                }

                // 确保配置文件目录存在
                string configDir = Path.GetDirectoryName(_configFilePath);
                if (!string.IsNullOrEmpty(configDir))
                {
                    var dirResult = _pathManager.CreateDirectoryIfNotExistsWithDetails(configDir);
                    if (!dirResult.Success)
                    {
                        result.ErrorMessage = dirResult.ErrorMessage;
                        return result;
                    }
                }

                // 检查磁盘空间
                string jsonContent = JsonConvert.SerializeObject(configData, Formatting.Indented);
                long requiredBytes = System.Text.Encoding.UTF8.GetByteCount(jsonContent);
                if (!_pathManager.HasSufficientDiskSpace(_configFilePath, requiredBytes))
                {
                    result.ErrorMessage = ErrorMessages.PATH_DISK_FULL;
                    return result;
                }

                // 保存配置文件
                File.WriteAllText(_configFilePath, jsonContent);
                result.Success = true;
                return result;
            }
            catch (UnauthorizedAccessException)
            {
                result.ErrorMessage = ErrorMessages.PATH_ACCESS_DENIED;
                return result;
            }
            catch (DirectoryNotFoundException)
            {
                result.ErrorMessage = ErrorMessages.PATH_DIRECTORY_NOT_EXIST;
                return result;
            }
            catch (IOException ex) when (ex.Message.Contains("disk") || ex.Message.Contains("space"))
            {
                result.ErrorMessage = ErrorMessages.PATH_DISK_FULL;
                return result;
            }
            catch (IOException ex)
            {
                result.ErrorMessage = $"{ErrorMessages.FILE_WRITE_ERROR}: {ex.Message}";
                return result;
            }
            catch (JsonException ex)
            {
                result.ErrorMessage = $"{ErrorMessages.CONFIG_SAVE_FAILED}: JSON序列化失败 - {ex.Message}";
                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = PathErrorHandler.AnalyzePathError(_configFilePath, ex);
                System.Diagnostics.Debug.WriteLine($"保存配置数据详细操作失败: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 安全加载配置数据并返回详细结果
        /// </summary>
        /// <returns>加载结果详情</returns>
        private ConfigLoadResult SafeLoadConfigDataWithDetails()
        {
            var result = new ConfigLoadResult
            {
                Success = false,
                ConfigData = new ConfigData(),
                ErrorMessage = string.Empty,
                ConfigPath = _configFilePath
            };

            try
            {
                if (!File.Exists(_configFilePath))
                {
                    // 配置文件不存在，返回默认配置
                    result.Success = true;
                    result.ConfigData = new ConfigData();
                    return result;
                }

                // 验证配置文件路径
                var pathValidation = _pathManager.ValidatePathWithDetails(_configFilePath);
                if (!pathValidation.IsValid)
                {
                    result.ErrorMessage = pathValidation.ErrorMessage;
                    return result;
                }

                string jsonContent = File.ReadAllText(_configFilePath);
                if (string.IsNullOrWhiteSpace(jsonContent))
                {
                    result.Success = true;
                    result.ConfigData = new ConfigData();
                    return result;
                }

                var configData = JsonConvert.DeserializeObject<ConfigData>(jsonContent);
                result.Success = true;
                result.ConfigData = configData ?? new ConfigData();
                return result;
            }
            catch (UnauthorizedAccessException)
            {
                result.ErrorMessage = ErrorMessages.PATH_ACCESS_DENIED;
                return result;
            }
            catch (DirectoryNotFoundException)
            {
                result.ErrorMessage = ErrorMessages.PATH_DIRECTORY_NOT_EXIST;
                return result;
            }
            catch (FileNotFoundException)
            {
                // 文件不存在，返回默认配置
                result.Success = true;
                result.ConfigData = new ConfigData();
                return result;
            }
            catch (IOException ex)
            {
                result.ErrorMessage = $"{ErrorMessages.FILE_READ_ERROR}: {ex.Message}";
                return result;
            }
            catch (JsonException ex)
            {
                result.ErrorMessage = $"{ErrorMessages.CONFIG_INVALID}: JSON解析失败 - {ex.Message}";
                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = PathErrorHandler.AnalyzePathError(_configFilePath, ex);
                System.Diagnostics.Debug.WriteLine($"加载配置数据详细操作失败: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 执行配置备份
        /// </summary>
        /// <returns>备份结果</returns>
        private ConfigBackupResult CreateConfigBackup()
        {
            var result = new ConfigBackupResult
            {
                Success = false,
                BackupPath = string.Empty,
                ErrorMessage = string.Empty
            };

            try
            {
                if (!File.Exists(_configFilePath))
                {
                    result.Success = true; // 原文件不存在，无需备份
                    return result;
                }

                string backupPath = _configFilePath + ".backup." + DateTime.Now.ToString("yyyyMMddHHmmss");
                var copyResult = _pathManager.SafeCopyFile(_configFilePath, backupPath);
                
                result.Success = copyResult.Success;
                result.BackupPath = copyResult.Success ? backupPath : string.Empty;
                result.ErrorMessage = copyResult.ErrorMessage;
                
                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"{ErrorMessages.PATH_BACKUP_FAILED}: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"创建配置备份失败: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 从备份恢复配置
        /// </summary>
        /// <param name="backupPath">备份文件路径</param>
        /// <returns>恢复结果</returns>
        private bool RestoreConfigFromBackup(string backupPath)
        {
            try
            {
                if (string.IsNullOrEmpty(backupPath) || !File.Exists(backupPath))
                {
                    return false;
                }

                var moveResult = _pathManager.SafeMoveFile(backupPath, _configFilePath);
                return moveResult.Success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"从备份恢复配置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 验证域名格式是否有效
        /// </summary>
        /// <param name="domain">要验证的域名</param>
        /// <returns>域名是否有效</returns>
        private bool IsValidDomain(string domain)
        {
            if (string.IsNullOrWhiteSpace(domain))
                return false;

            // 基本的域名格式验证
            // 域名应该包含至少一个点，且不能以点开头或结尾
            domain = domain.Trim();
            
            if (domain.StartsWith(".") || domain.EndsWith("."))
                return false;

            if (!domain.Contains("."))
                return false;

            // 检查是否包含连续的点
            if (domain.Contains(".."))
                return false;

            // 检查是否包含非法字符
            foreach (char c in domain)
            {
                if (!char.IsLetterOrDigit(c) && c != '.' && c != '-')
                    return false;
            }

            return true;
        }
    }

    /// <summary>
    /// 配置操作结果
    /// </summary>
    public class ConfigOperationResult
    {
        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 配置文件路径
        /// </summary>
        public string ConfigPath { get; set; }
    }

    /// <summary>
    /// 配置加载结果
    /// </summary>
    internal class ConfigLoadResult
    {
        /// <summary>
        /// 加载是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 加载的配置数据
        /// </summary>
        public ConfigurationService.ConfigData ConfigData { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 配置文件路径
        /// </summary>
        public string ConfigPath { get; set; }
    }

    /// <summary>
    /// 配置备份结果
    /// </summary>
    public class ConfigBackupResult
    {
        /// <summary>
        /// 备份是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 备份文件路径
        /// </summary>
        public string BackupPath { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
    }
}