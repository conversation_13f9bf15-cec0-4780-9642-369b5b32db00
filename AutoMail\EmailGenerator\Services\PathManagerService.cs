using System;
using System.IO;
using System.Windows.Forms;
using EmailGenerator.Models;
using EmailGenerator.Helpers;

namespace EmailGenerator.Services
{
    /// <summary>
    /// 路径管理服务，处理文件路径相关操作
    /// </summary>
    public class PathManagerService
    {
        /// <summary>
        /// 获取程序exe所在目录
        /// </summary>
        /// <returns>程序exe所在目录的完整路径</returns>
        public string GetExecutableDirectory()
        {
            try
            {
                return Path.GetDirectoryName(Application.ExecutablePath);
            }
            catch (Exception ex)
            {
                // 如果获取失败，返回当前工作目录作为备选
                return Directory.GetCurrentDirectory();
            }
        }

        /// <summary>
        /// 获取默认配置文件路径
        /// </summary>
        /// <returns>默认配置文件的完整路径</returns>
        public string GetDefaultConfigPath()
        {
            string exeDirectory = GetExecutableDirectory();
            return Path.Combine(exeDirectory, "config.json");
        }

        /// <summary>
        /// 获取默认数据文件路径
        /// </summary>
        /// <returns>默认数据文件的完整路径</returns>
        public string GetDefaultDataPath()
        {
            string exeDirectory = GetExecutableDirectory();
            return Path.Combine(exeDirectory, "emails.json");
        }

        /// <summary>
        /// 验证路径的有效性
        /// </summary>
        /// <param name="path">要验证的路径</param>
        /// <returns>路径是否有效</returns>
        public bool IsValidPath(string path)
        {
            try
            {
                var result = PathErrorHandler.ValidatePathDetailed(path);
                return result.IsValid;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"路径验证失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 验证路径的有效性并返回详细结果
        /// </summary>
        /// <param name="path">要验证的路径</param>
        /// <returns>详细的验证结果</returns>
        public PathValidationResult ValidatePathWithDetails(string path)
        {
            try
            {
                return PathErrorHandler.ValidatePathDetailed(path);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"路径详细验证失败: {ex.Message}");
                return new PathValidationResult
                {
                    IsValid = false,
                    Path = path,
                    ErrorMessage = ErrorMessages.PATH_INVALID,
                    Guidance = ErrorMessages.PATH_GUIDANCE_SELECT_VALID
                };
            }
        }

        /// <summary>
        /// 验证路径是否可写
        /// </summary>
        /// <param name="path">要验证的路径</param>
        /// <returns>路径是否可写</returns>
        public bool IsWritablePath(string path)
        {
            if (!IsValidPath(path))
                return false;

            try
            {
                // 如果是文件路径，获取其目录
                string directoryPath = path;
                if (Path.HasExtension(path))
                {
                    directoryPath = Path.GetDirectoryName(path);
                }

                if (string.IsNullOrEmpty(directoryPath))
                    return false;

                // 找到第一个存在的父目录
                string currentPath = directoryPath;
                while (!Directory.Exists(currentPath))
                {
                    string parentPath = Path.GetDirectoryName(currentPath);
                    if (string.IsNullOrEmpty(parentPath) || parentPath == currentPath)
                    {
                        // 到达根目录但仍不存在，返回false
                        return false;
                    }
                    currentPath = parentPath;
                }

                // 在存在的目录中测试写权限
                string tempFile = Path.Combine(currentPath, Path.GetRandomFileName());
                try
                {
                    using (FileStream fs = File.Create(tempFile))
                    {
                        // 文件创建成功，说明有写权限
                    }
                    File.Delete(tempFile);
                    return true;
                }
                catch
                {
                    return false;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 如果目录不存在则创建目录
        /// </summary>
        /// <param name="path">目录路径</param>
        /// <returns>是否成功创建或目录已存在</returns>
        public bool CreateDirectoryIfNotExists(string path)
        {
            try
            {
                return CreateDirectoryIfNotExistsWithDetails(path).Success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建目录失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 如果目录不存在则创建目录，并返回详细结果
        /// </summary>
        /// <param name="path">目录路径</param>
        /// <returns>创建结果详情</returns>
        public DirectoryCreationResult CreateDirectoryIfNotExistsWithDetails(string path)
        {
            var result = new DirectoryCreationResult
            {
                Success = false,
                Path = path,
                ErrorMessage = string.Empty
            };

            try
            {
                if (string.IsNullOrWhiteSpace(path))
                {
                    result.ErrorMessage = ErrorMessages.PATH_EMPTY;
                    return result;
                }

                // 如果是文件路径，获取其目录
                string directoryPath = path;
                if (Path.HasExtension(path))
                {
                    directoryPath = Path.GetDirectoryName(path);
                }

                if (string.IsNullOrEmpty(directoryPath))
                {
                    result.ErrorMessage = ErrorMessages.PATH_INVALID;
                    return result;
                }

                result.Path = directoryPath;

                // 如果目录已存在，返回成功
                if (Directory.Exists(directoryPath))
                {
                    result.Success = true;
                    return result;
                }

                // 验证路径有效性
                var validation = ValidatePathWithDetails(directoryPath);
                if (!validation.IsValid)
                {
                    result.ErrorMessage = validation.ErrorMessage;
                    return result;
                }

                // 尝试创建目录
                Directory.CreateDirectory(directoryPath);
                result.Success = true;
                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = PathErrorHandler.AnalyzePathCreationError(path, ex);
                System.Diagnostics.Debug.WriteLine($"创建目录详细操作失败: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 检查目录是否有写入权限
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <returns>是否有写入权限</returns>
        public bool HasWritePermission(string directoryPath)
        {
            if (string.IsNullOrWhiteSpace(directoryPath))
                return false;

            try
            {
                // 如果目录不存在，检查父目录
                if (!Directory.Exists(directoryPath))
                {
                    string parentDir = Path.GetDirectoryName(directoryPath);
                    if (!string.IsNullOrEmpty(parentDir))
                    {
                        return HasWritePermission(parentDir);
                    }
                    return false;
                }

                // 尝试在目录中创建临时文件来测试写权限
                string tempFile = Path.Combine(directoryPath, Path.GetRandomFileName());
                try
                {
                    using (FileStream fs = File.Create(tempFile))
                    {
                        // 文件创建成功，说明有写权限
                    }
                    File.Delete(tempFile);
                    return true;
                }
                catch
                {
                    return false;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 显示文件夹选择对话框
        /// </summary>
        /// <param name="title">对话框标题</param>
        /// <param name="defaultPath">默认路径</param>
        /// <returns>用户选择的路径，如果取消则返回null</returns>
        public string SelectFolderDialog(string title, string defaultPath = null)
        {
            using (FolderBrowserDialog dialog = new FolderBrowserDialog())
            {
                dialog.Description = title ?? "请选择文件夹";
                dialog.ShowNewFolderButton = true;

                // 设置默认路径
                if (!string.IsNullOrEmpty(defaultPath) && Directory.Exists(defaultPath))
                {
                    dialog.SelectedPath = defaultPath;
                }
                else
                {
                    dialog.SelectedPath = GetExecutableDirectory();
                }

                DialogResult result = dialog.ShowDialog();
                if (result == DialogResult.OK && !string.IsNullOrEmpty(dialog.SelectedPath))
                {
                    return dialog.SelectedPath;
                }

                return null;
            }
        }

        /// <summary>
        /// 安全执行路径操作，失败时提供详细错误信息
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="operation">要执行的操作</param>
        /// <param name="path">操作的路径</param>
        /// <param name="defaultValue">失败时的默认返回值</param>
        /// <param name="showErrorDialog">是否显示错误对话框</param>
        /// <param name="owner">父窗体</param>
        /// <returns>操作结果</returns>
        public T SafeExecutePathOperation<T>(Func<string, T> operation, string path, T defaultValue, bool showErrorDialog = false, IWin32Window owner = null)
        {
            try
            {
                return operation(path);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"路径操作失败: {ex.Message}");
                
                if (showErrorDialog)
                {
                    PathErrorHandler.ShowPathErrorDialog(path, ex, owner);
                }
                
                return defaultValue;
            }
        }

        /// <summary>
        /// 安全执行路径操作并返回详细结果
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="operation">要执行的操作</param>
        /// <param name="path">操作的路径</param>
        /// <param name="defaultValue">失败时的默认返回值</param>
        /// <returns>操作结果详情</returns>
        public PathOperationResult<T> SafeExecutePathOperationWithDetails<T>(Func<string, T> operation, string path, T defaultValue)
        {
            var result = new PathOperationResult<T>
            {
                Success = false,
                Value = defaultValue,
                Path = path,
                ErrorMessage = string.Empty
            };

            try
            {
                result.Value = operation(path);
                result.Success = true;
                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = PathErrorHandler.AnalyzePathError(path, ex);
                System.Diagnostics.Debug.WriteLine($"路径操作失败: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 安全删除文件，提供详细的错误处理
        /// </summary>
        /// <param name="filePath">要删除的文件路径</param>
        /// <returns>删除结果详情</returns>
        public FileOperationResult SafeDeleteFile(string filePath)
        {
            var result = new FileOperationResult
            {
                Success = false,
                Path = filePath,
                ErrorMessage = string.Empty
            };

            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    result.ErrorMessage = ErrorMessages.PATH_EMPTY;
                    return result;
                }

                if (!File.Exists(filePath))
                {
                    result.Success = true; // 文件不存在视为删除成功
                    return result;
                }

                File.Delete(filePath);
                result.Success = true;
                return result;
            }
            catch (UnauthorizedAccessException)
            {
                result.ErrorMessage = ErrorMessages.PATH_ACCESS_DENIED;
                return result;
            }
            catch (DirectoryNotFoundException)
            {
                result.ErrorMessage = ErrorMessages.PATH_DIRECTORY_NOT_EXIST;
                return result;
            }
            catch (IOException ex)
            {
                result.ErrorMessage = $"{ErrorMessages.FILE_DELETE_ERROR}: {ex.Message}";
                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = PathErrorHandler.AnalyzePathError(filePath, ex);
                System.Diagnostics.Debug.WriteLine($"删除文件失败: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 安全复制文件，提供详细的错误处理
        /// </summary>
        /// <param name="sourcePath">源文件路径</param>
        /// <param name="destinationPath">目标文件路径</param>
        /// <param name="overwrite">是否覆盖已存在的文件</param>
        /// <returns>复制结果详情</returns>
        public FileOperationResult SafeCopyFile(string sourcePath, string destinationPath, bool overwrite = false)
        {
            var result = new FileOperationResult
            {
                Success = false,
                Path = sourcePath,
                ErrorMessage = string.Empty
            };

            try
            {
                if (string.IsNullOrWhiteSpace(sourcePath))
                {
                    result.ErrorMessage = ErrorMessages.PATH_EMPTY;
                    return result;
                }

                if (string.IsNullOrWhiteSpace(destinationPath))
                {
                    result.ErrorMessage = ErrorMessages.PATH_EMPTY;
                    return result;
                }

                if (!File.Exists(sourcePath))
                {
                    result.ErrorMessage = ErrorMessages.FILE_READ_ERROR;
                    return result;
                }

                // 确保目标目录存在
                string destinationDir = Path.GetDirectoryName(destinationPath);
                if (!string.IsNullOrEmpty(destinationDir))
                {
                    var dirResult = CreateDirectoryIfNotExistsWithDetails(destinationDir);
                    if (!dirResult.Success)
                    {
                        result.ErrorMessage = dirResult.ErrorMessage;
                        return result;
                    }
                }

                File.Copy(sourcePath, destinationPath, overwrite);
                result.Success = true;
                result.Path = destinationPath;
                return result;
            }
            catch (UnauthorizedAccessException)
            {
                result.ErrorMessage = ErrorMessages.PATH_ACCESS_DENIED;
                return result;
            }
            catch (DirectoryNotFoundException)
            {
                result.ErrorMessage = ErrorMessages.PATH_DIRECTORY_NOT_EXIST;
                return result;
            }
            catch (IOException ex) when (ex.Message.Contains("already exists"))
            {
                result.ErrorMessage = "目标文件已存在，无法覆盖";
                return result;
            }
            catch (IOException ex) when (ex.Message.Contains("disk") || ex.Message.Contains("space"))
            {
                result.ErrorMessage = ErrorMessages.PATH_DISK_FULL;
                return result;
            }
            catch (IOException ex)
            {
                result.ErrorMessage = $"{ErrorMessages.FILE_WRITE_ERROR}: {ex.Message}";
                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = PathErrorHandler.AnalyzePathError(sourcePath, ex);
                System.Diagnostics.Debug.WriteLine($"复制文件失败: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 安全移动文件，提供详细的错误处理
        /// </summary>
        /// <param name="sourcePath">源文件路径</param>
        /// <param name="destinationPath">目标文件路径</param>
        /// <returns>移动结果详情</returns>
        public FileOperationResult SafeMoveFile(string sourcePath, string destinationPath)
        {
            var result = new FileOperationResult
            {
                Success = false,
                Path = sourcePath,
                ErrorMessage = string.Empty
            };

            try
            {
                if (string.IsNullOrWhiteSpace(sourcePath))
                {
                    result.ErrorMessage = ErrorMessages.PATH_EMPTY;
                    return result;
                }

                if (string.IsNullOrWhiteSpace(destinationPath))
                {
                    result.ErrorMessage = ErrorMessages.PATH_EMPTY;
                    return result;
                }

                if (!File.Exists(sourcePath))
                {
                    result.ErrorMessage = ErrorMessages.FILE_READ_ERROR;
                    return result;
                }

                // 确保目标目录存在
                string destinationDir = Path.GetDirectoryName(destinationPath);
                if (!string.IsNullOrEmpty(destinationDir))
                {
                    var dirResult = CreateDirectoryIfNotExistsWithDetails(destinationDir);
                    if (!dirResult.Success)
                    {
                        result.ErrorMessage = dirResult.ErrorMessage;
                        return result;
                    }
                }

                File.Move(sourcePath, destinationPath);
                result.Success = true;
                result.Path = destinationPath;
                return result;
            }
            catch (UnauthorizedAccessException)
            {
                result.ErrorMessage = ErrorMessages.PATH_ACCESS_DENIED;
                return result;
            }
            catch (DirectoryNotFoundException)
            {
                result.ErrorMessage = ErrorMessages.PATH_DIRECTORY_NOT_EXIST;
                return result;
            }
            catch (IOException ex) when (ex.Message.Contains("already exists"))
            {
                result.ErrorMessage = "目标文件已存在，无法移动";
                return result;
            }
            catch (IOException ex) when (ex.Message.Contains("disk") || ex.Message.Contains("space"))
            {
                result.ErrorMessage = ErrorMessages.PATH_DISK_FULL;
                return result;
            }
            catch (IOException ex)
            {
                result.ErrorMessage = $"{ErrorMessages.FILE_WRITE_ERROR}: {ex.Message}";
                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = PathErrorHandler.AnalyzePathError(sourcePath, ex);
                System.Diagnostics.Debug.WriteLine($"移动文件失败: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 获取路径的磁盘空间信息
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <returns>磁盘空间信息</returns>
        public DiskSpaceInfo GetDiskSpaceInfo(string path)
        {
            var result = new DiskSpaceInfo
            {
                IsValid = false,
                Path = path,
                TotalBytes = 0,
                FreeBytes = 0,
                UsedBytes = 0
            };

            try
            {
                if (string.IsNullOrWhiteSpace(path))
                {
                    return result;
                }

                string rootPath = Path.GetPathRoot(path);
                if (string.IsNullOrEmpty(rootPath))
                {
                    return result;
                }

                DriveInfo drive = new DriveInfo(rootPath);
                if (drive.IsReady)
                {
                    result.IsValid = true;
                    result.TotalBytes = drive.TotalSize;
                    result.FreeBytes = drive.AvailableFreeSpace;
                    result.UsedBytes = drive.TotalSize - drive.AvailableFreeSpace;
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取磁盘空间信息失败: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 检查路径是否有足够的磁盘空间
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <param name="requiredBytes">需要的字节数</param>
        /// <returns>是否有足够空间</returns>
        public bool HasSufficientDiskSpace(string path, long requiredBytes)
        {
            try
            {
                var spaceInfo = GetDiskSpaceInfo(path);
                return spaceInfo.IsValid && spaceInfo.FreeBytes >= requiredBytes;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查磁盘空间失败: {ex.Message}");
                return false;
            }
        }
    }

    /// <summary>
    /// 目录创建结果
    /// </summary>
    public class DirectoryCreationResult
    {
        /// <summary>
        /// 创建是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 目录路径
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// 路径操作结果
    /// </summary>
    /// <typeparam name="T">返回值类型</typeparam>
    public class PathOperationResult<T>
    {
        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 操作结果值
        /// </summary>
        public T Value { get; set; }

        /// <summary>
        /// 操作的路径
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// 文件操作结果
    /// </summary>
    public class FileOperationResult
    {
        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 操作的文件路径
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// 磁盘空间信息
    /// </summary>
    public class DiskSpaceInfo
    {
        /// <summary>
        /// 信息是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 检查的路径
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// 总字节数
        /// </summary>
        public long TotalBytes { get; set; }

        /// <summary>
        /// 可用字节数
        /// </summary>
        public long FreeBytes { get; set; }

        /// <summary>
        /// 已用字节数
        /// </summary>
        public long UsedBytes { get; set; }

        /// <summary>
        /// 获取可用空间百分比
        /// </summary>
        public double FreeSpacePercentage => TotalBytes > 0 ? (double)FreeBytes / TotalBytes * 100 : 0;

        /// <summary>
        /// 获取已用空间百分比
        /// </summary>
        public double UsedSpacePercentage => TotalBytes > 0 ? (double)UsedBytes / TotalBytes * 100 : 0;
    }
}