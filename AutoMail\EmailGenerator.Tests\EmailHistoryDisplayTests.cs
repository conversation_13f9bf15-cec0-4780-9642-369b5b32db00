using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using EmailGenerator.Forms;
using EmailGenerator.Services;
using EmailGenerator.Models;

namespace EmailGenerator.Tests
{
    [TestClass]
    public class EmailHistoryDisplayTests
    {
        private MainForm _mainForm;
        private string _testConfigPath;
        private string _testDataPath;
        private string _originalConfigPath;
        private string _originalDataPath;

        [TestInitialize]
        public void Setup()
        {
            _originalConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.json");
            _originalDataPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "emails.json");
            _testConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "test_config.json");
            _testDataPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "test_emails.json");
            
            BackupOriginalFiles();
            _mainForm = new MainForm();
        }

        [TestCleanup]
        public void Cleanup()
        {
            CleanupTestFiles();
            RestoreOriginalFiles();
            _mainForm?.Dispose();
        }

        [TestMethod]
        public void TestLoadEmailHistoryWithEmptyData()
        {
            var dataService = new DataService();
            
            // 确保数据文件为空
            dataService.SaveEmails(new List<EmailRecord>());

            using (var form = new MainForm())
            {
                var lstEmails = GetPrivateField<ListBox>(form, "lstEmails");
                
                Assert.IsNotNull(lstEmails, "邮箱列表控件不应为null");
                Assert.AreEqual(1, lstEmails.Items.Count, "空数据时应显示一条提示信息");
                Assert.AreEqual("暂无邮箱记录", lstEmails.Items[0].ToString(), "应显示正确的空数据提示");
            }
        }

        [TestMethod]
        public void TestLoadEmailHistoryWithData()
        {
            var dataService = new DataService();
            var testEmails = new List<EmailRecord>
            {
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Username = "test1",
                    Domain = "example.com",
                    CreatedDate = DateTime.Now.AddHours(-2),
                    Status = EmailStatus.Active
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Username = "test2",
                    Domain = "example.com",
                    CreatedDate = DateTime.Now.AddHours(-1),
                    Status = EmailStatus.Deactivated,
                    DeactivatedDate = DateTime.Now.AddMinutes(-30)
                }
            };

            dataService.SaveEmails(testEmails);

            using (var form = new MainForm())
            {
                var lstEmails = GetPrivateField<ListBox>(form, "lstEmails");
                
                Assert.IsNotNull(lstEmails, "邮箱列表控件不应为null");
                Assert.AreEqual(2, lstEmails.Items.Count, "应显示2条邮箱记录");
                
                // 验证显示格式
                string firstItem = lstEmails.Items[0].ToString();
                string secondItem = lstEmails.Items[1].ToString();
                
                // 应该按创建时间倒序排列，所以test2应该在前面
                Assert.IsTrue(firstItem.Contains("<EMAIL>"), "第一项应该是最新创建的邮箱");
                Assert.IsTrue(firstItem.Contains("✗"), "已作废邮箱应显示✗图标");
                Assert.IsTrue(firstItem.Contains("(已作废)"), "已作废邮箱应显示作废标识");
                
                Assert.IsTrue(secondItem.Contains("<EMAIL>"), "第二项应该是较早创建的邮箱");
                Assert.IsTrue(secondItem.Contains("✓"), "有效邮箱应显示✓图标");
                Assert.IsFalse(secondItem.Contains("(已作废)"), "有效邮箱不应显示作废标识");
            }
        }

        [TestMethod]
        public void TestEmailListDisplayFormat()
        {
            var dataService = new DataService();
            var testDate = new DateTime(2024, 1, 15, 14, 30, 0);
            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                Username = "format",
                Domain = "test.com",
                CreatedDate = testDate,
                Status = EmailStatus.Active
            };

            dataService.SaveEmails(new List<EmailRecord> { testEmail });

            using (var form = new MainForm())
            {
                var lstEmails = GetPrivateField<ListBox>(form, "lstEmails");
                
                Assert.AreEqual(1, lstEmails.Items.Count, "应显示1条邮箱记录");
                
                string displayText = lstEmails.Items[0].ToString();
                
                // 验证显示格式：✓ <EMAIL> - 2024-01-15 14:30
                Assert.IsTrue(displayText.Contains("✓"), "应包含状态图标");
                Assert.IsTrue(displayText.Contains("<EMAIL>"), "应包含邮箱地址");
                Assert.IsTrue(displayText.Contains("2024-01-15 14:30"), "应包含格式化的创建时间");
            }
        }

        [TestMethod]
        public void TestEmailListSelectionStateManagement()
        {
            var dataService = new DataService();
            var testEmails = new List<EmailRecord>
            {
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Username = "active",
                    Domain = "test.com",
                    CreatedDate = DateTime.Now,
                    Status = EmailStatus.Active
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Username = "deactivated",
                    Domain = "test.com",
                    CreatedDate = DateTime.Now.AddMinutes(-1),
                    Status = EmailStatus.Deactivated
                }
            };

            dataService.SaveEmails(testEmails);

            using (var form = new MainForm())
            {
                var lstEmails = GetPrivateField<ListBox>(form, "lstEmails");
                var btnCopy = GetPrivateField<Button>(form, "btnCopy");
                var btnDeactivate = GetPrivateField<Button>(form, "btnDeactivate");
                var btnDelete = GetPrivateField<Button>(form, "btnDelete");
                
                // 初始状态：没有选择，按钮应该被禁用
                Assert.IsFalse(btnCopy.Enabled, "复制按钮初始应被禁用");
                Assert.IsFalse(btnDeactivate.Enabled, "作废按钮初始应被禁用");
                Assert.IsFalse(btnDelete.Enabled, "删除按钮初始应被禁用");
                
                // 选择有效邮箱
                lstEmails.SelectedIndex = 0; // 选择***************
                
                // 手动触发选择变化事件
                var method = typeof(MainForm).GetMethod("LstEmails_SelectedIndexChanged", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                method?.Invoke(form, new object[] { lstEmails, EventArgs.Empty });
                
                Assert.IsTrue(btnCopy.Enabled, "选择有效邮箱后复制按钮应启用");
                Assert.IsTrue(btnDeactivate.Enabled, "选择有效邮箱后作废按钮应启用");
                Assert.IsTrue(btnDelete.Enabled, "选择有效邮箱后删除按钮应启用");
                
                // 选择已作废邮箱
                lstEmails.SelectedIndex = 1; // 选择********************
                method?.Invoke(form, new object[] { lstEmails, EventArgs.Empty });
                
                Assert.IsTrue(btnCopy.Enabled, "选择已作废邮箱后复制按钮应启用");
                Assert.IsFalse(btnDeactivate.Enabled, "选择已作废邮箱后作废按钮应禁用");
                Assert.IsTrue(btnDelete.Enabled, "选择已作废邮箱后删除按钮应启用");
            }
        }

        [TestMethod]
        public void TestGetSelectedEmailRecord()
        {
            var dataService = new DataService();
            var testEmails = new List<EmailRecord>
            {
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Username = "first",
                    Domain = "test.com",
                    CreatedDate = DateTime.Now,
                    Status = EmailStatus.Active
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Username = "second",
                    Domain = "test.com",
                    CreatedDate = DateTime.Now.AddMinutes(-1),
                    Status = EmailStatus.Active
                }
            };

            dataService.SaveEmails(testEmails);

            using (var form = new MainForm())
            {
                var lstEmails = GetPrivateField<ListBox>(form, "lstEmails");
                
                // 测试未选择时的情况
                var method = typeof(MainForm).GetMethod("GetSelectedEmailRecord", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                var result = method?.Invoke(form, null) as EmailRecord;
                Assert.IsNull(result, "未选择时应返回null");
                
                // 测试选择第一项
                lstEmails.SelectedIndex = 0;
                result = method?.Invoke(form, null) as EmailRecord;
                Assert.IsNotNull(result, "选择后应返回邮箱记录");
                Assert.AreEqual("<EMAIL>", result.Email, "应返回正确的邮箱记录");
                
                // 测试选择第二项
                lstEmails.SelectedIndex = 1;
                result = method?.Invoke(form, null) as EmailRecord;
                Assert.IsNotNull(result, "选择后应返回邮箱记录");
                Assert.AreEqual("<EMAIL>", result.Email, "应返回正确的邮箱记录");
            }
        }

        [TestMethod]
        public void TestRefreshEmailListAfterDataChange()
        {
            var dataService = new DataService();
            
            // 初始为空
            dataService.SaveEmails(new List<EmailRecord>());

            using (var form = new MainForm())
            {
                var lstEmails = GetPrivateField<ListBox>(form, "lstEmails");
                Assert.AreEqual(1, lstEmails.Items.Count, "初始应显示空数据提示");
                
                // 添加邮箱记录
                var newEmail = new EmailRecord
                {
                    Email = "<EMAIL>",
                    Username = "new",
                    Domain = "test.com",
                    CreatedDate = DateTime.Now,
                    Status = EmailStatus.Active
                };
                
                dataService.AddEmail(newEmail);
                
                // 手动调用RefreshEmailList方法
                var refreshMethod = typeof(MainForm).GetMethod("RefreshEmailList", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                // 需要先更新内存中的邮箱历史
                var emailHistoryField = typeof(MainForm).GetField("_emailHistory", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var updatedEmails = dataService.LoadEmails();
                emailHistoryField?.SetValue(form, updatedEmails);
                
                refreshMethod?.Invoke(form, null);
                
                Assert.AreEqual(1, lstEmails.Items.Count, "添加邮箱后应显示1条记录");
                Assert.IsTrue(lstEmails.Items[0].ToString().Contains("<EMAIL>"), "应显示新添加的邮箱");
            }
        }

        private void BackupOriginalFiles()
        {
            if (File.Exists(_originalConfigPath))
            {
                File.Copy(_originalConfigPath, _originalConfigPath + ".backup", true);
                File.Delete(_originalConfigPath);
            }

            if (File.Exists(_originalDataPath))
            {
                File.Copy(_originalDataPath, _originalDataPath + ".backup", true);
                File.Delete(_originalDataPath);
            }
        }

        private void RestoreOriginalFiles()
        {
            if (File.Exists(_originalConfigPath + ".backup"))
            {
                File.Move(_originalConfigPath + ".backup", _originalConfigPath);
            }

            if (File.Exists(_originalDataPath + ".backup"))
            {
                File.Move(_originalDataPath + ".backup", _originalDataPath);
            }
        }

        private void CleanupTestFiles()
        {
            string[] testFiles = { _testConfigPath, _testDataPath, _originalConfigPath, _originalDataPath };
            
            foreach (string file in testFiles)
            {
                if (File.Exists(file))
                {
                    File.Delete(file);
                }
            }
        }

        private T GetPrivateField<T>(object obj, string fieldName)
        {
            var field = obj.GetType().GetField(fieldName, 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (field == null)
            {
                throw new ArgumentException($"Field '{fieldName}' not found");
            }

            return (T)field.GetValue(obj);
        }
    }
}