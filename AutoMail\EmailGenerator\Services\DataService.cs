using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using EmailGenerator.Models;
using Newtonsoft.Json;

namespace EmailGenerator.Services
{
    /// <summary>
    /// 数据服务类，负责邮箱数据的文件存储和管理
    /// </summary>
    public class DataService
    {
        private string _dataFilePath;
        private readonly ConfigurationService _configService;
        private readonly PathManagerService _pathManager;
        private string _fallbackPath;

        /// <summary>
        /// 邮箱数据容器
        /// </summary>
        private class EmailDataContainer
        {
            public List<EmailRecord> Emails { get; set; } = new List<EmailRecord>();
        }

        public DataService()
        {
            _pathManager = new PathManagerService();
            _configService = new ConfigurationService(_pathManager);
            _fallbackPath = _pathManager.GetDefaultDataPath();
            _dataFilePath = GetCurrentDataPath();
        }

        public DataService(string dataFilePath)
        {
            _pathManager = new PathManagerService();
            _configService = new ConfigurationService(_pathManager);
            _fallbackPath = _pathManager.GetDefaultDataPath();
            _dataFilePath = dataFilePath ?? GetCurrentDataPath();
        }

        public DataService(ConfigurationService configService, PathManagerService pathManager)
        {
            _configService = configService ?? throw new ArgumentNullException(nameof(configService));
            _pathManager = pathManager ?? throw new ArgumentNullException(nameof(pathManager));
            _fallbackPath = _pathManager.GetDefaultDataPath();
            _dataFilePath = GetCurrentDataPath();
        }

        /// <summary>
        /// 获取当前数据文件路径
        /// </summary>
        /// <returns>当前数据文件路径</returns>
        private string GetCurrentDataPath()
        {
            try
            {
                return _configService?.GetCurrentDataPath() ?? _fallbackPath;
            }
            catch
            {
                return _fallbackPath;
            }
        }

        /// <summary>
        /// 更新数据文件路径
        /// </summary>
        /// <param name="newPath">新的数据文件路径</param>
        /// <returns>更新是否成功</returns>
        public bool UpdateDataPath(string newPath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(newPath))
                {
                    throw new ArgumentException(ErrorMessages.PATH_INVALID);
                }

                // 验证新路径的有效性
                if (!_pathManager.IsValidPath(newPath) || !_pathManager.IsWritablePath(newPath))
                {
                    throw new InvalidOperationException(ErrorMessages.PATH_NOT_WRITABLE);
                }

                string oldPath = _dataFilePath;
                string newDataFilePath = Path.Combine(newPath, "emails.json");

                // 如果新路径与当前路径相同，直接返回成功
                if (string.Equals(oldPath, newDataFilePath, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }

                // 执行数据迁移
                if (MigrateData(oldPath, newDataFilePath))
                {
                    _dataFilePath = newDataFilePath;
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新数据路径失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 迁移数据到新路径
        /// </summary>
        /// <param name="oldPath">原数据文件路径</param>
        /// <param name="newPath">新数据文件路径</param>
        /// <returns>迁移是否成功</returns>
        private bool MigrateData(string oldPath, string newPath)
        {
            try
            {
                // 确保新路径的目录存在
                string newDirectory = Path.GetDirectoryName(newPath);
                if (!string.IsNullOrEmpty(newDirectory) && !Directory.Exists(newDirectory))
                {
                    if (!_pathManager.CreateDirectoryIfNotExists(newDirectory))
                    {
                        throw new InvalidOperationException(ErrorMessages.PATH_CREATE_FAILED);
                    }
                }

                // 如果原文件不存在，直接在新路径创建空文件
                if (!File.Exists(oldPath))
                {
                    var emptyData = new List<EmailRecord>();
                    return SaveEmailsToPath(emptyData, newPath);
                }

                // 创建备份
                string backupPath = CreateBackup(oldPath);
                if (string.IsNullOrEmpty(backupPath))
                {
                    throw new InvalidOperationException(ErrorMessages.PATH_BACKUP_FAILED);
                }

                try
                {
                    // 加载原数据
                    var emails = LoadEmailsFromPath(oldPath);
                    
                    // 保存到新路径
                    if (SaveEmailsToPath(emails, newPath))
                    {
                        // 迁移成功，删除备份文件
                        try
                        {
                            File.Delete(backupPath);
                        }
                        catch
                        {
                            // 删除备份文件失败不影响迁移结果
                        }
                        return true;
                    }
                    else
                    {
                        // 迁移失败，恢复备份
                        RestoreFromBackup(backupPath, oldPath);
                        throw new InvalidOperationException(ErrorMessages.PATH_MIGRATION_FAILED);
                    }
                }
                catch
                {
                    // 迁移过程中出错，恢复备份
                    RestoreFromBackup(backupPath, oldPath);
                    throw;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据迁移失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建数据文件备份
        /// </summary>
        /// <param name="filePath">要备份的文件路径</param>
        /// <returns>备份文件路径，失败时返回空字符串</returns>
        private string CreateBackup(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return string.Empty;
                }

                string backupPath = filePath + ".backup." + DateTime.Now.ToString("yyyyMMddHHmmss");
                File.Copy(filePath, backupPath, true);
                return backupPath;
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 从备份恢复数据文件
        /// </summary>
        /// <param name="backupPath">备份文件路径</param>
        /// <param name="originalPath">原文件路径</param>
        /// <returns>恢复是否成功</returns>
        private bool RestoreFromBackup(string backupPath, string originalPath)
        {
            try
            {
                if (File.Exists(backupPath))
                {
                    File.Copy(backupPath, originalPath, true);
                    File.Delete(backupPath);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从指定路径加载邮箱数据
        /// </summary>
        /// <param name="filePath">数据文件路径</param>
        /// <returns>邮箱记录列表</returns>
        private List<EmailRecord> LoadEmailsFromPath(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return new List<EmailRecord>();
                }

                string jsonContent = File.ReadAllText(filePath);
                if (string.IsNullOrWhiteSpace(jsonContent))
                {
                    return new List<EmailRecord>();
                }

                var container = JsonConvert.DeserializeObject<EmailDataContainer>(jsonContent);
                return container?.Emails ?? new List<EmailRecord>();
            }
            catch
            {
                return new List<EmailRecord>();
            }
        }

        /// <summary>
        /// 将邮箱数据保存到指定路径
        /// </summary>
        /// <param name="emails">邮箱记录列表</param>
        /// <param name="filePath">数据文件路径</param>
        /// <returns>保存是否成功</returns>
        private bool SaveEmailsToPath(List<EmailRecord> emails, string filePath)
        {
            try
            {
                var container = new EmailDataContainer
                {
                    Emails = emails ?? new List<EmailRecord>()
                };

                string jsonContent = JsonConvert.SerializeObject(container, Formatting.Indented);
                
                // 确保目录存在
                string directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllText(filePath, jsonContent);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 安全执行文件操作，失败时回退到默认路径
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="operation">要执行的操作</param>
        /// <param name="defaultValue">操作失败时的默认返回值</param>
        /// <returns>操作结果</returns>
        private T SafeExecuteWithFallback<T>(Func<string, T> operation, T defaultValue)
        {
            try
            {
                // 首先尝试使用当前配置的路径
                return operation(_dataFilePath);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"使用配置路径操作失败: {ex.Message}，尝试回退到默认路径");
                
                try
                {
                    // 回退到默认路径
                    _dataFilePath = _fallbackPath;
                    return operation(_dataFilePath);
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"回退路径操作也失败: {fallbackEx.Message}");
                    throw;
                }
            }
        }

        /// <summary>
        /// 从JSON文件加载邮箱历史记录包括状态信息（异步版本）
        /// </summary>
        /// <returns>邮箱记录列表</returns>
        /// <exception cref="InvalidOperationException">当文件访问失败时抛出</exception>
        /// <exception cref="JsonException">当JSON解析失败时抛出</exception>
        public async Task<List<EmailRecord>> LoadEmailsAsync()
        {
            return await Task.Run(() => SafeExecuteWithFallback(filePath =>
            {
                try
                {
                    if (!File.Exists(filePath))
                    {
                        // 文件不存在，返回空列表
                        return new List<EmailRecord>();
                    }

                    string jsonContent;
                    try
                    {
                        jsonContent = File.ReadAllText(filePath);
                    }
                    catch (UnauthorizedAccessException)
                    {
                        throw new InvalidOperationException(ErrorMessages.FILE_ACCESS_ERROR);
                    }
                    catch (IOException ex)
                    {
                        throw new InvalidOperationException($"{ErrorMessages.FILE_READ_ERROR}: {ex.Message}");
                    }

                    if (string.IsNullOrWhiteSpace(jsonContent))
                    {
                        return new List<EmailRecord>();
                    }

                    try
                    {
                        var container = JsonConvert.DeserializeObject<EmailDataContainer>(jsonContent);
                        return container?.Emails ?? new List<EmailRecord>();
                    }
                    catch (JsonException)
                    {
                        throw new JsonException(ErrorMessages.DATA_PARSE_ERROR);
                    }
                }
                catch (Exception ex) when (!(ex is InvalidOperationException || ex is JsonException))
                {
                    System.Diagnostics.Debug.WriteLine($"加载邮箱数据失败: {ex.Message}");
                    throw new InvalidOperationException($"{ErrorMessages.DATA_LOAD_FAILED}: {ex.Message}");
                }
            }, new List<EmailRecord>()));
        }

        /// <summary>
        /// 从JSON文件加载邮箱历史记录包括状态信息（同步版本，保持向后兼容）
        /// </summary>
        /// <returns>邮箱记录列表</returns>
        /// <exception cref="InvalidOperationException">当文件访问失败时抛出</exception>
        /// <exception cref="JsonException">当JSON解析失败时抛出</exception>
        public List<EmailRecord> LoadEmails()
        {
            return SafeExecuteWithFallback(filePath =>
            {
                try
                {
                    if (!File.Exists(filePath))
                    {
                        // 文件不存在，返回空列表
                        return new List<EmailRecord>();
                    }

                    string jsonContent;
                    try
                    {
                        jsonContent = File.ReadAllText(filePath);
                    }
                    catch (UnauthorizedAccessException)
                    {
                        throw new InvalidOperationException(ErrorMessages.FILE_ACCESS_ERROR);
                    }
                    catch (IOException ex)
                    {
                        throw new InvalidOperationException($"{ErrorMessages.FILE_READ_ERROR}: {ex.Message}");
                    }

                    if (string.IsNullOrWhiteSpace(jsonContent))
                    {
                        return new List<EmailRecord>();
                    }

                    try
                    {
                        var container = JsonConvert.DeserializeObject<EmailDataContainer>(jsonContent);
                        return container?.Emails ?? new List<EmailRecord>();
                    }
                    catch (JsonException)
                    {
                        throw new JsonException(ErrorMessages.DATA_PARSE_ERROR);
                    }
                }
                catch (Exception ex) when (!(ex is InvalidOperationException || ex is JsonException))
                {
                    System.Diagnostics.Debug.WriteLine($"加载邮箱数据失败: {ex.Message}");
                    throw new InvalidOperationException($"{ErrorMessages.DATA_LOAD_FAILED}: {ex.Message}");
                }
            }, new List<EmailRecord>());
        }

        /// <summary>
        /// 将邮箱列表保存到JSON文件（异步版本）
        /// </summary>
        /// <param name="emails">要保存的邮箱列表</param>
        /// <returns>保存是否成功</returns>
        /// <exception cref="InvalidOperationException">当文件操作失败时抛出</exception>
        /// <exception cref="JsonException">当JSON序列化失败时抛出</exception>
        public async Task<bool> SaveEmailsAsync(List<EmailRecord> emails)
        {
            return await Task.Run(() => SafeExecuteWithFallback(filePath =>
            {
                try
                {
                    if (emails == null)
                    {
                        emails = new List<EmailRecord>();
                    }

                    var container = new EmailDataContainer
                    {
                        Emails = emails
                    };

                    string jsonContent;
                    try
                    {
                        jsonContent = JsonConvert.SerializeObject(container, Formatting.Indented);
                    }
                    catch (JsonException)
                    {
                        throw new JsonException(ErrorMessages.DATA_PARSE_ERROR);
                    }
                    
                    // 确保目录存在
                    string directory = Path.GetDirectoryName(filePath);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        try
                        {
                            Directory.CreateDirectory(directory);
                        }
                        catch (UnauthorizedAccessException)
                        {
                            throw new InvalidOperationException(ErrorMessages.FILE_ACCESS_ERROR);
                        }
                        catch (IOException ex)
                        {
                            throw new InvalidOperationException($"{ErrorMessages.FILE_CREATE_ERROR}: {ex.Message}");
                        }
                    }

                    try
                    {
                        File.WriteAllText(filePath, jsonContent);
                        return true;
                    }
                    catch (UnauthorizedAccessException)
                    {
                        throw new InvalidOperationException(ErrorMessages.FILE_ACCESS_ERROR);
                    }
                    catch (IOException ex)
                    {
                        throw new InvalidOperationException($"{ErrorMessages.FILE_WRITE_ERROR}: {ex.Message}");
                    }
                }
                catch (Exception ex) when (!(ex is InvalidOperationException || ex is JsonException))
                {
                    System.Diagnostics.Debug.WriteLine($"保存邮箱数据失败: {ex.Message}");
                    throw new InvalidOperationException($"{ErrorMessages.DATA_SAVE_FAILED}: {ex.Message}");
                }
            }, false));
        }

        /// <summary>
        /// 将邮箱列表保存到JSON文件（同步版本，保持向后兼容）
        /// </summary>
        /// <param name="emails">要保存的邮箱列表</param>
        /// <returns>保存是否成功</returns>
        /// <exception cref="InvalidOperationException">当文件操作失败时抛出</exception>
        /// <exception cref="JsonException">当JSON序列化失败时抛出</exception>
        public bool SaveEmails(List<EmailRecord> emails)
        {
            return SafeExecuteWithFallback(filePath =>
            {
                try
                {
                    if (emails == null)
                    {
                        emails = new List<EmailRecord>();
                    }

                    var container = new EmailDataContainer
                    {
                        Emails = emails
                    };

                    string jsonContent;
                    try
                    {
                        jsonContent = JsonConvert.SerializeObject(container, Formatting.Indented);
                    }
                    catch (JsonException)
                    {
                        throw new JsonException(ErrorMessages.DATA_PARSE_ERROR);
                    }
                    
                    // 确保目录存在
                    string directory = Path.GetDirectoryName(filePath);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        try
                        {
                            Directory.CreateDirectory(directory);
                        }
                        catch (UnauthorizedAccessException)
                        {
                            throw new InvalidOperationException(ErrorMessages.FILE_ACCESS_ERROR);
                        }
                        catch (IOException ex)
                        {
                            throw new InvalidOperationException($"{ErrorMessages.FILE_CREATE_ERROR}: {ex.Message}");
                        }
                    }

                    try
                    {
                        File.WriteAllText(filePath, jsonContent);
                        return true;
                    }
                    catch (UnauthorizedAccessException)
                    {
                        throw new InvalidOperationException(ErrorMessages.FILE_ACCESS_ERROR);
                    }
                    catch (IOException ex)
                    {
                        throw new InvalidOperationException($"{ErrorMessages.FILE_WRITE_ERROR}: {ex.Message}");
                    }
                }
                catch (Exception ex) when (!(ex is InvalidOperationException || ex is JsonException))
                {
                    System.Diagnostics.Debug.WriteLine($"保存邮箱数据失败: {ex.Message}");
                    throw new InvalidOperationException($"{ErrorMessages.DATA_SAVE_FAILED}: {ex.Message}");
                }
            }, false);
        }

        /// <summary>
        /// 添加新的邮箱记录（异步版本）
        /// </summary>
        /// <param name="email">要添加的邮箱记录</param>
        /// <returns>添加是否成功</returns>
        /// <exception cref="ArgumentException">当邮箱记录无效时抛出</exception>
        /// <exception cref="InvalidOperationException">当邮箱已存在或操作失败时抛出</exception>
        public async Task<bool> AddEmailAsync(EmailRecord email)
        {
            try
            {
                if (email == null)
                {
                    throw new ArgumentException("邮箱记录不能为空");
                }

                if (string.IsNullOrWhiteSpace(email.Email))
                {
                    throw new ArgumentException(ErrorMessages.EMAIL_INVALID);
                }

                var emails = await LoadEmailsAsync();
                
                // 检查是否已存在相同的邮箱
                if (emails.Any(e => e.Email.Equals(email.Email, StringComparison.OrdinalIgnoreCase)))
                {
                    throw new InvalidOperationException(ErrorMessages.EMAIL_ALREADY_EXISTS);
                }

                emails.Add(email);
                return await SaveEmailsAsync(emails);
            }
            catch (Exception ex) when (!(ex is ArgumentException || ex is InvalidOperationException || ex is JsonException))
            {
                System.Diagnostics.Debug.WriteLine($"添加邮箱失败: {ex.Message}");
                throw new InvalidOperationException($"{ErrorMessages.EMAIL_SAVE_FAILED}: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加新的邮箱记录（同步版本，保持向后兼容）
        /// </summary>
        /// <param name="email">要添加的邮箱记录</param>
        /// <returns>添加是否成功</returns>
        /// <exception cref="ArgumentException">当邮箱记录无效时抛出</exception>
        /// <exception cref="InvalidOperationException">当邮箱已存在或操作失败时抛出</exception>
        public bool AddEmail(EmailRecord email)
        {
            try
            {
                if (email == null)
                {
                    throw new ArgumentException("邮箱记录不能为空");
                }

                if (string.IsNullOrWhiteSpace(email.Email))
                {
                    throw new ArgumentException(ErrorMessages.EMAIL_INVALID);
                }

                var emails = LoadEmails();
                
                // 检查是否已存在相同的邮箱
                if (emails.Any(e => e.Email.Equals(email.Email, StringComparison.OrdinalIgnoreCase)))
                {
                    throw new InvalidOperationException(ErrorMessages.EMAIL_ALREADY_EXISTS);
                }

                emails.Add(email);
                return SaveEmails(emails);
            }
            catch (Exception ex) when (!(ex is ArgumentException || ex is InvalidOperationException || ex is JsonException))
            {
                System.Diagnostics.Debug.WriteLine($"添加邮箱失败: {ex.Message}");
                throw new InvalidOperationException($"{ErrorMessages.EMAIL_SAVE_FAILED}: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除指定的邮箱记录（异步版本）
        /// </summary>
        /// <param name="email">要删除的邮箱地址</param>
        /// <returns>删除是否成功</returns>
        /// <exception cref="ArgumentException">当邮箱地址无效时抛出</exception>
        /// <exception cref="InvalidOperationException">当邮箱不存在或删除失败时抛出</exception>
        public async Task<bool> DeleteEmailAsync(string email)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(email))
                {
                    throw new ArgumentException(ErrorMessages.EMAIL_INVALID);
                }

                var emails = await LoadEmailsAsync();
                var emailToRemove = emails.FirstOrDefault(e => e.Email.Equals(email, StringComparison.OrdinalIgnoreCase));
                
                if (emailToRemove == null)
                {
                    throw new InvalidOperationException(ErrorMessages.EMAIL_NOT_FOUND);
                }

                emails.Remove(emailToRemove);
                return await SaveEmailsAsync(emails);
            }
            catch (Exception ex) when (!(ex is ArgumentException || ex is InvalidOperationException || ex is JsonException))
            {
                System.Diagnostics.Debug.WriteLine($"删除邮箱失败: {ex.Message}");
                throw new InvalidOperationException($"{ErrorMessages.EMAIL_DELETE_FAILED}: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除指定的邮箱记录（同步版本，保持向后兼容）
        /// </summary>
        /// <param name="email">要删除的邮箱地址</param>
        /// <returns>删除是否成功</returns>
        /// <exception cref="ArgumentException">当邮箱地址无效时抛出</exception>
        /// <exception cref="InvalidOperationException">当邮箱不存在或删除失败时抛出</exception>
        public bool DeleteEmail(string email)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(email))
                {
                    throw new ArgumentException(ErrorMessages.EMAIL_INVALID);
                }

                var emails = LoadEmails();
                var emailToRemove = emails.FirstOrDefault(e => e.Email.Equals(email, StringComparison.OrdinalIgnoreCase));
                
                if (emailToRemove == null)
                {
                    throw new InvalidOperationException(ErrorMessages.EMAIL_NOT_FOUND);
                }

                emails.Remove(emailToRemove);
                return SaveEmails(emails);
            }
            catch (Exception ex) when (!(ex is ArgumentException || ex is InvalidOperationException || ex is JsonException))
            {
                System.Diagnostics.Debug.WriteLine($"删除邮箱失败: {ex.Message}");
                throw new InvalidOperationException($"{ErrorMessages.EMAIL_DELETE_FAILED}: {ex.Message}");
            }
        }

        /// <summary>
        /// 作废指定的邮箱记录（异步版本）
        /// </summary>
        /// <param name="email">要作废的邮箱地址</param>
        /// <returns>作废是否成功</returns>
        public async Task<bool> DeactivateEmailAsync(string email)
        {
            return await UpdateEmailStatusAsync(email, EmailStatus.Deactivated);
        }

        /// <summary>
        /// 作废指定的邮箱记录（同步版本，保持向后兼容）
        /// </summary>
        /// <param name="email">要作废的邮箱地址</param>
        /// <returns>作废是否成功</returns>
        public bool DeactivateEmail(string email)
        {
            return UpdateEmailStatus(email, EmailStatus.Deactivated);
        }

        /// <summary>
        /// 更新邮箱状态（异步版本）
        /// </summary>
        /// <param name="email">要更新的邮箱地址</param>
        /// <param name="status">新的状态</param>
        /// <returns>更新是否成功</returns>
        /// <exception cref="ArgumentException">当邮箱地址无效时抛出</exception>
        /// <exception cref="InvalidOperationException">当邮箱不存在或更新失败时抛出</exception>
        public async Task<bool> UpdateEmailStatusAsync(string email, EmailStatus status)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(email))
                {
                    throw new ArgumentException(ErrorMessages.EMAIL_INVALID);
                }

                var emails = await LoadEmailsAsync();
                var emailToUpdate = emails.FirstOrDefault(e => e.Email.Equals(email, StringComparison.OrdinalIgnoreCase));
                
                if (emailToUpdate == null)
                {
                    throw new InvalidOperationException(ErrorMessages.EMAIL_NOT_FOUND);
                }

                emailToUpdate.Status = status;
                if (status == EmailStatus.Deactivated)
                {
                    emailToUpdate.DeactivatedDate = DateTime.Now;
                }
                else if (status == EmailStatus.Active)
                {
                    emailToUpdate.DeactivatedDate = null;
                }

                return await SaveEmailsAsync(emails);
            }
            catch (Exception ex) when (!(ex is ArgumentException || ex is InvalidOperationException || ex is JsonException))
            {
                System.Diagnostics.Debug.WriteLine($"更新邮箱状态失败: {ex.Message}");
                throw new InvalidOperationException($"{ErrorMessages.EMAIL_DEACTIVATE_FAILED}: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新邮箱状态（同步版本，保持向后兼容）
        /// </summary>
        /// <param name="email">要更新的邮箱地址</param>
        /// <param name="status">新的状态</param>
        /// <returns>更新是否成功</returns>
        /// <exception cref="ArgumentException">当邮箱地址无效时抛出</exception>
        /// <exception cref="InvalidOperationException">当邮箱不存在或更新失败时抛出</exception>
        public bool UpdateEmailStatus(string email, EmailStatus status)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(email))
                {
                    throw new ArgumentException(ErrorMessages.EMAIL_INVALID);
                }

                var emails = LoadEmails();
                var emailToUpdate = emails.FirstOrDefault(e => e.Email.Equals(email, StringComparison.OrdinalIgnoreCase));
                
                if (emailToUpdate == null)
                {
                    throw new InvalidOperationException(ErrorMessages.EMAIL_NOT_FOUND);
                }

                emailToUpdate.Status = status;
                if (status == EmailStatus.Deactivated)
                {
                    emailToUpdate.DeactivatedDate = DateTime.Now;
                }
                else if (status == EmailStatus.Active)
                {
                    emailToUpdate.DeactivatedDate = null;
                }

                return SaveEmails(emails);
            }
            catch (Exception ex) when (!(ex is ArgumentException || ex is InvalidOperationException || ex is JsonException))
            {
                System.Diagnostics.Debug.WriteLine($"更新邮箱状态失败: {ex.Message}");
                throw new InvalidOperationException($"{ErrorMessages.EMAIL_DEACTIVATE_FAILED}: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查邮箱是否已存在
        /// </summary>
        /// <param name="email">要检查的邮箱地址</param>
        /// <returns>邮箱是否存在</returns>
        public bool EmailExists(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                return false;
            }

            var emails = LoadEmails();
            return emails.Any(e => e.Email.Equals(email, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取所有有效的邮箱地址
        /// </summary>
        /// <returns>有效邮箱地址列表</returns>
        public List<string> GetActiveEmails()
        {
            var emails = LoadEmails();
            return emails.Where(e => e.Status == EmailStatus.Active)
                        .Select(e => e.Email)
                        .ToList();
        }

        /// <summary>
        /// 获取当前使用的数据文件路径
        /// </summary>
        /// <returns>当前数据文件路径</returns>
        public string GetCurrentDataFilePath()
        {
            return _dataFilePath;
        }

        /// <summary>
        /// 刷新数据文件路径（从配置服务重新获取）
        /// </summary>
        /// <returns>刷新是否成功</returns>
        public bool RefreshDataPath()
        {
            try
            {
                string newPath = GetCurrentDataPath();
                if (!string.Equals(_dataFilePath, newPath, StringComparison.OrdinalIgnoreCase))
                {
                    _dataFilePath = newPath;
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证当前数据文件路径是否可访问
        /// </summary>
        /// <returns>路径是否可访问</returns>
        public bool ValidateCurrentPath()
        {
            try
            {
                string directory = Path.GetDirectoryName(_dataFilePath);
                return !string.IsNullOrEmpty(directory) && 
                       _pathManager.IsValidPath(directory) && 
                       _pathManager.IsWritablePath(directory);
            }
            catch
            {
                return false;
            }
        }
    }
}