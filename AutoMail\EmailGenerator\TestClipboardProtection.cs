using System;
using System.Windows.Forms;
using EmailGenerator.Helpers;

namespace EmailGenerator
{
    /// <summary>
    /// 剪贴板保护功能测试程序
    /// 用于验证MessageBox不会覆盖剪贴板中的邮箱地址
    /// </summary>
    public class TestClipboardProtection
    {
        [STAThread]
        public static void TestMain()
        {
            try
            {
                Console.WriteLine("=== 剪贴板保护功能测试 ===");
                
                // 测试邮箱地址
                string testEmail = "<EMAIL>";
                
                Console.WriteLine($"1. 设置测试邮箱到剪贴板: {testEmail}");
                Clipboard.SetText(testEmail);
                
                Console.WriteLine("2. 验证剪贴板内容...");
                string clipboardBefore = Clipboard.GetText();
                Console.WriteLine($"   剪贴板内容: {clipboardBefore}");
                
                if (clipboardBefore == testEmail)
                {
                    Console.WriteLine("   ✅ 剪贴板设置成功");
                }
                else
                {
                    Console.WriteLine("   ❌ 剪贴板设置失败");
                    return;
                }
                
                Console.WriteLine("3. 显示成功消息（带剪贴板保护）...");
                Console.WriteLine("   注意：MessageBox显示时，请尝试按 Ctrl+C");
                
                // 显示成功消息（这里会触发剪贴板保护机制）
                MessageHelper.ShowSuccessMessage("邮箱地址已复制到剪贴板！\n\n请在此对话框中按 Ctrl+C 测试剪贴板保护功能。", "复制成功");
                
                Console.WriteLine("4. 验证剪贴板内容是否被保护...");
                string clipboardAfter = Clipboard.GetText();
                Console.WriteLine($"   剪贴板内容: {clipboardAfter}");
                
                if (clipboardAfter == testEmail)
                {
                    Console.WriteLine("   ✅ 剪贴板保护成功！邮箱地址未被覆盖");
                }
                else
                {
                    Console.WriteLine("   ❌ 剪贴板保护失败！内容被覆盖了");
                    Console.WriteLine($"   预期: {testEmail}");
                    Console.WriteLine($"   实际: {clipboardAfter}");
                }
                
                Console.WriteLine("\n=== 测试完成 ===");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
        }
    }
}