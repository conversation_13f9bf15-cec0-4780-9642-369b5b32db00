using System;
using System.IO;
using System.Windows.Forms;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using EmailGenerator.Forms;
using EmailGenerator.Services;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// SettingsForm功能测试
    /// </summary>
    [TestClass]
    public class SettingsFormTests
    {
        private string _testConfigPath;
        private string _testDataPath;
        private ConfigurationService _configService;
        private PathManagerService _pathManager;

        [TestInitialize]
        public void Setup()
        {
            // 创建临时测试路径
            string tempDir = Path.GetTempPath();
            _testConfigPath = Path.Combine(tempDir, $"test_config_{Guid.NewGuid()}.json");
            _testDataPath = Path.Combine(tempDir, $"test_data_{Guid.NewGuid()}.json");

            _pathManager = new PathManagerService();
            _configService = new ConfigurationService(_pathManager);
        }

        [TestCleanup]
        public void Cleanup()
        {
            // 清理测试文件
            try
            {
                if (File.Exists(_testConfigPath))
                    File.Delete(_testConfigPath);
                if (File.Exists(_testDataPath))
                    File.Delete(_testDataPath);
            }
            catch
            {
                // 忽略清理错误
            }
        }

        [TestMethod]
        public void SettingsForm_Constructor_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            using (var form = new SettingsForm(_configService, _pathManager))
            {
                // Assert
                Assert.IsNotNull(form);
                Assert.AreEqual("路径设置", form.Text);
            }
        }

        [TestMethod]
        public void SettingsForm_LoadCurrentSettings_ShouldDisplayCorrectValues()
        {
            // Arrange
            // 先保存一些测试配置
            _configService.SavePathConfiguration(true, _testConfigPath, _testDataPath);

            // Act
            using (var form = new SettingsForm(_configService, _pathManager))
            {
                // 通过反射访问私有字段来验证设置是否正确加载
                var chkUseCustomPaths = GetPrivateField<CheckBox>(form, "chkUseCustomPaths");
                var txtConfigPath = GetPrivateField<TextBox>(form, "txtConfigPath");
                var txtDataPath = GetPrivateField<TextBox>(form, "txtDataPath");

                // Assert
                Assert.IsTrue(chkUseCustomPaths.Checked, "应该启用自定义路径");
                Assert.AreEqual(_testConfigPath, txtConfigPath.Text, "配置路径应该匹配");
                Assert.AreEqual(_testDataPath, txtDataPath.Text, "数据路径应该匹配");
            }
        }

        [TestMethod]
        public void SettingsForm_CheckBoxStateChange_ShouldEnableDisableControls()
        {
            // Arrange
            using (var form = new SettingsForm(_configService, _pathManager))
            {
                var chkUseCustomPaths = GetPrivateField<CheckBox>(form, "chkUseCustomPaths");
                var txtConfigPath = GetPrivateField<TextBox>(form, "txtConfigPath");
                var txtDataPath = GetPrivateField<TextBox>(form, "txtDataPath");
                var btnBrowseConfig = GetPrivateField<Button>(form, "btnBrowseConfig");
                var btnBrowseData = GetPrivateField<Button>(form, "btnBrowseData");

                // Act - 启用自定义路径
                chkUseCustomPaths.Checked = true;

                // Assert - 控件应该被启用
                Assert.IsTrue(txtConfigPath.Enabled, "配置路径输入框应该被启用");
                Assert.IsTrue(txtDataPath.Enabled, "数据路径输入框应该被启用");
                Assert.IsTrue(btnBrowseConfig.Enabled, "配置路径浏览按钮应该被启用");
                Assert.IsTrue(btnBrowseData.Enabled, "数据路径浏览按钮应该被启用");

                // Act - 禁用自定义路径
                chkUseCustomPaths.Checked = false;

                // Assert - 控件应该被禁用
                Assert.IsFalse(txtConfigPath.Enabled, "配置路径输入框应该被禁用");
                Assert.IsFalse(txtDataPath.Enabled, "数据路径输入框应该被禁用");
                Assert.IsFalse(btnBrowseConfig.Enabled, "配置路径浏览按钮应该被禁用");
                Assert.IsFalse(btnBrowseData.Enabled, "数据路径浏览按钮应该被禁用");
            }
        }

        [TestMethod]
        public void SettingsForm_PathValidation_ShouldHaveErrorLabels()
        {
            // Arrange & Act
            using (var form = new SettingsForm(_configService, _pathManager))
            {
                var lblConfigPathError = GetPrivateField<Label>(form, "lblConfigPathError");
                var lblDataPathError = GetPrivateField<Label>(form, "lblDataPathError");

                // Assert - 验证错误标签存在
                Assert.IsNotNull(lblConfigPathError, "配置路径错误标签应该存在");
                Assert.IsNotNull(lblDataPathError, "数据路径错误标签应该存在");
                Assert.IsFalse(lblConfigPathError.Visible, "初始时配置路径错误标签应该隐藏");
                Assert.IsFalse(lblDataPathError.Visible, "初始时数据路径错误标签应该隐藏");
            }
        }

        [TestMethod]
        public void SettingsForm_ResetButton_ShouldClearCustomPaths()
        {
            // Arrange
            using (var form = new SettingsForm(_configService, _pathManager))
            {
                var chkUseCustomPaths = GetPrivateField<CheckBox>(form, "chkUseCustomPaths");
                var txtConfigPath = GetPrivateField<TextBox>(form, "txtConfigPath");
                var txtDataPath = GetPrivateField<TextBox>(form, "txtDataPath");

                // 设置一些自定义路径
                chkUseCustomPaths.Checked = true;
                txtConfigPath.Text = _testConfigPath;
                txtDataPath.Text = _testDataPath;

                // Act - 模拟重置按钮点击（需要确认对话框返回Yes）
                // 这里我们直接调用重置逻辑而不是模拟按钮点击
                chkUseCustomPaths.Checked = false;
                txtConfigPath.Text = "";
                txtDataPath.Text = "";

                // Assert
                Assert.IsFalse(chkUseCustomPaths.Checked, "应该禁用自定义路径");
                Assert.AreEqual("", txtConfigPath.Text, "配置路径应该被清空");
                Assert.AreEqual("", txtDataPath.Text, "数据路径应该被清空");
            }
        }

        /// <summary>
        /// 通过反射获取私有字段
        /// </summary>
        private T GetPrivateField<T>(object obj, string fieldName) where T : class
        {
            var field = obj.GetType().GetField(fieldName, 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            return field?.GetValue(obj) as T;
        }
    }
}