using Microsoft.VisualStudio.TestTools.UnitTesting;
using EmailGenerator.Services;
using EmailGenerator.Models;
using EmailGenerator.Forms;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Windows.Forms;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// 端到端集成测试类，验证完整的应用程序流程
    /// </summary>
    [TestClass]
    public class EndToEndIntegrationTests
    {
        private string testConfigPath;
        private string testDataPath;
        private ConfigurationService configService;
        private DataService dataService;
        private EmailService emailService;

        [TestInitialize]
        public void Setup()
        {
            // 创建测试专用的临时文件路径
            testConfigPath = Path.Combine(Path.GetTempPath(), $"test_config_{Guid.NewGuid()}.json");
            testDataPath = Path.Combine(Path.GetTempPath(), $"test_emails_{Guid.NewGuid()}.json");
            
            // 初始化服务实例
            configService = new ConfigurationService();
            dataService = new DataService(testDataPath);
            emailService = new EmailService(dataService);
        }

        [TestCleanup]
        public void Cleanup()
        {
            // 清理测试文件
            if (File.Exists(testConfigPath))
                File.Delete(testConfigPath);
            if (File.Exists(testDataPath))
                File.Delete(testDataPath);
        }

        /// <summary>
        /// 测试完整的域名设置到邮箱生成流程
        /// 需求: 1.1, 1.2, 1.3, 1.4, 2.1, 2.2, 2.3, 2.4, 2.5
        /// </summary>
        [TestMethod]
        public void TestCompleteWorkflow_DomainSettingToEmailGeneration()
        {
            // 步骤1: 设置域名
            string testDomain = "test-domain.com";
            configService.SaveDomain(testDomain);
            
            // 验证域名保存成功
            string savedDomain = configService.LoadDomain();
            Assert.AreEqual(testDomain, savedDomain, "域名应该正确保存和加载");

            // 步骤2: 生成第一个邮箱
            var existingEmails = dataService.LoadEmails();
            string firstEmail = emailService.GenerateUniqueEmail(testDomain, existingEmails.ConvertAll(e => e.Email));
            
            Assert.IsNotNull(firstEmail, "应该成功生成邮箱");
            Assert.IsTrue(firstEmail.EndsWith($"@{testDomain}"), "生成的邮箱应该使用指定的域名");

            // 步骤3: 保存邮箱到历史记录
            var emailRecord = new EmailRecord
            {
                Email = firstEmail,
                CreatedDate = DateTime.Now,
                Domain = testDomain,
                Username = firstEmail.Split('@')[0],
                Status = EmailStatus.Active
            };
            dataService.AddEmail(emailRecord);

            // 步骤4: 验证邮箱已保存
            var savedEmails = dataService.LoadEmails();
            Assert.AreEqual(1, savedEmails.Count, "应该有一个保存的邮箱");
            Assert.AreEqual(firstEmail, savedEmails[0].Email, "保存的邮箱应该匹配生成的邮箱");

            // 步骤5: 生成第二个邮箱，验证唯一性
            string secondEmail = emailService.GenerateUniqueEmail(testDomain, savedEmails.ConvertAll(e => e.Email));
            Assert.AreNotEqual(firstEmail, secondEmail, "第二个邮箱应该与第一个不同");
        }

        /// <summary>
        /// 测试数据持久化在程序重启后的完整性
        /// 需求: 4.1, 4.2, 4.3, 4.4, 4.5
        /// </summary>
        [TestMethod]
        public void TestDataPersistenceAfterRestart()
        {
            // 模拟第一次程序运行
            string testDomain = "persistence-test.com";
            configService.SaveDomain(testDomain);

            // 生成并保存多个邮箱
            var emails = new List<EmailRecord>();
            for (int i = 0; i < 3; i++)
            {
                var existingEmails = emails.ConvertAll(e => e.Email);
                string newEmail = emailService.GenerateUniqueEmail(testDomain, existingEmails);
                var record = new EmailRecord
                {
                    Email = newEmail,
                    CreatedDate = DateTime.Now.AddMinutes(-i),
                    Domain = testDomain,
                    Username = newEmail.Split('@')[0],
                    Status = i == 1 ? EmailStatus.Deactivated : EmailStatus.Active,
                    DeactivatedDate = i == 1 ? DateTime.Now : null
                };
                emails.Add(record);
                dataService.AddEmail(record);
            }

            // 模拟程序重启 - 创建新的服务实例
            var newConfigService = new ConfigurationService();
            var newDataService = new DataService(testDataPath);

            // 验证配置数据持久化
            string loadedDomain = newConfigService.LoadDomain();
            Assert.AreEqual(testDomain, loadedDomain, "重启后应该能正确加载域名配置");

            // 验证邮箱数据持久化
            var loadedEmails = newDataService.LoadEmails();
            Assert.AreEqual(3, loadedEmails.Count, "重启后应该能加载所有邮箱记录");

            // 验证邮箱状态持久化
            var deactivatedEmail = loadedEmails.Find(e => e.Status == EmailStatus.Deactivated);
            Assert.IsNotNull(deactivatedEmail, "应该有一个失效的邮箱");
            Assert.IsNotNull(deactivatedEmail.DeactivatedDate, "失效邮箱应该有失效日期");

            var activeEmails = loadedEmails.FindAll(e => e.Status == EmailStatus.Active);
            Assert.AreEqual(2, activeEmails.Count, "应该有两个有效的邮箱");
        }

        /// <summary>
        /// 测试边界条件和异常情况
        /// 需求: 5.3, 5.4
        /// </summary>
        [TestMethod]
        public void TestBoundaryConditionsAndExceptions()
        {
            // 测试无效域名
            Assert.ThrowsException<ArgumentException>(() =>
            {
                emailService.GenerateUniqueEmail("", new List<string>());
            }, "空域名应该抛出异常");

            Assert.ThrowsException<ArgumentException>(() =>
            {
                emailService.GenerateUniqueEmail("invalid-domain", new List<string>());
            }, "无效域名格式应该抛出异常");

            // 测试文件不存在的情况
            string nonExistentPath = Path.Combine(Path.GetTempPath(), $"non_existent_{Guid.NewGuid()}.json");
            var dataServiceWithNonExistentFile = new DataService(nonExistentPath);
            
            // 应该能处理文件不存在的情况，返回空列表
            var emails = dataServiceWithNonExistentFile.LoadEmails();
            Assert.IsNotNull(emails, "不存在的文件应该返回空列表而不是null");
            Assert.AreEqual(0, emails.Count, "不存在的文件应该返回空列表");

            // 测试大量邮箱生成的性能
            string testDomain = "performance-test.com";
            var existingEmails = new List<string>();
            
            // 生成100个邮箱，验证性能和唯一性
            var startTime = DateTime.Now;
            for (int i = 0; i < 100; i++)
            {
                string email = emailService.GenerateUniqueEmail(testDomain, existingEmails);
                Assert.IsFalse(existingEmails.Contains(email), $"第{i+1}个邮箱应该是唯一的");
                existingEmails.Add(email);
            }
            var endTime = DateTime.Now;
            
            // 验证性能（100个邮箱生成应该在合理时间内完成）
            var duration = endTime - startTime;
            Assert.IsTrue(duration.TotalSeconds < 10, "生成100个邮箱应该在10秒内完成");

            // 清理测试文件
            if (File.Exists(nonExistentPath))
                File.Delete(nonExistentPath);
        }

        /// <summary>
        /// 测试邮箱管理操作的完整流程
        /// 需求: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8
        /// </summary>
        [TestMethod]
        public void TestEmailManagementWorkflow()
        {
            string testDomain = "management-test.com";
            
            // 生成并添加测试邮箱
            var testEmails = new List<EmailRecord>();
            for (int i = 0; i < 5; i++)
            {
                var existingEmails = testEmails.ConvertAll(e => e.Email);
                string email = emailService.GenerateUniqueEmail(testDomain, existingEmails);
                var record = new EmailRecord
                {
                    Email = email,
                    CreatedDate = DateTime.Now.AddMinutes(-i),
                    Domain = testDomain,
                    Username = email.Split('@')[0],
                    Status = EmailStatus.Active
                };
                testEmails.Add(record);
                dataService.AddEmail(record);
            }

            // 验证邮箱列表加载
            var loadedEmails = dataService.LoadEmails();
            Assert.AreEqual(5, loadedEmails.Count, "应该加载所有5个邮箱");

            // 测试邮箱作废功能
            string emailToDeactivate = testEmails[0].Email;
            dataService.DeactivateEmail(emailToDeactivate);
            
            var updatedEmails = dataService.LoadEmails();
            var deactivatedEmail = updatedEmails.Find(e => e.Email == emailToDeactivate);
            Assert.IsNotNull(deactivatedEmail, "应该找到被作废的邮箱");
            Assert.AreEqual(EmailStatus.Deactivated, deactivatedEmail.Status, "邮箱状态应该是失效");
            Assert.IsNotNull(deactivatedEmail.DeactivatedDate, "失效邮箱应该有失效日期");

            // 测试邮箱删除功能
            string emailToDelete = testEmails[1].Email;
            dataService.DeleteEmail(emailToDelete);
            
            var emailsAfterDeletion = dataService.LoadEmails();
            Assert.AreEqual(4, emailsAfterDeletion.Count, "删除后应该剩余4个邮箱");
            Assert.IsNull(emailsAfterDeletion.Find(e => e.Email == emailToDelete), "被删除的邮箱不应该存在");

            // 验证状态区分显示
            var activeEmails = emailsAfterDeletion.FindAll(e => e.Status == EmailStatus.Active);
            var deactivatedEmails = emailsAfterDeletion.FindAll(e => e.Status == EmailStatus.Deactivated);
            
            Assert.AreEqual(3, activeEmails.Count, "应该有3个有效邮箱");
            Assert.AreEqual(1, deactivatedEmails.Count, "应该有1个失效邮箱");
        }

        /// <summary>
        /// 测试配置服务的完整功能
        /// 需求: 1.1, 1.2, 1.3, 1.4
        /// </summary>
        [TestMethod]
        public void TestConfigurationServiceComplete()
        {
            // 测试默认状态
            string initialDomain = configService.LoadDomain();
            Assert.IsTrue(string.IsNullOrEmpty(initialDomain) || initialDomain == "example.com", 
                "初始域名应该为空或默认值");

            // 测试域名保存和加载
            string[] testDomains = { "test1.com", "test2.org", "test3.net" };
            
            foreach (string domain in testDomains)
            {
                configService.SaveDomain(domain);
                string loadedDomain = configService.LoadDomain();
                Assert.AreEqual(domain, loadedDomain, $"域名 {domain} 应该正确保存和加载");
            }

            // 测试域名格式验证
            Assert.IsTrue(emailService.IsValidDomain("valid-domain.com"), "有效域名应该通过验证");
            Assert.IsFalse(emailService.IsValidDomain(""), "空域名应该验证失败");
            Assert.IsFalse(emailService.IsValidDomain("invalid"), "无效域名应该验证失败");
        }
    }
}