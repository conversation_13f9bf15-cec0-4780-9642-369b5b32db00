using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using EmailGenerator.Models;
using EmailGenerator.Services;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// EmailService的单元测试
    /// </summary>
    [TestClass]
    public class EmailServiceTests
    {
        private EmailService _emailService;
        private DataService _dataService;
        private string _testDataPath;

        [TestInitialize]
        public void Setup()
        {
            _testDataPath = Path.Combine(Path.GetTempPath(), "test_emails_service.json");
            _dataService = new DataService(_testDataPath);
            _emailService = new EmailService(_dataService);
            
            // 清理测试环境
            if (File.Exists(_testDataPath))
            {
                File.Delete(_testDataPath);
            }
        }

        [TestCleanup]
        public void Cleanup()
        {
            // 清理测试文件
            if (File.Exists(_testDataPath))
            {
                File.Delete(_testDataPath);
            }
        }

        [TestMethod]
        public void IsValidDomain_ValidDomains_ReturnsTrue()
        {
            // Arrange & Act & Assert
            Assert.IsTrue(_emailService.IsValidDomain("example.com"));
            Assert.IsTrue(_emailService.IsValidDomain("test.org"));
            Assert.IsTrue(_emailService.IsValidDomain("my-domain.net"));
            Assert.IsTrue(_emailService.IsValidDomain("subdomain.example.com"));
        }

        [TestMethod]
        public void IsValidDomain_InvalidDomains_ReturnsFalse()
        {
            // Arrange & Act & Assert
            Assert.IsFalse(_emailService.IsValidDomain(""));
            Assert.IsFalse(_emailService.IsValidDomain(null));
            Assert.IsFalse(_emailService.IsValidDomain("   "));
            Assert.IsFalse(_emailService.IsValidDomain(".example.com"));
            Assert.IsFalse(_emailService.IsValidDomain("example.com."));
            Assert.IsFalse(_emailService.IsValidDomain("example"));
            Assert.IsFalse(_emailService.IsValidDomain("ex ample.com"));
            Assert.IsFalse(_emailService.IsValidDomain("example..com"));
        }

        [TestMethod]
        public void GenerateUniqueEmail_ValidDomain_ReturnsValidEmail()
        {
            // Arrange
            string domain = "test.com";

            // Act
            string email = _emailService.GenerateUniqueEmail(domain);

            // Assert
            Assert.IsNotNull(email);
            Assert.IsTrue(email.Contains("@"));
            Assert.IsTrue(email.EndsWith("@test.com"));
            
            string[] parts = email.Split('@');
            Assert.AreEqual(2, parts.Length);
            Assert.IsTrue(parts[0].Length >= 6); // 用户名至少6位
            Assert.IsTrue(parts[0].Length <= 12); // 用户名最多12位
        }

        [TestMethod]
        public void GenerateUniqueEmail_InvalidDomain_ReturnsNull()
        {
            // Act
            string email = _emailService.GenerateUniqueEmail("invalid");

            // Assert
            Assert.IsNull(email);
        }

        [TestMethod]
        public void GenerateUniqueEmail_WithExistingEmails_ReturnsUniqueEmail()
        {
            // Arrange
            string domain = "test.com";
            var existingEmails = new List<string> { "<EMAIL>", "<EMAIL>" };

            // Act
            string email = _emailService.GenerateUniqueEmail(domain, existingEmails);

            // Assert
            Assert.IsNotNull(email);
            Assert.IsFalse(existingEmails.Contains(email));
            Assert.IsTrue(email.EndsWith("@test.com"));
        }

        [TestMethod]
        public void CreateEmailRecord_ValidDomain_ReturnsValidRecord()
        {
            // Arrange
            string domain = "example.org";

            // Act
            var record = _emailService.CreateEmailRecord(domain);

            // Assert
            Assert.IsNotNull(record);
            Assert.IsNotNull(record.Email);
            Assert.IsTrue(record.Email.Contains("@"));
            Assert.AreEqual(domain, record.Domain);
            Assert.IsNotNull(record.Username);
            Assert.AreEqual(EmailStatus.Active, record.Status);
            Assert.IsNull(record.DeactivatedDate);
            Assert.IsTrue(record.CreatedDate <= DateTime.Now);
        }

        [TestMethod]
        public void CreateEmailRecord_InvalidDomain_ReturnsNull()
        {
            // Act
            var record = _emailService.CreateEmailRecord("invalid");

            // Assert
            Assert.IsNull(record);
        }

        [TestMethod]
        public void GenerateMultipleEmails_ValidInput_ReturnsCorrectCount()
        {
            // Arrange
            string domain = "multi.com";
            int count = 5;

            // Act
            var emails = _emailService.GenerateMultipleEmails(domain, count);

            // Assert
            Assert.AreEqual(count, emails.Count);
            
            // 检查所有邮箱都是唯一的
            var emailAddresses = emails.Select(e => e.Email).ToList();
            Assert.AreEqual(emailAddresses.Count, emailAddresses.Distinct().Count());
            
            // 检查所有邮箱都有正确的域名
            Assert.IsTrue(emails.All(e => e.Domain == domain));
            Assert.IsTrue(emails.All(e => e.Email.EndsWith($"@{domain}")));
        }

        [TestMethod]
        public void GenerateMultipleEmails_InvalidDomain_ReturnsEmptyList()
        {
            // Act
            var emails = _emailService.GenerateMultipleEmails("invalid", 5);

            // Assert
            Assert.AreEqual(0, emails.Count);
        }

        [TestMethod]
        public void GenerateMultipleEmails_ZeroCount_ReturnsEmptyList()
        {
            // Act
            var emails = _emailService.GenerateMultipleEmails("test.com", 0);

            // Assert
            Assert.AreEqual(0, emails.Count);
        }

        [TestMethod]
        public void GenerateUniqueEmail_MultipleGenerations_ProducesUniqueEmails()
        {
            // Arrange
            string domain = "unique.com";
            var generatedEmails = new HashSet<string>();

            // Act - 生成多个邮箱
            for (int i = 0; i < 20; i++)
            {
                string email = _emailService.GenerateUniqueEmail(domain);
                Assert.IsNotNull(email);
                generatedEmails.Add(email);
            }

            // Assert - 确保所有邮箱都是唯一的
            Assert.AreEqual(20, generatedEmails.Count);
        }

        [TestMethod]
        public void GenerateUniqueEmail_WithDataService_AvoidsExistingEmails()
        {
            // Arrange
            string domain = "avoid.com";
            
            // 先添加一些邮箱到数据服务
            var existingRecord = new EmailRecord
            {
                Email = "<EMAIL>",
                Domain = domain,
                Username = "existing",
                CreatedDate = DateTime.Now,
                Status = EmailStatus.Active
            };
            _dataService.AddEmail(existingRecord);

            // Act - 生成新邮箱
            string newEmail = _emailService.GenerateUniqueEmail(domain);

            // Assert
            Assert.IsNotNull(newEmail);
            Assert.AreNotEqual("<EMAIL>", newEmail);
            Assert.IsTrue(newEmail.EndsWith($"@{domain}"));
        }
    }
}