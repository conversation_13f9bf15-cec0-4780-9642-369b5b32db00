using System;

namespace EmailGenerator.Models
{
    /// <summary>
    /// 邮箱记录数据模型
    /// </summary>
    public class EmailRecord
    {
        /// <summary>
        /// 完整的邮箱地址
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 创建日期
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// 邮箱域名
        /// </summary>
        public string Domain { get; set; } = string.Empty;

        /// <summary>
        /// 用户名部分
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱状态
        /// </summary>
        public EmailStatus Status { get; set; }

        /// <summary>
        /// 作废日期（如果已作废）
        /// </summary>
        public DateTime? DeactivatedDate { get; set; }

        public EmailRecord()
        {
            CreatedDate = DateTime.Now;
            Status = EmailStatus.Active;
        }
    }
}