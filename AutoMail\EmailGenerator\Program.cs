using System;
using System.IO;
using System.Windows.Forms;
using EmailGenerator.Forms;
using EmailGenerator.Models;
using EmailGenerator.Helpers;

namespace EmailGenerator
{
    /// <summary>
    /// 程序入口点
    /// </summary>
    internal static class Program
    {
        private static readonly string LogFilePath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "EmailGenerator", "logs", "application.log");

        /// <summary>
        /// 应用程序的主入口点
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // 初始化日志系统
                InitializeLogging();
                LogInfo("程序启动开始");

                // 设置全局异常处理器
                SetupGlobalExceptionHandlers();

                // 启用应用程序的视觉样式
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                LogInfo("正在初始化主窗体");
                
                // 运行主窗体
                using (var mainForm = new MainForm())
                {
                    LogInfo("主窗体初始化完成，开始运行应用程序");
                    Application.Run(mainForm);
                }
                
                LogInfo("程序正常退出");
            }
            catch (TypeInitializationException ex)
            {
                LogError("类型初始化异常", ex);
                ShowStartupError($"程序初始化失败: {ex.InnerException?.Message ?? ex.Message}");
            }
            catch (System.Configuration.ConfigurationException ex)
            {
                LogError("配置异常", ex);
                ShowStartupError($"配置文件错误: {ex.Message}");
            }
            catch (UnauthorizedAccessException ex)
            {
                LogError("权限异常", ex);
                ShowStartupError($"权限不足: {ex.Message}\n\n请以管理员身份运行程序或检查文件权限。");
            }
            catch (FileNotFoundException ex)
            {
                LogError("文件未找到异常", ex);
                ShowStartupError($"缺少必要文件: {ex.FileName ?? ex.Message}");
            }
            catch (DirectoryNotFoundException ex)
            {
                LogError("目录未找到异常", ex);
                ShowStartupError($"缺少必要目录: {ex.Message}");
            }
            catch (OutOfMemoryException ex)
            {
                LogError("内存不足异常", ex);
                ShowStartupError("系统内存不足，无法启动程序。");
            }
            catch (Exception ex)
            {
                LogError("未处理的启动异常", ex);
                ShowStartupError($"程序启动时发生未知错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化日志系统
        /// </summary>
        private static void InitializeLogging()
        {
            try
            {
                var logDirectory = Path.GetDirectoryName(LogFilePath);
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }

                // 清理旧日志文件（保留最近7天的日志）
                CleanupOldLogFiles(logDirectory);
            }
            catch (Exception ex)
            {
                // 日志初始化失败不应该阻止程序启动
                System.Diagnostics.Debug.WriteLine($"日志初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理旧日志文件
        /// </summary>
        /// <param name="logDirectory">日志目录</param>
        private static void CleanupOldLogFiles(string logDirectory)
        {
            try
            {
                var logFiles = Directory.GetFiles(logDirectory, "*.log");
                var cutoffDate = DateTime.Now.AddDays(-7);

                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(logFile);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清理旧日志文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置全局异常处理器
        /// </summary>
        private static void SetupGlobalExceptionHandlers()
        {
            // 设置UI线程异常处理器
            Application.ThreadException += Application_ThreadException;
            
            // 设置非UI线程异常处理器
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            
            // 设置应用程序域异常处理器
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
        }

        /// <summary>
        /// UI线程异常处理器
        /// </summary>
        private static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            LogError("UI线程未处理异常", e.Exception);
            
            try
            {
                var result = MessageBox.Show(
                    $"程序遇到错误，是否继续运行？\n\n错误信息: {e.Exception.Message}",
                    "程序错误",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Error);

                if (result == DialogResult.No)
                {
                    LogInfo("用户选择退出程序");
                    Application.Exit();
                }
            }
            catch (Exception ex)
            {
                LogError("异常处理器本身发生异常", ex);
                Application.Exit();
            }
        }

        /// <summary>
        /// 非UI线程异常处理器
        /// </summary>
        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception ex)
            {
                LogError("应用程序域未处理异常", ex);
            }
            else
            {
                LogError("应用程序域未处理异常", new Exception(e.ExceptionObject?.ToString() ?? "未知异常"));
            }

            if (e.IsTerminating)
            {
                LogError("程序即将终止", null);
                MessageBox.Show("程序遇到严重错误，即将退出。", "严重错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 显示启动错误消息
        /// </summary>
        /// <param name="message">错误消息</param>
        private static void ShowStartupError(string message)
        {
            try
            {
                MessageBox.Show(message, "启动错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                // 如果连MessageBox都无法显示，记录到系统事件日志
                System.Diagnostics.Debug.WriteLine($"无法显示启动错误消息: {ex.Message}");
                try
                {
                    System.Diagnostics.EventLog.WriteEntry("EmailGenerator", 
                        $"启动错误: {message}", System.Diagnostics.EventLogEntryType.Error);
                }
                catch
                {
                    // 忽略事件日志写入失败
                }
            }
        }

        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private static void LogInfo(string message)
        {
            WriteLog("INFO", message, null);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="exception">异常对象</param>
        private static void LogError(string message, Exception exception)
        {
            WriteLog("ERROR", message, exception);
        }

        /// <summary>
        /// 写入日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        private static void WriteLog(string level, string message, Exception exception)
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                var logEntry = $"[{timestamp}] [{level}] {message}";
                
                if (exception != null)
                {
                    logEntry += $"\n异常详情: {exception}";
                }

                // 写入文件日志
                File.AppendAllText(LogFilePath, logEntry + Environment.NewLine);
                
                // 同时输出到调试控制台
                System.Diagnostics.Debug.WriteLine(logEntry);
            }
            catch (Exception ex)
            {
                // 日志写入失败不应该影响程序运行
                System.Diagnostics.Debug.WriteLine($"写入日志失败: {ex.Message}");
            }
        }
    }
}