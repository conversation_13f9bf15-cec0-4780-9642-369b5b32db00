# 设计文档

## 概述

邮箱生成器是一个基于C# WinForms的桌面应用程序，旨在为用户提供自动生成唯一邮箱地址的功能。该程序采用分层架构设计，包含数据访问层、业务逻辑层和用户界面层，确保代码的可维护性和可扩展性。程序支持自定义域名设置、邮箱地址生成、历史记录管理和数据持久化存储。

## 架构

### 整体架构模式
采用三层架构模式：
- **表示层（UI Layer）**: WinForms用户界面
- **业务逻辑层（Business Layer）**: 邮箱生成和验证逻辑
- **数据访问层（Data Layer）**: 文件存储和配置管理

### 技术栈
- **开发框架**: .NET Framework 4.7.2 或更高版本
- **UI框架**: Windows Forms
- **数据存储**: JSON文件存储
- **配置管理**: .NET Configuration API
- **随机生成**: System.Random 和 System.Guid

## 组件和接口

### 1. 主窗体 (MainForm)
```csharp
public partial class MainForm : Form
{
    private EmailService emailService;
    private ConfigurationService configService;
    private List<EmailRecord> emailHistory;
    
    // UI控件
    private TextBox txtDomain;
    private Button btnGenerate;
    private ListBox lstEmails;
    private Button btnCopy;
    private Button btnDelete;
    private Button btnDeactivate;
    private Button btnSaveDomain;
}
```

### 2. 邮箱服务 (EmailService)
```csharp
public class EmailService
{
    public string GenerateUniqueEmail(string domain, List<string> existingEmails);
    public bool IsValidDomain(string domain);
    private string GenerateRandomUsername();
    private bool IsEmailUnique(string email, List<string> existingEmails);
}
```

### 3. 数据服务 (DataService)
```csharp
public class DataService
{
    private string dataFilePath;
    
    public List<EmailRecord> LoadEmails();
    public void SaveEmails(List<EmailRecord> emails);
    public void DeleteEmail(string email);
    public void AddEmail(EmailRecord email);
    public void DeactivateEmail(string email);
    public void UpdateEmailStatus(string email, EmailStatus status);
}
```

### 4. 配置服务 (ConfigurationService)
```csharp
public class ConfigurationService
{
    private string configFilePath;
    
    public string LoadDomain();
    public void SaveDomain(string domain);
    public string GetDefaultDataPath();
    public string GetCustomConfigPath();
    public string GetCustomDataPath();
    public void SetCustomConfigPath(string path);
    public void SetCustomDataPath(string path);
    public bool ValidatePath(string path);
    public void ResetToDefaultPaths();
}
```

### 5. 路径管理服务 (PathManagerService)
```csharp
public class PathManagerService
{
    public string GetExecutableDirectory();
    public string GetDefaultConfigPath();
    public string GetDefaultDataPath();
    public bool IsValidPath(string path);
    public bool IsWritablePath(string path);
    public bool CreateDirectoryIfNotExists(string path);
    public string SelectFolderDialog(string title, string defaultPath);
}
```

### 5. 邮箱记录模型 (EmailRecord)
```csharp
public class EmailRecord
{
    public string Email { get; set; }
    public DateTime CreatedDate { get; set; }
    public string Domain { get; set; }
    public string Username { get; set; }
    public EmailStatus Status { get; set; }
    public DateTime? DeactivatedDate { get; set; }
}

public enum EmailStatus
{
    Active = 0,
    Deactivated = 1
}
```

## 数据模型

### 邮箱记录结构
```json
{
  "emails": [
    {
      "email": "<EMAIL>",
      "createdDate": "2024-01-15T10:30:00Z",
      "domain": "example.com",
      "username": "user123",
      "status": 0,
      "deactivatedDate": null
    },
    {
      "email": "<EMAIL>",
      "createdDate": "2024-01-14T09:20:00Z",
      "domain": "example.com",
      "username": "test456",
      "status": 1,
      "deactivatedDate": "2024-01-16T14:30:00Z"
    }
  ]
}
```

### 配置文件结构
```json
{
  "domain": "example.com",
  "dataFilePath": "emails.json",
  "lastUsed": "2024-01-15T10:30:00Z",
  "paths": {
    "configPath": "",
    "dataPath": "",
    "useCustomPaths": false,
    "customConfigPath": "C:\\MyApp\\Config",
    "customDataPath": "C:\\MyApp\\Data"
  }
}
```

## 错误处理

### 异常处理策略
1. **文件操作异常**: 捕获IOException，显示用户友好的错误消息
2. **JSON序列化异常**: 捕获JsonException，提供数据恢复选项
3. **域名验证异常**: 捕获ArgumentException，提示正确的域名格式
4. **网络相关异常**: 预留接口，用于未来可能的在线验证功能

### 错误消息设计
```csharp
public static class ErrorMessages
{
    public const string INVALID_DOMAIN = "域名格式无效，请输入正确的域名格式（如：example.com）";
    public const string FILE_ACCESS_ERROR = "无法访问数据文件，请检查文件权限";
    public const string GENERATION_FAILED = "邮箱生成失败，请重试";
    public const string DELETE_FAILED = "删除邮箱失败，请重试";
    public const string DEACTIVATE_FAILED = "作废邮箱失败，请重试";
    public const string NO_EMAIL_SELECTED = "请先选择要操作的邮箱";
    public const string INVALID_PATH = "路径无效，请选择有效的文件夹路径";
    public const string PATH_NOT_WRITABLE = "路径不可写，请检查文件夹权限或选择其他路径";
    public const string PATH_CREATE_FAILED = "无法创建目录，请检查权限或选择其他路径";
    public const string CONFIG_SAVE_FAILED = "保存路径配置失败，将使用默认路径";
}
```

## 测试策略

### 单元测试
- **EmailService测试**: 验证邮箱生成逻辑和唯一性检查
- **DataService测试**: 验证文件读写操作和数据完整性
- **ConfigurationService测试**: 验证配置加载和保存功能
- **域名验证测试**: 验证各种域名格式的有效性

### 集成测试
- **完整流程测试**: 从域名设置到邮箱生成的完整流程
- **数据持久化测试**: 验证数据在程序重启后的完整性
- **UI交互测试**: 验证用户界面操作的正确性

### 性能测试
- **大量数据测试**: 测试处理大量邮箱记录时的性能
- **文件操作性能**: 测试文件读写操作的效率
- **内存使用测试**: 监控程序的内存使用情况

## 用户界面设计

### 主窗体布局
```text
┌─────────────────────────────────────┐
│ 邮箱生成器                    [设置] │
├─────────────────────────────────────┤
│ 域名设置: [example.com    ] [保存]   │
├─────────────────────────────────────┤
│ [生成新邮箱]                         │
├─────────────────────────────────────┤
│ 邮箱历史记录:                        │
│ ┌─────────────────────────────────┐ │
│ │ ✓ <EMAIL>           │ │
│ │ ✗ <EMAIL> (已作废)  │ │
│ │ ✓ <EMAIL>         │ │
│ └─────────────────────────────────┘ │
│ [复制选中] [作废选中] [删除选中]      │
└─────────────────────────────────────┘
```

### 设置窗体布局
```text
┌─────────────────────────────────────┐
│ 路径设置                             │
├─────────────────────────────────────┤
│ □ 使用自定义路径                     │
├─────────────────────────────────────┤
│ 配置文件路径:                        │
│ [C:\MyApp\Config        ] [浏览...]  │
├─────────────────────────────────────┤
│ 数据文件路径:                        │
│ [C:\MyApp\Data          ] [浏览...]  │
├─────────────────────────────────────┤
│ 当前状态:                            │
│ 配置文件: 默认路径 (exe目录)          │
│ 数据文件: 默认路径 (exe目录)          │
├─────────────────────────────────────┤
│        [确定] [取消] [重置为默认]     │
└─────────────────────────────────────┘
```

### 控件规格
- **域名输入框**: TextBox，支持输入验证
- **生成按钮**: Button，触发邮箱生成逻辑
- **邮箱列表**: ListBox，显示历史邮箱记录，区分有效和失效状态
- **操作按钮**: 复制、作废和删除按钮，支持选中项操作

## 安全考虑

### 数据安全
- 本地文件存储，避免网络传输风险
- JSON文件权限控制，防止未授权访问
- 输入验证，防止恶意数据注入

### 隐私保护
- 不收集用户个人信息
- 本地数据存储，不上传到服务器
- 用户可完全控制数据的删除和管理

## 性能优化

### 内存管理
- 使用适当的数据结构存储邮箱记录
- 及时释放不需要的对象引用
- 避免内存泄漏，特别是事件处理器的注册和注销

### 文件操作优化
- 异步文件读写操作，避免UI阻塞
- 批量操作优化，减少文件I/O次数
- 文件缓存机制，提高访问效率

### UI响应性
- 长时间操作使用后台线程
- 进度指示器显示操作状态
- 防止重复点击和并发操作