using System;
using System.Windows.Forms;
using EmailGenerator.Models;

namespace EmailGenerator.Helpers
{
    /// <summary>
    /// 消息显示帮助类，提供统一的消息显示方法
    /// </summary>
    public static class MessageHelper
    {
        /// <summary>
        /// 执行操作并保护剪贴板内容不被MessageBox的Ctrl+C覆盖
        /// </summary>
        /// <param name="action">要执行的操作</param>
        private static void ExecuteWithClipboardProtection(Action action)
        {
            string clipboardBackup = null;
            bool hasClipboardContent = false;
            
            try
            {
                // 备份剪贴板内容
                if (Clipboard.ContainsText())
                {
                    clipboardBackup = Clipboard.GetText();
                    hasClipboardContent = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"备份剪贴板失败: {ex.Message}");
            }

            try
            {
                // 执行操作（显示MessageBox）
                action();
            }
            finally
            {
                // 恢复剪贴板内容
                if (hasClipboardContent && !string.IsNullOrEmpty(clipboardBackup))
                {
                    try
                    {
                        Clipboard.SetText(clipboardBackup);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"恢复剪贴板失败: {ex.Message}");
                    }
                }
            }
        }
        /// <summary>
        /// 显示错误消息
        /// </summary>
        /// <param name="message">错误消息内容</param>
        /// <param name="title">对话框标题，默认为"错误"</param>
        /// <param name="owner">父窗体，可选</param>
        public static void ShowErrorMessage(string message, string title = "错误", IWin32Window owner = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(message))
                {
                    message = ErrorMessages.UNKNOWN_ERROR;
                }

                if (owner != null)
                {
                    MessageBox.Show(owner, message, title, MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                else
                {
                    MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                // 如果显示错误消息本身失败，使用系统默认方式显示
                System.Diagnostics.Debug.WriteLine($"显示错误消息失败: {ex.Message}");
                MessageBox.Show($"发生错误: {message}", "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 显示成功消息（保护剪贴板内容不被Ctrl+C覆盖）
        /// </summary>
        /// <param name="message">成功消息内容</param>
        /// <param name="title">对话框标题，默认为"成功"</param>
        /// <param name="owner">父窗体，可选</param>
        public static void ShowSuccessMessage(string message, string title = "成功", IWin32Window owner = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(message))
                {
                    message = SuccessMessages.OPERATION_COMPLETED;
                }

                ExecuteWithClipboardProtection(() =>
                {
                    if (owner != null)
                    {
                        MessageBox.Show(owner, message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                });
            }
            catch (Exception ex)
            {
                // 如果显示成功消息失败，记录错误但不中断程序
                System.Diagnostics.Debug.WriteLine($"显示成功消息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示警告消息
        /// </summary>
        /// <param name="message">警告消息内容</param>
        /// <param name="title">对话框标题，默认为"警告"</param>
        /// <param name="owner">父窗体，可选</param>
        public static void ShowWarningMessage(string message, string title = "警告", IWin32Window owner = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(message))
                {
                    message = "发生警告";
                }

                if (owner != null)
                {
                    MessageBox.Show(owner, message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
                else
                {
                    MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示警告消息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示确认对话框
        /// </summary>
        /// <param name="message">确认消息内容</param>
        /// <param name="title">对话框标题，默认为"确认"</param>
        /// <param name="owner">父窗体，可选</param>
        /// <returns>用户选择的结果</returns>
        public static DialogResult ShowConfirmationMessage(string message, string title = "确认", IWin32Window owner = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(message))
                {
                    message = "确定要执行此操作吗？";
                }

                if (owner != null)
                {
                    return MessageBox.Show(owner, message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                }
                else
                {
                    return MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示确认消息失败: {ex.Message}");
                return DialogResult.No; // 默认返回No，确保安全
            }
        }

        /// <summary>
        /// 显示信息消息（保护剪贴板内容不被Ctrl+C覆盖）
        /// </summary>
        /// <param name="message">信息消息内容</param>
        /// <param name="title">对话框标题，默认为"信息"</param>
        /// <param name="owner">父窗体，可选</param>
        public static void ShowInfoMessage(string message, string title = "信息", IWin32Window owner = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(message))
                {
                    message = "信息";
                }

                ExecuteWithClipboardProtection(() =>
                {
                    if (owner != null)
                    {
                        MessageBox.Show(owner, message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示信息消息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示异常错误消息
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <param name="customMessage">自定义错误消息前缀</param>
        /// <param name="owner">父窗体，可选</param>
        public static void ShowExceptionMessage(Exception ex, string customMessage = null, IWin32Window owner = null)
        {
            try
            {
                string message;
                if (!string.IsNullOrWhiteSpace(customMessage))
                {
                    message = $"{customMessage}: {ex.Message}";
                }
                else
                {
                    message = $"发生错误: {ex.Message}";
                }

                ShowErrorMessage(message, "系统错误", owner);

                // 记录详细的异常信息到调试输出
                System.Diagnostics.Debug.WriteLine($"异常详情: {ex}");
            }
            catch (Exception innerEx)
            {
                System.Diagnostics.Debug.WriteLine($"显示异常消息失败: {innerEx.Message}");
                MessageBox.Show($"系统发生严重错误: {ex.Message}", "严重错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}