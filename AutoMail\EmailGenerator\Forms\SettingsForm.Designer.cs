namespace EmailGenerator.Forms
{
    partial class SettingsForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.chkUseCustomPaths = new System.Windows.Forms.CheckBox();
            this.lblConfigPath = new System.Windows.Forms.Label();
            this.txtConfigPath = new System.Windows.Forms.TextBox();
            this.btnBrowseConfig = new System.Windows.Forms.Button();
            this.lblDataPath = new System.Windows.Forms.Label();
            this.txtDataPath = new System.Windows.Forms.TextBox();
            this.btnBrowseData = new System.Windows.Forms.Button();
            this.grpCurrentStatus = new System.Windows.Forms.GroupBox();
            this.lblCurrentConfigPath = new System.Windows.Forms.Label();
            this.lblCurrentDataPath = new System.Windows.Forms.Label();
            this.lblConfigPathValue = new System.Windows.Forms.Label();
            this.lblDataPathValue = new System.Windows.Forms.Label();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnReset = new System.Windows.Forms.Button();
            this.lblConfigPathError = new System.Windows.Forms.Label();
            this.lblDataPathError = new System.Windows.Forms.Label();
            this.grpCurrentStatus.SuspendLayout();
            this.SuspendLayout();

            // 设置窗体属性
            this.Text = "路径设置";
            this.Size = new System.Drawing.Size(600, 450);
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;

            // 
            // chkUseCustomPaths
            // 
            this.chkUseCustomPaths.Location = new System.Drawing.Point(20, 20);
            this.chkUseCustomPaths.Name = "chkUseCustomPaths";
            this.chkUseCustomPaths.Size = new System.Drawing.Size(150, 23);
            this.chkUseCustomPaths.TabIndex = 0;
            this.chkUseCustomPaths.Text = "使用自定义路径";
            this.chkUseCustomPaths.UseVisualStyleBackColor = true;
            this.chkUseCustomPaths.CheckedChanged += new System.EventHandler(this.ChkUseCustomPaths_CheckedChanged);

            // 
            // lblConfigPath
            // 
            this.lblConfigPath.Enabled = false;
            this.lblConfigPath.Location = new System.Drawing.Point(40, 60);
            this.lblConfigPath.Name = "lblConfigPath";
            this.lblConfigPath.Size = new System.Drawing.Size(100, 23);
            this.lblConfigPath.TabIndex = 1;
            this.lblConfigPath.Text = "配置文件路径:";
            this.lblConfigPath.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;

            // 
            // txtConfigPath
            // 
            this.txtConfigPath.Enabled = false;
            this.txtConfigPath.Location = new System.Drawing.Point(40, 85);
            this.txtConfigPath.Name = "txtConfigPath";
            this.txtConfigPath.PlaceholderText = "选择配置文件路径...";
            this.txtConfigPath.Size = new System.Drawing.Size(400, 23);
            this.txtConfigPath.TabIndex = 1;
            this.txtConfigPath.TextChanged += new System.EventHandler(this.TxtConfigPath_TextChanged);

            // 
            // btnBrowseConfig
            // 
            this.btnBrowseConfig.Enabled = false;
            this.btnBrowseConfig.Location = new System.Drawing.Point(450, 84);
            this.btnBrowseConfig.Name = "btnBrowseConfig";
            this.btnBrowseConfig.Size = new System.Drawing.Size(70, 25);
            this.btnBrowseConfig.TabIndex = 2;
            this.btnBrowseConfig.Text = "浏览...";
            this.btnBrowseConfig.UseVisualStyleBackColor = true;
            this.btnBrowseConfig.Click += new System.EventHandler(this.BtnBrowseConfig_Click);

            // 
            // lblDataPath
            // 
            this.lblDataPath.Enabled = false;
            this.lblDataPath.Location = new System.Drawing.Point(40, 125);
            this.lblDataPath.Name = "lblDataPath";
            this.lblDataPath.Size = new System.Drawing.Size(100, 23);
            this.lblDataPath.TabIndex = 3;
            this.lblDataPath.Text = "数据文件路径:";
            this.lblDataPath.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;

            // 
            // txtDataPath
            // 
            this.txtDataPath.Enabled = false;
            this.txtDataPath.Location = new System.Drawing.Point(40, 150);
            this.txtDataPath.Name = "txtDataPath";
            this.txtDataPath.PlaceholderText = "选择数据文件路径...";
            this.txtDataPath.Size = new System.Drawing.Size(400, 23);
            this.txtDataPath.TabIndex = 3;
            this.txtDataPath.TextChanged += new System.EventHandler(this.TxtDataPath_TextChanged);

            // 
            // btnBrowseData
            // 
            this.btnBrowseData.Enabled = false;
            this.btnBrowseData.Location = new System.Drawing.Point(450, 149);
            this.btnBrowseData.Name = "btnBrowseData";
            this.btnBrowseData.Size = new System.Drawing.Size(70, 25);
            this.btnBrowseData.TabIndex = 4;
            this.btnBrowseData.Text = "浏览...";
            this.btnBrowseData.UseVisualStyleBackColor = true;
            this.btnBrowseData.Click += new System.EventHandler(this.BtnBrowseData_Click);

            // 
            // lblConfigPathError
            // 
            this.lblConfigPathError.ForeColor = System.Drawing.Color.Red;
            this.lblConfigPathError.Font = new System.Drawing.Font("Microsoft YaHei UI", 8F);
            this.lblConfigPathError.Location = new System.Drawing.Point(40, 110);
            this.lblConfigPathError.Name = "lblConfigPathError";
            this.lblConfigPathError.Size = new System.Drawing.Size(480, 20);
            this.lblConfigPathError.TabIndex = 2;
            this.lblConfigPathError.Text = "";
            this.lblConfigPathError.Visible = false;

            // 
            // lblDataPathError
            // 
            this.lblDataPathError.ForeColor = System.Drawing.Color.Red;
            this.lblDataPathError.Font = new System.Drawing.Font("Microsoft YaHei UI", 8F);
            this.lblDataPathError.Location = new System.Drawing.Point(40, 175);
            this.lblDataPathError.Name = "lblDataPathError";
            this.lblDataPathError.Size = new System.Drawing.Size(480, 20);
            this.lblDataPathError.TabIndex = 4;
            this.lblDataPathError.Text = "";
            this.lblDataPathError.Visible = false;

            // 
            // grpCurrentStatus
            // 
            this.grpCurrentStatus.Controls.Add(this.lblCurrentConfigPath);
            this.grpCurrentStatus.Controls.Add(this.lblCurrentDataPath);
            this.grpCurrentStatus.Controls.Add(this.lblConfigPathValue);
            this.grpCurrentStatus.Controls.Add(this.lblDataPathValue);
            this.grpCurrentStatus.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            this.grpCurrentStatus.Location = new System.Drawing.Point(20, 200);
            this.grpCurrentStatus.Name = "grpCurrentStatus";
            this.grpCurrentStatus.Size = new System.Drawing.Size(540, 120);
            this.grpCurrentStatus.TabIndex = 5;
            this.grpCurrentStatus.TabStop = false;
            this.grpCurrentStatus.Text = "当前路径状态";

            // 
            // lblCurrentConfigPath
            // 
            this.lblCurrentConfigPath.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Regular);
            this.lblCurrentConfigPath.Location = new System.Drawing.Point(15, 25);
            this.lblCurrentConfigPath.Name = "lblCurrentConfigPath";
            this.lblCurrentConfigPath.Size = new System.Drawing.Size(80, 23);
            this.lblCurrentConfigPath.TabIndex = 0;
            this.lblCurrentConfigPath.Text = "配置文件:";
            this.lblCurrentConfigPath.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;

            // 
            // lblConfigPathValue
            // 
            this.lblConfigPathValue.AutoEllipsis = true;
            this.lblConfigPathValue.Font = new System.Drawing.Font("Consolas", 8F);
            this.lblConfigPathValue.ForeColor = System.Drawing.Color.DarkBlue;
            this.lblConfigPathValue.Location = new System.Drawing.Point(100, 25);
            this.lblConfigPathValue.Name = "lblConfigPathValue";
            this.lblConfigPathValue.Size = new System.Drawing.Size(420, 23);
            this.lblConfigPathValue.TabIndex = 1;
            this.lblConfigPathValue.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;

            // 
            // lblCurrentDataPath
            // 
            this.lblCurrentDataPath.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Regular);
            this.lblCurrentDataPath.Location = new System.Drawing.Point(15, 55);
            this.lblCurrentDataPath.Name = "lblCurrentDataPath";
            this.lblCurrentDataPath.Size = new System.Drawing.Size(80, 23);
            this.lblCurrentDataPath.TabIndex = 2;
            this.lblCurrentDataPath.Text = "数据文件:";
            this.lblCurrentDataPath.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;

            // 
            // lblDataPathValue
            // 
            this.lblDataPathValue.AutoEllipsis = true;
            this.lblDataPathValue.Font = new System.Drawing.Font("Consolas", 8F);
            this.lblDataPathValue.ForeColor = System.Drawing.Color.DarkBlue;
            this.lblDataPathValue.Location = new System.Drawing.Point(100, 55);
            this.lblDataPathValue.Name = "lblDataPathValue";
            this.lblDataPathValue.Size = new System.Drawing.Size(420, 23);
            this.lblDataPathValue.TabIndex = 3;
            this.lblDataPathValue.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;

            // 
            // btnReset
            // 
            this.btnReset.Location = new System.Drawing.Point(180, 350);
            this.btnReset.Name = "btnReset";
            this.btnReset.Size = new System.Drawing.Size(100, 30);
            this.btnReset.TabIndex = 5;
            this.btnReset.Text = "重置为默认";
            this.btnReset.UseVisualStyleBackColor = true;
            this.btnReset.Click += new System.EventHandler(this.BtnReset_Click);

            // 
            // btnOK
            // 
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(300, 350);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(80, 30);
            this.btnOK.TabIndex = 6;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.BtnOK_Click);

            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(390, 350);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(80, 30);
            this.btnCancel.TabIndex = 7;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.BtnCancel_Click);

            // 设置默认按钮和取消按钮
            this.AcceptButton = this.btnOK;
            this.CancelButton = this.btnCancel;

            // 添加控件到窗体
            this.Controls.Add(this.chkUseCustomPaths);
            this.Controls.Add(this.lblConfigPath);
            this.Controls.Add(this.txtConfigPath);
            this.Controls.Add(this.btnBrowseConfig);
            this.Controls.Add(this.lblConfigPathError);
            this.Controls.Add(this.lblDataPath);
            this.Controls.Add(this.txtDataPath);
            this.Controls.Add(this.btnBrowseData);
            this.Controls.Add(this.lblDataPathError);
            this.Controls.Add(this.grpCurrentStatus);
            this.Controls.Add(this.btnReset);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);

            this.grpCurrentStatus.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        #endregion

        private System.Windows.Forms.CheckBox chkUseCustomPaths;
        private System.Windows.Forms.Label lblConfigPath;
        private System.Windows.Forms.TextBox txtConfigPath;
        private System.Windows.Forms.Button btnBrowseConfig;
        private System.Windows.Forms.Label lblDataPath;
        private System.Windows.Forms.TextBox txtDataPath;
        private System.Windows.Forms.Button btnBrowseData;
        private System.Windows.Forms.GroupBox grpCurrentStatus;
        private System.Windows.Forms.Label lblCurrentConfigPath;
        private System.Windows.Forms.Label lblCurrentDataPath;
        private System.Windows.Forms.Label lblConfigPathValue;
        private System.Windows.Forms.Label lblDataPathValue;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnReset;
        private System.Windows.Forms.Label lblConfigPathError;
        private System.Windows.Forms.Label lblDataPathError;
    }
}