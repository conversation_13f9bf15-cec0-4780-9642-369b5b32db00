using System;
using System.IO;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using EmailGenerator.Helpers;
using EmailGenerator.Models;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// 路径错误处理器测试类
    /// </summary>
    [TestClass]
    public class PathErrorHandlerTests
    {
        private string _testDirectory;
        private string _tempFile;

        [TestInitialize]
        public void Setup()
        {
            // 创建测试目录
            _testDirectory = Path.Combine(Path.GetTempPath(), "EmailGeneratorTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);
            
            _tempFile = Path.Combine(_testDirectory, "test.txt");
        }

        [TestCleanup]
        public void Cleanup()
        {
            try
            {
                if (File.Exists(_tempFile))
                    File.Delete(_tempFile);
                
                if (Directory.Exists(_testDirectory))
                    Directory.Delete(_testDirectory, true);
            }
            catch
            {
                // 清理失败不影响测试结果
            }
        }

        [TestMethod]
        public void AnalyzePathError_EmptyPath_ReturnsEmptyPathError()
        {
            // Arrange
            string emptyPath = "";

            // Act
            string result = PathErrorHandler.AnalyzePathError(emptyPath);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_EMPTY, result);
        }

        [TestMethod]
        public void AnalyzePathError_NullPath_ReturnsEmptyPathError()
        {
            // Arrange
            string nullPath = null;

            // Act
            string result = PathErrorHandler.AnalyzePathError(nullPath);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_EMPTY, result);
        }

        [TestMethod]
        public void AnalyzePathError_WhitespacePath_ReturnsEmptyPathError()
        {
            // Arrange
            string whitespacePath = "   ";

            // Act
            string result = PathErrorHandler.AnalyzePathError(whitespacePath);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_EMPTY, result);
        }

        [TestMethod]
        public void AnalyzePathError_TooLongPath_ReturnsTooLongError()
        {
            // Arrange
            string longPath = "C:\\" + new string('a', 300); // 超过260字符限制

            // Act
            string result = PathErrorHandler.AnalyzePathError(longPath);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_TOO_LONG, result);
        }

        [TestMethod]
        public void AnalyzePathError_InvalidCharacters_ReturnsInvalidCharsError()
        {
            // Arrange
            string invalidPath = "C:\\test<>path";

            // Act
            string result = PathErrorHandler.AnalyzePathError(invalidPath);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_CONTAINS_INVALID_CHARS, result);
        }

        [TestMethod]
        public void AnalyzePathError_NetworkPath_ReturnsNetworkNotSupportedError()
        {
            // Arrange
            string networkPath = "\\\\server\\share";

            // Act
            string result = PathErrorHandler.AnalyzePathError(networkPath);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_NETWORK_NOT_SUPPORTED, result);
        }

        [TestMethod]
        public void AnalyzePathError_RelativePath_ReturnsNotAbsoluteError()
        {
            // Arrange
            string relativePath = "relative\\path";

            // Act
            string result = PathErrorHandler.AnalyzePathError(relativePath);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_NOT_ABSOLUTE, result);
        }

        [TestMethod]
        public void AnalyzePathError_ReservedName_ReturnsReservedNameError()
        {
            // Arrange
            string reservedPath = Path.Combine(_testDirectory, "CON");

            // Act
            string result = PathErrorHandler.AnalyzePathError(reservedPath);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_RESERVED_NAME, result);
        }

        [TestMethod]
        public void AnalyzePathError_UnauthorizedAccessException_ReturnsAccessDeniedError()
        {
            // Arrange
            string path = Path.Combine(_testDirectory, "test");
            var exception = new UnauthorizedAccessException();

            // Act
            string result = PathErrorHandler.AnalyzePathError(path, exception);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_ACCESS_DENIED, result);
        }

        [TestMethod]
        public void AnalyzePathError_DirectoryNotFoundException_ReturnsDirectoryNotExistError()
        {
            // Arrange
            string path = Path.Combine(_testDirectory, "nonexistent");
            var exception = new DirectoryNotFoundException();

            // Act
            string result = PathErrorHandler.AnalyzePathError(path, exception);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_DIRECTORY_NOT_EXIST, result);
        }

        [TestMethod]
        public void AnalyzePathError_PathTooLongException_ReturnsTooLongError()
        {
            // Arrange
            string path = Path.Combine(_testDirectory, "test");
            var exception = new PathTooLongException();

            // Act
            string result = PathErrorHandler.AnalyzePathError(path, exception);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_TOO_LONG, result);
        }

        [TestMethod]
        public void AnalyzePathCreationError_UnauthorizedAccessException_ReturnsPermissionDeniedError()
        {
            // Arrange
            string path = "C:\\test";
            var exception = new UnauthorizedAccessException();

            // Act
            string result = PathErrorHandler.AnalyzePathCreationError(path, exception);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_CREATE_PERMISSION_DENIED, result);
        }

        [TestMethod]
        public void AnalyzePathCreationError_IOExceptionDiskFull_ReturnsDiskFullError()
        {
            // Arrange
            string path = "C:\\test";
            var exception = new IOException("disk space");

            // Act
            string result = PathErrorHandler.AnalyzePathCreationError(path, exception);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_CREATE_DISK_FULL, result);
        }

        [TestMethod]
        public void AnalyzePathCreationError_PathTooLongException_ReturnsNameTooLongError()
        {
            // Arrange
            string path = "C:\\test";
            var exception = new PathTooLongException();

            // Act
            string result = PathErrorHandler.AnalyzePathCreationError(path, exception);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_CREATE_NAME_TOO_LONG, result);
        }

        [TestMethod]
        public void AnalyzePathCreationError_ArgumentException_ReturnsInvalidNameError()
        {
            // Arrange
            string path = "C:\\test";
            var exception = new ArgumentException();

            // Act
            string result = PathErrorHandler.AnalyzePathCreationError(path, exception);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_CREATE_INVALID_NAME, result);
        }

        [TestMethod]
        public void AnalyzePathCreationError_DirectoryNotFoundException_ReturnsParentNotExistError()
        {
            // Arrange
            string path = "C:\\test";
            var exception = new DirectoryNotFoundException();

            // Act
            string result = PathErrorHandler.AnalyzePathCreationError(path, exception);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_CREATE_PARENT_NOT_EXIST, result);
        }

        [TestMethod]
        public void GetPermissionGuidance_SystemPath_ReturnsAdminRightsGuidance()
        {
            // Arrange
            string systemPath = Environment.GetFolderPath(Environment.SpecialFolder.Windows);

            // Act
            string result = PathErrorHandler.GetPermissionGuidance(systemPath);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_GUIDANCE_ADMIN_RIGHTS, result);
        }

        [TestMethod]
        public void GetPermissionGuidance_ProgramFilesPath_ReturnsAdminRightsGuidance()
        {
            // Arrange
            string programFilesPath = Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles);

            // Act
            string result = PathErrorHandler.GetPermissionGuidance(programFilesPath);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_GUIDANCE_ADMIN_RIGHTS, result);
        }

        [TestMethod]
        public void GetPermissionGuidance_RegularPath_ReturnsCheckPermissionGuidance()
        {
            // Arrange
            string regularPath = "C:\\Users\\<USER>\\test<>path";

            // Act
            var result = PathErrorHandler.ValidatePathDetailed(invalidPath);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual(ErrorMessages.PATH_CONTAINS_INVALID_CHARS, result.ErrorMessage);
            Assert.IsNotNull(result.Guidance);
        }

        [TestMethod]
        public void ValidatePathDetailed_NonExistentPath_ReturnsInvalidResult()
        {
            // Arrange
            string nonExistentPath = "C:\\NonExistentDirectory123456";

            // Act
            var result = PathErrorHandler.ValidatePathDetailed(nonExistentPath);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.IsNotNull(result.ErrorMessage);
            Assert.IsNotNull(result.Guidance);
        }

        [TestMethod]
        public void ShowPathErrorDialog_DoesNotThrowException()
        {
            // Arrange
            string path = "C:\\test";
            var exception = new UnauthorizedAccessException();

            // Act & Assert - 确保方法不抛出异常
            try
            {
                PathErrorHandler.ShowPathErrorDialog(path, exception);
                Assert.IsTrue(true); // 如果没有异常，测试通过
            }
            catch (Exception ex)
            {
                Assert.Fail($"ShowPathErrorDialog抛出了异常: {ex.Message}");
            }
        }

        [TestMethod]
        public void ShowPathCreationErrorDialog_DoesNotThrowException()
        {
            // Arrange
            string path = "C:\\test";
            var exception = new UnauthorizedAccessException();

            // Act & Assert - 确保方法不抛出异常
            try
            {
                PathErrorHandler.ShowPathCreationErrorDialog(path, exception);
                Assert.IsTrue(true); // 如果没有异常，测试通过
            }
            catch (Exception ex)
            {
                Assert.Fail($"ShowPathCreationErrorDialog抛出了异常: {ex.Message}");
            }
        }

        #region 路径权限错误处理测试

        [TestMethod]
        public void AnalyzePathError_IOExceptionWithDiskMessage_ReturnsDiskFullError()
        {
            // Arrange
            string path = "C:\\test";
            var exception = new IOException("disk space insufficient");

            // Act
            string result = PathErrorHandler.AnalyzePathError(path, exception);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_DISK_FULL, result);
        }

        [TestMethod]
        public void AnalyzePathError_IOExceptionWithReadOnlyMessage_ReturnsReadOnlyError()
        {
            // Arrange
            string path = "C:\\test";
            var exception = new IOException("read-only file system");

            // Act
            string result = PathErrorHandler.AnalyzePathError(path, exception);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_READ_ONLY, result);
        }

        [TestMethod]
        public void AnalyzePathCreationError_IOExceptionWithFileExists_ReturnsFileExistsError()
        {
            // Arrange
            string path = "C:\\test";
            File.WriteAllText(path, "test"); // 创建一个文件
            var exception = new IOException("already exists");

            try
            {
                // Act
                string result = PathErrorHandler.AnalyzePathCreationError(path, exception);

                // Assert
                Assert.AreEqual(ErrorMessages.PATH_CREATE_ALREADY_EXISTS_AS_FILE, result);
            }
            finally
            {
                // Cleanup
                if (File.Exists(path))
                    File.Delete(path);
            }
        }

        [TestMethod]
        public void GetPermissionGuidance_SystemX86Path_ReturnsAdminRightsGuidance()
        {
            // Arrange
            string systemX86Path = Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86);
            if (string.IsNullOrEmpty(systemX86Path))
            {
                Assert.Inconclusive("ProgramFilesX86 路径不可用");
                return;
            }

            // Act
            string result = PathErrorHandler.GetPermissionGuidance(systemX86Path);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_GUIDANCE_ADMIN_RIGHTS, result);
        }

        [TestMethod]
        public void GetPermissionGuidance_SystemDirectoryPath_ReturnsAdminRightsGuidance()
        {
            // Arrange
            string systemPath = Environment.GetFolderPath(Environment.SpecialFolder.System);

            // Act
            string result = PathErrorHandler.GetPermissionGuidance(systemPath);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_GUIDANCE_ADMIN_RIGHTS, result);
        }

        #endregion

        #region 路径验证详细测试

        [TestMethod]
        public void ValidatePathDetailed_ValidWritablePath_ReturnsValidResult()
        {
            // Arrange
            string validPath = _testDirectory;

            // Act
            var result = PathErrorHandler.ValidatePathDetailed(validPath);

            // Assert
            Assert.IsTrue(result.IsValid, $"路径验证应该成功: {result.ErrorMessage}");
            Assert.AreEqual(validPath, result.Path);
            Assert.AreEqual(string.Empty, result.ErrorMessage);
            Assert.AreEqual(string.Empty, result.Guidance);
        }

        [TestMethod]
        public void ValidatePathDetailed_PathWithInvalidChars_ReturnsInvalidResult()
        {
            // Arrange
            string invalidPath = "C:\\test|path";

            // Act
            var result = PathErrorHandler.ValidatePathDetailed(invalidPath);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual(ErrorMessages.PATH_CONTAINS_INVALID_CHARS, result.ErrorMessage);
            Assert.IsNotNull(result.Guidance);
            Assert.IsTrue(result.Guidance.Length > 0);
        }

        [TestMethod]
        public void ValidatePathDetailed_NetworkPath_ReturnsInvalidResult()
        {
            // Arrange
            string networkPath = "\\\\server\\share\\folder";

            // Act
            var result = PathErrorHandler.ValidatePathDetailed(networkPath);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual(ErrorMessages.PATH_NETWORK_NOT_SUPPORTED, result.ErrorMessage);
            Assert.IsNotNull(result.Guidance);
        }

        [TestMethod]
        public void ValidatePathDetailed_RelativePath_ReturnsInvalidResult()
        {
            // Arrange
            string relativePath = "..\\relative\\path";

            // Act
            var result = PathErrorHandler.ValidatePathDetailed(relativePath);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual(ErrorMessages.PATH_NOT_ABSOLUTE, result.ErrorMessage);
            Assert.IsNotNull(result.Guidance);
        }

        [TestMethod]
        public void ValidatePathDetailed_PathTooLong_ReturnsInvalidResult()
        {
            // Arrange
            string longPath = "C:\\" + new string('a', 300);

            // Act
            var result = PathErrorHandler.ValidatePathDetailed(longPath);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual(ErrorMessages.PATH_TOO_LONG, result.ErrorMessage);
            Assert.IsNotNull(result.Guidance);
        }

        #endregion

        #region 异常处理边界测试

        [TestMethod]
        public void AnalyzePathError_NullException_ReturnsDirectoryNotExistError()
        {
            // Arrange
            string nonExistentPath = "C:\\NonExistentDirectory123456789";

            // Act
            string result = PathErrorHandler.AnalyzePathError(nonExistentPath, null);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_DIRECTORY_NOT_EXIST, result);
        }

        [TestMethod]
        public void AnalyzePathError_UnknownException_ReturnsPathInvalidWithMessage()
        {
            // Arrange
            string path = "C:\\test";
            var exception = new InvalidOperationException("Unknown error");

            // Act
            string result = PathErrorHandler.AnalyzePathError(path, exception);

            // Assert
            Assert.IsTrue(result.StartsWith(ErrorMessages.PATH_INVALID));
            Assert.IsTrue(result.Contains("Unknown error"));
        }

        [TestMethod]
        public void AnalyzePathCreationError_UnknownException_ReturnsCreateFailedWithMessage()
        {
            // Arrange
            string path = "C:\\test";
            var exception = new InvalidOperationException("Unknown creation error");

            // Act
            string result = PathErrorHandler.AnalyzePathCreationError(path, exception);

            // Assert
            Assert.IsTrue(result.StartsWith(ErrorMessages.PATH_CREATE_FAILED));
            Assert.IsTrue(result.Contains("Unknown creation error"));
        }

        [TestMethod]
        public void GetPermissionGuidance_EmptyPath_ReturnsCheckPermissionGuidance()
        {
            // Arrange
            string emptyPath = "";

            // Act
            string result = PathErrorHandler.GetPermissionGuidance(emptyPath);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_GUIDANCE_CHECK_PERMISSION, result);
        }

        [TestMethod]
        public void GetPermissionGuidance_NullPath_ReturnsCheckPermissionGuidance()
        {
            // Arrange
            string nullPath = null;

            // Act
            string result = PathErrorHandler.GetPermissionGuidance(nullPath);

            // Assert
            Assert.AreEqual(ErrorMessages.PATH_GUIDANCE_CHECK_PERMISSION, result);
        }

        #endregion

        #region 错误对话框显示测试

        [TestMethod]
        public void ShowPathErrorDialog_WithNullPath_DoesNotThrowException()
        {
            // Arrange
            string nullPath = null;

            // Act & Assert
            try
            {
                PathErrorHandler.ShowPathErrorDialog(nullPath);
                Assert.IsTrue(true);
            }
            catch (Exception ex)
            {
                Assert.Fail($"ShowPathErrorDialog with null path threw exception: {ex.Message}");
            }
        }

        [TestMethod]
        public void ShowPathErrorDialog_WithEmptyPath_DoesNotThrowException()
        {
            // Arrange
            string emptyPath = "";

            // Act & Assert
            try
            {
                PathErrorHandler.ShowPathErrorDialog(emptyPath);
                Assert.IsTrue(true);
            }
            catch (Exception ex)
            {
                Assert.Fail($"ShowPathErrorDialog with empty path threw exception: {ex.Message}");
            }
        }

        [TestMethod]
        public void ShowPathCreationErrorDialog_WithNullPath_DoesNotThrowException()
        {
            // Arrange
            string nullPath = null;
            var exception = new UnauthorizedAccessException();

            // Act & Assert
            try
            {
                PathErrorHandler.ShowPathCreationErrorDialog(nullPath, exception);
                Assert.IsTrue(true);
            }
            catch (Exception ex)
            {
                Assert.Fail($"ShowPathCreationErrorDialog with null path threw exception: {ex.Message}");
            }
        }

        [TestMethod]
        public void ShowPathCreationErrorDialog_WithNullException_DoesNotThrowException()
        {
            // Arrange
            string path = "C:\\test";
            Exception nullException = null;

            // Act & Assert
            try
            {
                PathErrorHandler.ShowPathCreationErrorDialog(path, nullException);
                Assert.IsTrue(true);
            }
            catch (Exception ex)
            {
                Assert.Fail($"ShowPathCreationErrorDialog with null exception threw exception: {ex.Message}");
            }
        }

        #endregion
    }
}