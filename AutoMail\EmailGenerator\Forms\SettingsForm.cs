using System;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;
using EmailGenerator.Services;
using EmailGenerator.Helpers;

namespace EmailGenerator.Forms
{
    /// <summary>
    /// 路径设置窗体
    /// </summary>
    public partial class SettingsForm : Form
    {
        private readonly ConfigurationService _configService;
        private readonly PathManagerService _pathManager;
        private readonly DataService _dataService;
        private readonly DataMigrationService _migrationService;

        // 原始设置备份，用于取消操作
        private bool _originalUseCustomPaths;
        private string _originalConfigPath;
        private string _originalDataPath;

        public SettingsForm()
        {
            _configService = new ConfigurationService();
            _pathManager = new PathManagerService();
            _dataService = new DataService(_configService, _pathManager);
            _migrationService = new DataMigrationService(_pathManager, _configService, _dataService);
            InitializeComponent();
            LoadCurrentSettings();
        }

        public SettingsForm(ConfigurationService configService, PathManagerService pathManager)
        {
            _configService = configService ?? new ConfigurationService();
            _pathManager = pathManager ?? new PathManagerService();
            _dataService = new DataService(_configService, _pathManager);
            _migrationService = new DataMigrationService(_pathManager, _configService, _dataService);
            InitializeComponent();
            LoadCurrentSettings();
        }

        public SettingsForm(ConfigurationService configService, PathManagerService pathManager, DataService dataService)
        {
            _configService = configService ?? new ConfigurationService();
            _pathManager = pathManager ?? new PathManagerService();
            _dataService = dataService ?? new DataService(_configService, _pathManager);
            _migrationService = new DataMigrationService(_pathManager, _configService, _dataService);
            InitializeComponent();
            LoadCurrentSettings();
        }

        /// <summary>
        /// 加载当前设置
        /// </summary>
        private void LoadCurrentSettings()
        {
            try
            {
                // 获取当前路径配置
                bool useCustomPaths = _configService.GetUseCustomPaths();
                string configPath = _configService.GetCustomConfigPath();
                string dataPath = _configService.GetCustomDataPath();

                // 备份原始设置
                _originalUseCustomPaths = useCustomPaths;
                _originalConfigPath = configPath ?? "";
                _originalDataPath = dataPath ?? "";

                // 设置UI状态
                chkUseCustomPaths.Checked = useCustomPaths;
                txtConfigPath.Text = configPath ?? "";
                txtDataPath.Text = dataPath ?? "";

                // 更新当前状态显示
                UpdateCurrentStatusDisplay();
            }
            catch (Exception ex)
            {
                MessageHelper.ShowExceptionMessage(ex, "加载设置失败", this);
            }
        }

        /// <summary>
        /// 更新当前状态显示
        /// </summary>
        private void UpdateCurrentStatusDisplay()
        {
            try
            {
                // 显示当前实际使用的路径
                string currentConfigPath = _configService.GetCurrentConfigPath();
                string currentDataPath = _configService.GetCurrentDataPath();

                lblConfigPathValue.Text = currentConfigPath;
                lblDataPathValue.Text = currentDataPath;

                // 设置工具提示
                var toolTip = new ToolTip();
                toolTip.SetToolTip(lblConfigPathValue, currentConfigPath);
                toolTip.SetToolTip(lblDataPathValue, currentDataPath);
            }
            catch (Exception ex)
            {
                lblConfigPathValue.Text = "获取路径失败";
                lblDataPathValue.Text = "获取路径失败";
            }
        }

        /// <summary>
        /// 自定义路径复选框状态变化事件
        /// </summary>
        private void ChkUseCustomPaths_CheckedChanged(object sender, EventArgs e)
        {
            bool enabled = chkUseCustomPaths.Checked;

            // 启用或禁用路径配置控件
            lblConfigPath.Enabled = enabled;
            txtConfigPath.Enabled = enabled;
            btnBrowseConfig.Enabled = enabled;
            lblDataPath.Enabled = enabled;
            txtDataPath.Enabled = enabled;
            btnBrowseData.Enabled = enabled;

            // 如果禁用自定义路径，清空输入框并隐藏错误提示
            if (!enabled)
            {
                txtConfigPath.Text = "";
                txtDataPath.Text = "";
                HidePathError(lblConfigPathError);
                HidePathError(lblDataPathError);
            }
            else
            {
                // 如果启用自定义路径但输入框为空，设置默认值
                if (string.IsNullOrWhiteSpace(txtConfigPath.Text))
                {
                    txtConfigPath.Text = _pathManager.GetDefaultConfigPath();
                }
                if (string.IsNullOrWhiteSpace(txtDataPath.Text))
                {
                    txtDataPath.Text = _pathManager.GetDefaultDataPath();
                }

                // 启用自定义路径时进行实时验证
                ValidateConfigPathRealTime();
                ValidateDataPathRealTime();
            }
        }

        /// <summary>
        /// 配置文件路径浏览按钮点击事件
        /// </summary>
        private void BtnBrowseConfig_Click(object sender, EventArgs e)
        {
            try
            {
                using (var saveFileDialog = new SaveFileDialog())
                {
                    saveFileDialog.Title = "选择配置文件路径";
                    saveFileDialog.Filter = "JSON文件 (*.json)|*.json|所有文件 (*.*)|*.*";
                    saveFileDialog.DefaultExt = "json";
                    saveFileDialog.FileName = "config.json";

                    // 设置初始目录
                    if (!string.IsNullOrWhiteSpace(txtConfigPath.Text))
                    {
                        string directory = Path.GetDirectoryName(txtConfigPath.Text);
                        if (Directory.Exists(directory))
                        {
                            saveFileDialog.InitialDirectory = directory;
                        }
                    }
                    else
                    {
                        saveFileDialog.InitialDirectory = _pathManager.GetExecutableDirectory();
                    }

                    if (saveFileDialog.ShowDialog(this) == DialogResult.OK)
                    {
                        string selectedPath = saveFileDialog.FileName;
                        
                        // 验证路径
                        if (ValidatePath(selectedPath, "配置文件"))
                        {
                            txtConfigPath.Text = selectedPath;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowExceptionMessage(ex, "选择配置文件路径时发生错误", this);
            }
        }

        /// <summary>
        /// 数据文件路径浏览按钮点击事件
        /// </summary>
        private void BtnBrowseData_Click(object sender, EventArgs e)
        {
            try
            {
                using (var saveFileDialog = new SaveFileDialog())
                {
                    saveFileDialog.Title = "选择数据文件路径";
                    saveFileDialog.Filter = "JSON文件 (*.json)|*.json|所有文件 (*.*)|*.*";
                    saveFileDialog.DefaultExt = "json";
                    saveFileDialog.FileName = "emails.json";

                    // 设置初始目录
                    if (!string.IsNullOrWhiteSpace(txtDataPath.Text))
                    {
                        string directory = Path.GetDirectoryName(txtDataPath.Text);
                        if (Directory.Exists(directory))
                        {
                            saveFileDialog.InitialDirectory = directory;
                        }
                    }
                    else
                    {
                        saveFileDialog.InitialDirectory = _pathManager.GetExecutableDirectory();
                    }

                    if (saveFileDialog.ShowDialog(this) == DialogResult.OK)
                    {
                        string selectedPath = saveFileDialog.FileName;
                        
                        // 验证路径
                        if (ValidatePath(selectedPath, "数据文件"))
                        {
                            txtDataPath.Text = selectedPath;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowExceptionMessage(ex, "选择数据文件路径时发生错误", this);
            }
        }

        /// <summary>
        /// 验证路径的有效性
        /// </summary>
        /// <param name="path">要验证的路径</param>
        /// <param name="pathType">路径类型（用于错误消息）</param>
        /// <returns>路径是否有效</returns>
        private bool ValidatePath(string path, string pathType)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                {
                    MessageHelper.ShowWarningMessage($"{pathType}路径不能为空", "路径验证失败", this);
                    return false;
                }

                // 验证路径格式
                if (!_pathManager.IsValidPath(path))
                {
                    MessageHelper.ShowWarningMessage($"{pathType}路径格式无效", "路径验证失败", this);
                    return false;
                }

                // 验证目录是否存在，如果不存在则询问是否创建
                string directory = Path.GetDirectoryName(path);
                if (!Directory.Exists(directory))
                {
                    var result = MessageHelper.ShowConfirmationMessage(
                        $"目录 {directory} 不存在，是否创建？", 
                        "创建目录", this);

                    if (result == DialogResult.Yes)
                    {
                        try
                        {
                            Directory.CreateDirectory(directory);
                        }
                        catch (Exception ex)
                        {
                            MessageHelper.ShowErrorMessage($"创建目录失败: {ex.Message}", "目录创建失败", this);
                            return false;
                        }
                    }
                    else
                    {
                        return false;
                    }
                }

                // 验证是否有写入权限
                if (!_pathManager.HasWritePermission(directory))
                {
                    MessageHelper.ShowWarningMessage($"对目录 {directory} 没有写入权限", "权限验证失败", this);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                MessageHelper.ShowExceptionMessage(ex, $"验证{pathType}路径时发生错误", this);
                return false;
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private async void BtnOK_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证设置
                if (!ValidateSettings())
                {
                    return;
                }

                // 检查是否需要数据迁移
                if (await CheckAndPerformMigrationAsync())
                {
                    // 保存设置
                    bool success = SaveSettings();
                    if (success)
                    {
                        MessageHelper.ShowSuccessMessage("路径设置已保存", "保存成功", this);
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                    else
                    {
                        MessageHelper.ShowErrorMessage("保存路径设置失败", "保存失败", this);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowExceptionMessage(ex, "保存设置时发生错误", this);
            }
        }

        /// <summary>
        /// 验证设置的有效性
        /// </summary>
        /// <returns>设置是否有效</returns>
        private bool ValidateSettings()
        {
            if (!chkUseCustomPaths.Checked)
            {
                return true; // 使用默认路径，无需验证
            }

            // 验证配置文件路径
            if (string.IsNullOrWhiteSpace(txtConfigPath.Text))
            {
                MessageHelper.ShowWarningMessage("请选择配置文件路径", "验证失败", this);
                txtConfigPath.Focus();
                return false;
            }

            if (!ValidatePath(txtConfigPath.Text, "配置文件"))
            {
                txtConfigPath.Focus();
                return false;
            }

            // 验证数据文件路径
            if (string.IsNullOrWhiteSpace(txtDataPath.Text))
            {
                MessageHelper.ShowWarningMessage("请选择数据文件路径", "验证失败", this);
                txtDataPath.Focus();
                return false;
            }

            if (!ValidatePath(txtDataPath.Text, "数据文件"))
            {
                txtDataPath.Focus();
                return false;
            }

            // 验证两个路径不能相同
            if (string.Equals(txtConfigPath.Text.Trim(), txtDataPath.Text.Trim(), StringComparison.OrdinalIgnoreCase))
            {
                MessageHelper.ShowWarningMessage("配置文件路径和数据文件路径不能相同", "验证失败", this);
                txtDataPath.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// 保存设置
        /// </summary>
        /// <returns>保存是否成功</returns>
        private bool SaveSettings()
        {
            try
            {
                bool useCustomPaths = chkUseCustomPaths.Checked;
                string configPath = useCustomPaths ? txtConfigPath.Text.Trim() : null;
                string dataPath = useCustomPaths ? txtDataPath.Text.Trim() : null;

                // 保存路径配置
                bool success = _configService.SavePathConfiguration(useCustomPaths, configPath, dataPath);
                
                if (success)
                {
                    // 更新当前状态显示
                    UpdateCurrentStatusDisplay();
                }

                return success;
            }
            catch (Exception ex)
            {
                MessageHelper.ShowExceptionMessage(ex, "保存设置时发生错误", this);
                return false;
            }
        }

        /// <summary>
        /// 配置文件路径文本变化事件 - 实时验证
        /// </summary>
        private void TxtConfigPath_TextChanged(object sender, EventArgs e)
        {
            if (!chkUseCustomPaths.Checked)
            {
                lblConfigPathError.Visible = false;
                return;
            }

            ValidateConfigPathRealTime();
        }

        /// <summary>
        /// 数据文件路径文本变化事件 - 实时验证
        /// </summary>
        private void TxtDataPath_TextChanged(object sender, EventArgs e)
        {
            if (!chkUseCustomPaths.Checked)
            {
                lblDataPathError.Visible = false;
                return;
            }

            ValidateDataPathRealTime();
        }

        /// <summary>
        /// 实时验证配置文件路径
        /// </summary>
        private void ValidateConfigPathRealTime()
        {
            string path = txtConfigPath.Text.Trim();
            
            if (string.IsNullOrEmpty(path))
            {
                ShowPathError(lblConfigPathError, "配置文件路径不能为空");
                return;
            }

            if (!_pathManager.IsValidPath(path))
            {
                ShowPathError(lblConfigPathError, "配置文件路径格式无效");
                return;
            }

            string directory = Path.GetDirectoryName(path);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                ShowPathError(lblConfigPathError, "目录不存在，保存时将尝试创建");
                return;
            }

            if (!string.IsNullOrEmpty(directory) && !_pathManager.HasWritePermission(directory))
            {
                ShowPathError(lblConfigPathError, "对该目录没有写入权限");
                return;
            }

            // 检查是否与数据文件路径相同
            if (!string.IsNullOrEmpty(txtDataPath.Text.Trim()) && 
                string.Equals(path, txtDataPath.Text.Trim(), StringComparison.OrdinalIgnoreCase))
            {
                ShowPathError(lblConfigPathError, "配置文件路径不能与数据文件路径相同");
                return;
            }

            // 路径验证通过
            HidePathError(lblConfigPathError);
        }

        /// <summary>
        /// 实时验证数据文件路径
        /// </summary>
        private void ValidateDataPathRealTime()
        {
            string path = txtDataPath.Text.Trim();
            
            if (string.IsNullOrEmpty(path))
            {
                ShowPathError(lblDataPathError, "数据文件路径不能为空");
                return;
            }

            if (!_pathManager.IsValidPath(path))
            {
                ShowPathError(lblDataPathError, "数据文件路径格式无效");
                return;
            }

            string directory = Path.GetDirectoryName(path);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                ShowPathError(lblDataPathError, "目录不存在，保存时将尝试创建");
                return;
            }

            if (!string.IsNullOrEmpty(directory) && !_pathManager.HasWritePermission(directory))
            {
                ShowPathError(lblDataPathError, "对该目录没有写入权限");
                return;
            }

            // 检查是否与配置文件路径相同
            if (!string.IsNullOrEmpty(txtConfigPath.Text.Trim()) && 
                string.Equals(path, txtConfigPath.Text.Trim(), StringComparison.OrdinalIgnoreCase))
            {
                ShowPathError(lblDataPathError, "数据文件路径不能与配置文件路径相同");
                return;
            }

            // 路径验证通过
            HidePathError(lblDataPathError);
        }

        /// <summary>
        /// 显示路径错误提示
        /// </summary>
        private void ShowPathError(Label errorLabel, string message)
        {
            errorLabel.Text = message;
            errorLabel.Visible = true;
        }

        /// <summary>
        /// 隐藏路径错误提示
        /// </summary>
        private void HidePathError(Label errorLabel)
        {
            errorLabel.Visible = false;
        }

        /// <summary>
        /// 取消按钮点击事件 - 恢复原始设置
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                // 恢复原始设置
                chkUseCustomPaths.Checked = _originalUseCustomPaths;
                txtConfigPath.Text = _originalConfigPath;
                txtDataPath.Text = _originalDataPath;

                // 隐藏错误提示
                HidePathError(lblConfigPathError);
                HidePathError(lblDataPathError);

                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageHelper.ShowExceptionMessage(ex, "取消操作时发生错误", this);
            }
        }

        /// <summary>
        /// 重置为默认按钮点击事件
        /// </summary>
        private void BtnReset_Click(object sender, EventArgs e)
        {
            try
            {
                var result = MessageHelper.ShowConfirmationMessage(
                    "确定要重置为默认路径设置吗？\n\n这将清除所有自定义路径配置。", 
                    "确认重置", this);

                if (result == DialogResult.Yes)
                {
                    // 重置UI状态
                    chkUseCustomPaths.Checked = false;
                    txtConfigPath.Text = "";
                    txtDataPath.Text = "";

                    // 隐藏错误提示
                    HidePathError(lblConfigPathError);
                    HidePathError(lblDataPathError);

                    // 更新当前状态显示
                    UpdateCurrentStatusDisplay();

                    MessageHelper.ShowInfoMessage("已重置为默认路径设置", "重置完成", this);
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowExceptionMessage(ex, "重置设置时发生错误", this);
            }
        }

        /// <summary>
        /// 检查并执行数据迁移
        /// </summary>
        /// <returns>是否成功完成迁移或无需迁移</returns>
        private async Task<bool> CheckAndPerformMigrationAsync()
        {
            try
            {
                // 获取当前路径和新路径
                string currentConfigPath = _configService.GetCurrentConfigPath();
                string currentDataPath = _configService.GetCurrentDataPath();

                string newConfigPath = chkUseCustomPaths.Checked ? txtConfigPath.Text.Trim() : _pathManager.GetDefaultConfigPath();
                string newDataPath = chkUseCustomPaths.Checked ? txtDataPath.Text.Trim() : _pathManager.GetDefaultDataPath();

                // 检查是否需要迁移
                bool needsMigration = !string.Equals(currentConfigPath, newConfigPath, StringComparison.OrdinalIgnoreCase) ||
                                     !string.Equals(currentDataPath, newDataPath, StringComparison.OrdinalIgnoreCase);

                if (!needsMigration)
                {
                    return true; // 无需迁移
                }

                // 显示迁移确认对话框
                if (!_migrationService.ShowMigrationConfirmation(currentConfigPath, newConfigPath, currentDataPath, newDataPath))
                {
                    return false; // 用户取消迁移
                }

                // 执行迁移
                using (var progressForm = new MigrationProgressForm())
                {
                    var migrationTask = progressForm.StartMigrationAsync((progressCallback, cancellationToken) =>
                        _migrationService.MigrateDataAsync(newConfigPath, newDataPath, progressCallback, cancellationToken));

                    progressForm.ShowDialog(this);

                    if (progressForm.MigrationResult?.Success == true)
                    {
                        MessageHelper.ShowSuccessMessage("数据迁移成功完成", "迁移成功", this);
                        return true;
                    }
                    else
                    {
                        string errorMessage = progressForm.MigrationResult?.ErrorMessage ?? "未知错误";
                        MessageHelper.ShowErrorMessage($"数据迁移失败: {errorMessage}", "迁移失败", this);
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowExceptionMessage(ex, "执行数据迁移时发生错误", this);
                return false;
            }
        }

        /// <summary>
        /// 检查路径变更是否需要数据迁移
        /// </summary>
        /// <returns>是否需要迁移</returns>
        private bool IsPathChangeRequiresMigration()
        {
            try
            {
                string currentConfigPath = _configService.GetCurrentConfigPath();
                string currentDataPath = _configService.GetCurrentDataPath();

                string newConfigPath = chkUseCustomPaths.Checked ? txtConfigPath.Text.Trim() : _pathManager.GetDefaultConfigPath();
                string newDataPath = chkUseCustomPaths.Checked ? txtDataPath.Text.Trim() : _pathManager.GetDefaultDataPath();

                return !string.Equals(currentConfigPath, newConfigPath, StringComparison.OrdinalIgnoreCase) ||
                       !string.Equals(currentDataPath, newDataPath, StringComparison.OrdinalIgnoreCase);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 显示迁移预览信息
        /// </summary>
        private void ShowMigrationPreview()
        {
            if (!IsPathChangeRequiresMigration())
            {
                return;
            }

            try
            {
                string currentConfigPath = _configService.GetCurrentConfigPath();
                string currentDataPath = _configService.GetCurrentDataPath();

                string newConfigPath = chkUseCustomPaths.Checked ? txtConfigPath.Text.Trim() : _pathManager.GetDefaultConfigPath();
                string newDataPath = chkUseCustomPaths.Checked ? txtDataPath.Text.Trim() : _pathManager.GetDefaultDataPath();

                string previewMessage = "路径变更将触发数据迁移：\n\n";
                previewMessage += $"配置文件：\n  从: {currentConfigPath}\n  到: {newConfigPath}\n\n";
                previewMessage += $"数据文件：\n  从: {currentDataPath}\n  到: {newDataPath}\n\n";
                previewMessage += "点击确定时将自动执行数据迁移。";

                MessageHelper.ShowInfoMessage(previewMessage, "迁移预览", this);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示迁移预览失败: {ex.Message}");
            }
        }
    }
}