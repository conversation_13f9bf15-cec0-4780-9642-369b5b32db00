using System;
using System.Drawing;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using EmailGenerator.Services;

namespace EmailGenerator.Forms
{
    /// <summary>
    /// 数据迁移进度窗体
    /// </summary>
    public partial class MigrationProgressForm : Form
    {
        private CancellationTokenSource cancellationTokenSource;
        private bool migrationCompleted = false;

        public DataMigrationService.MigrationResult MigrationResult { get; private set; }

        public MigrationProgressForm()
        {
            InitializeComponent();
            cancellationTokenSource = new CancellationTokenSource();
        }



        /// <summary>
        /// 开始迁移操作
        /// </summary>
        /// <param name="migrationTask">迁移任务</param>
        public async Task StartMigrationAsync(Func<DataMigrationService.MigrationProgressCallback, CancellationToken, Task<DataMigrationService.MigrationResult>> migrationTask)
        {
            try
            {
                // 创建进度回调
                DataMigrationService.MigrationProgressCallback progressCallback = (progress, message) =>
                {
                    if (this.InvokeRequired)
                    {
                        this.Invoke(new Action(() => UpdateProgress(progress, message)));
                    }
                    else
                    {
                        UpdateProgress(progress, message);
                    }
                };

                // 启动迁移任务
                var migrationTaskWithCallback = Task.Run(async () =>
                {
                    try
                    {
                        MigrationResult = await migrationTask(progressCallback, cancellationTokenSource.Token);
                        migrationCompleted = true;

                        // 更新UI
                        this.Invoke(new Action(() =>
                        {
                            if (MigrationResult.Success)
                            {
                                UpdateProgress(100, "迁移成功完成");
                                statusLabel.ForeColor = Color.Green;
                                cancelButton.Text = "完成";
                            }
                            else
                            {
                                statusLabel.Text = "迁移失败";
                                statusLabel.ForeColor = Color.Red;
                                detailLabel.Text = MigrationResult.ErrorMessage;
                                detailLabel.ForeColor = Color.Red;
                                cancelButton.Text = "关闭";
                            }
                        }));
                    }
                    catch (OperationCanceledException)
                    {
                        MigrationResult = new DataMigrationService.MigrationResult 
                        { 
                            ErrorMessage = "用户取消了迁移操作" 
                        };
                        migrationCompleted = true;

                        this.Invoke(new Action(() =>
                        {
                            statusLabel.Text = "迁移已取消";
                            statusLabel.ForeColor = Color.Orange;
                            detailLabel.Text = "用户取消了迁移操作";
                            cancelButton.Text = "关闭";
                        }));
                    }
                    catch (Exception ex)
                    {
                        MigrationResult = new DataMigrationService.MigrationResult 
                        { 
                            ErrorMessage = $"迁移过程中发生异常: {ex.Message}" 
                        };
                        migrationCompleted = true;

                        this.Invoke(new Action(() =>
                        {
                            statusLabel.Text = "迁移异常";
                            statusLabel.ForeColor = Color.Red;
                            detailLabel.Text = ex.Message;
                            detailLabel.ForeColor = Color.Red;
                            cancelButton.Text = "关闭";
                        }));
                    }
                });

                await migrationTaskWithCallback;
            }
            catch (Exception ex)
            {
                MigrationResult = new DataMigrationService.MigrationResult 
                { 
                    ErrorMessage = $"启动迁移任务失败: {ex.Message}" 
                };
                
                statusLabel.Text = "启动失败";
                statusLabel.ForeColor = Color.Red;
                detailLabel.Text = ex.Message;
                detailLabel.ForeColor = Color.Red;
                cancelButton.Text = "关闭";
            }
        }

        /// <summary>
        /// 更新进度显示
        /// </summary>
        /// <param name="progress">进度百分比</param>
        /// <param name="message">进度消息</param>
        private void UpdateProgress(int progress, string message)
        {
            try
            {
                progressBar.Value = Math.Max(0, Math.Min(100, progress));
                statusLabel.Text = message ?? "";
                
                // 更新详细信息
                if (progress <= 10)
                {
                    detailLabel.Text = "正在验证迁移路径...";
                }
                else if (progress <= 30)
                {
                    detailLabel.Text = "正在创建数据备份...";
                }
                else if (progress <= 70)
                {
                    detailLabel.Text = "正在迁移文件...";
                }
                else if (progress <= 90)
                {
                    detailLabel.Text = "正在验证数据完整性...";
                }
                else if (progress < 100)
                {
                    detailLabel.Text = "正在清理临时文件...";
                }
                else
                {
                    detailLabel.Text = "迁移操作已完成";
                }

                this.Refresh();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新进度失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, EventArgs e)
        {
            if (migrationCompleted)
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                // 确认取消操作
                var result = MessageBox.Show(
                    "确定要取消数据迁移吗？\n\n取消后将回滚到原始状态。",
                    "确认取消",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question,
                    MessageBoxDefaultButton.Button2
                );

                if (result == DialogResult.Yes)
                {
                    cancellationTokenSource.Cancel();
                    cancelButton.Enabled = false;
                    cancelButton.Text = "正在取消...";
                    statusLabel.Text = "正在取消迁移...";
                    statusLabel.ForeColor = Color.Orange;
                }
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void MigrationProgressForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (!migrationCompleted && e.CloseReason == CloseReason.UserClosing)
            {
                // 阻止用户直接关闭窗体
                e.Cancel = true;
                CancelButton_Click(sender, e);
            }
        }


    }
}