using System;
using System.IO;
using System.Windows.Forms;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using EmailGenerator.Forms;
using EmailGenerator.Services;
using EmailGenerator.Models;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// 主窗体路径设置集成测试
    /// </summary>
    [TestClass]
    public class MainFormPathIntegrationTests
    {
        private string _testDirectory;
        private string _originalDirectory;
        private PathManagerService _pathManager;
        private ConfigurationService _configService;

        [TestInitialize]
        public void Setup()
        {
            // 创建测试目录
            _originalDirectory = Directory.GetCurrentDirectory();
            _testDirectory = Path.Combine(Path.GetTempPath(), "EmailGeneratorTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);
            Directory.SetCurrentDirectory(_testDirectory);

            // 初始化服务
            _pathManager = new PathManagerService();
            _configService = new ConfigurationService(_pathManager);
        }

        [TestCleanup]
        public void Cleanup()
        {
            try
            {
                Directory.SetCurrentDirectory(_originalDirectory);
                if (Directory.Exists(_testDirectory))
                {
                    Directory.Delete(_testDirectory, true);
                }
            }
            catch
            {
                // 忽略清理错误
            }
        }

        [TestMethod]
        public void MainForm_InitializeServices_ShouldUseConfiguredPaths()
        {
            // Arrange
            string customConfigPath = Path.Combine(_testDirectory, "custom_config.json");
            string customDataPath = Path.Combine(_testDirectory, "custom_data.json");

            // 保存自定义路径配置
            _configService.SavePathConfiguration(true, customConfigPath, customDataPath);

            // Act
            using (var mainForm = new MainForm())
            {
                // 通过反射访问私有字段来验证服务初始化
                var configServiceField = typeof(MainForm).GetField("_configService", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var dataServiceField = typeof(MainForm).GetField("_dataService", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                var configService = (ConfigurationService)configServiceField.GetValue(mainForm);
                var dataService = (DataService)dataServiceField.GetValue(mainForm);

                // Assert
                Assert.IsNotNull(configService, "配置服务应该被正确初始化");
                Assert.IsNotNull(dataService, "数据服务应该被正确初始化");

                // 验证服务使用了正确的路径
                string actualConfigPath = configService.GetCurrentConfigPath();
                string actualDataPath = configService.GetCurrentDataPath();

                Assert.AreEqual(customConfigPath, actualConfigPath, "应该使用自定义配置文件路径");
                Assert.AreEqual(customDataPath, actualDataPath, "应该使用自定义数据文件路径");
            }
        }

        [TestMethod]
        public void MainForm_ApplyPathSettings_ShouldReinitializeServices()
        {
            // Arrange
            string initialConfigPath = Path.Combine(_testDirectory, "initial_config.json");
            string initialDataPath = Path.Combine(_testDirectory, "initial_data.json");
            string newConfigPath = Path.Combine(_testDirectory, "new_config.json");
            string newDataPath = Path.Combine(_testDirectory, "new_data.json");

            // 设置初始路径
            _configService.SavePathConfiguration(true, initialConfigPath, initialDataPath);

            using (var mainForm = new MainForm())
            {
                // 模拟路径变更
                _configService.SavePathConfiguration(true, newConfigPath, newDataPath);

                // Act - 通过反射调用私有方法
                var applyPathSettingsMethod = typeof(MainForm).GetMethod("ApplyPathSettings", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                bool result = (bool)applyPathSettingsMethod.Invoke(mainForm, null);

                // Assert
                Assert.IsTrue(result, "应用路径设置应该成功");

                // 验证服务已重新初始化并使用新路径
                var configServiceField = typeof(MainForm).GetField("_configService", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var configService = (ConfigurationService)configServiceField.GetValue(mainForm);

                string actualConfigPath = configService.GetCurrentConfigPath();
                string actualDataPath = configService.GetCurrentDataPath();

                Assert.AreEqual(newConfigPath, actualConfigPath, "应该使用新的配置文件路径");
                Assert.AreEqual(newDataPath, actualDataPath, "应该使用新的数据文件路径");
            }
        }

        [TestMethod]
        public void MainForm_ApplyPathSettings_WithInvalidPath_ShouldRollback()
        {
            // Arrange
            string validConfigPath = Path.Combine(_testDirectory, "valid_config.json");
            string validDataPath = Path.Combine(_testDirectory, "valid_data.json");
            string invalidPath = "Z:\\NonExistent\\Path\\config.json"; // 无效路径

            // 设置有效的初始路径
            _configService.SavePathConfiguration(true, validConfigPath, validDataPath);

            using (var mainForm = new MainForm())
            {
                // 获取初始配置服务引用
                var configServiceField = typeof(MainForm).GetField("_configService", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var initialConfigService = (ConfigurationService)configServiceField.GetValue(mainForm);
                string initialConfigPath = initialConfigService.GetCurrentConfigPath();

                // 尝试设置无效路径（这里我们模拟配置保存成功但应用失败的情况）
                // 在实际测试中，我们需要模拟服务初始化失败

                // Act & Assert
                // 由于无法直接模拟服务初始化失败，我们验证错误恢复机制的存在
                var applyPathSettingsMethod = typeof(MainForm).GetMethod("ApplyPathSettings", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                // 这个测试主要验证方法存在且有错误处理机制
                Assert.IsNotNull(applyPathSettingsMethod, "ApplyPathSettings方法应该存在");
            }
        }

        [TestMethod]
        public void MainForm_UpdatePathStatusDisplay_ShouldShowCorrectPathInfo()
        {
            // Arrange
            string customConfigPath = Path.Combine(_testDirectory, "custom", "config.json");
            string customDataPath = Path.Combine(_testDirectory, "custom", "data.json");
            
            // 创建目录
            Directory.CreateDirectory(Path.GetDirectoryName(customConfigPath));
            Directory.CreateDirectory(Path.GetDirectoryName(customDataPath));

            _configService.SavePathConfiguration(true, customConfigPath, customDataPath);

            using (var mainForm = new MainForm())
            {
                // Act - 通过反射调用私有方法
                var updatePathStatusMethod = typeof(MainForm).GetMethod("UpdatePathStatusDisplay", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                updatePathStatusMethod.Invoke(mainForm, null);

                // Assert - 验证状态栏标签存在
                var statusLabelField = typeof(MainForm).GetField("statusLabel", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var statusLabel = (ToolStripStatusLabel)statusLabelField.GetValue(mainForm);

                Assert.IsNotNull(statusLabel, "状态栏标签应该存在");
                Assert.IsTrue(statusLabel.Text.Contains("配置:"), "状态栏应该显示配置路径信息");
                Assert.IsTrue(statusLabel.Text.Contains("数据:"), "状态栏应该显示数据路径信息");
            }
        }

        [TestMethod]
        public void MainForm_GetPathDisplayName_ShouldReturnCorrectDisplayName()
        {
            // Arrange
            using (var mainForm = new MainForm())
            {
                var getPathDisplayNameMethod = typeof(MainForm).GetMethod("GetPathDisplayName", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                // Act & Assert
                // 测试默认路径
                string exeDirectory = _pathManager.GetExecutableDirectory();
                string defaultPath = Path.Combine(exeDirectory, "config.json");
                string defaultDisplayName = (string)getPathDisplayNameMethod.Invoke(mainForm, new object[] { defaultPath });
                Assert.AreEqual("默认路径", defaultDisplayName, "默认路径应该显示为'默认路径'");

                // 测试自定义路径
                string customPath = Path.Combine(_testDirectory, "custom", "config.json");
                string customDisplayName = (string)getPathDisplayNameMethod.Invoke(mainForm, new object[] { customPath });
                Assert.AreEqual("custom", customDisplayName, "自定义路径应该显示目录名");

                // 测试空路径
                string emptyDisplayName = (string)getPathDisplayNameMethod.Invoke(mainForm, new object[] { "" });
                Assert.AreEqual("未知", emptyDisplayName, "空路径应该显示为'未知'");

                // 测试null路径
                string nullDisplayName = (string)getPathDisplayNameMethod.Invoke(mainForm, new object[] { null });
                Assert.AreEqual("未知", nullDisplayName, "null路径应该显示为'未知'");
            }
        }

        [TestMethod]
        public void MainForm_MenuSettings_ShouldOpenSettingsForm()
        {
            // Arrange
            using (var mainForm = new MainForm())
            {
                // 通过反射获取菜单项
                var menuSettingsField = typeof(MainForm).GetField("menuSettings", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var menuSettings = (ToolStripMenuItem)menuSettingsField.GetValue(mainForm);

                // Assert
                Assert.IsNotNull(menuSettings, "设置菜单项应该存在");
                Assert.AreEqual("路径设置(&S)...", menuSettings.Text, "菜单项文本应该正确");
                Assert.IsTrue(menuSettings.HasDropDownItems == false || menuSettings.Enabled, "菜单项应该可用");
            }
        }

        [TestMethod]
        public void MainForm_StatusBar_ShouldDisplayPathInformation()
        {
            // Arrange & Act
            using (var mainForm = new MainForm())
            {
                // 通过反射获取状态栏组件
                var statusStripField = typeof(MainForm).GetField("statusStrip", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var statusLabelField = typeof(MainForm).GetField("statusLabel", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                var statusStrip = (StatusStrip)statusStripField.GetValue(mainForm);
                var statusLabel = (ToolStripStatusLabel)statusLabelField.GetValue(mainForm);

                // Assert
                Assert.IsNotNull(statusStrip, "状态栏应该存在");
                Assert.IsNotNull(statusLabel, "状态栏标签应该存在");
                Assert.IsTrue(statusStrip.Items.Contains(statusLabel), "状态栏应该包含状态标签");
            }
        }
    }
}