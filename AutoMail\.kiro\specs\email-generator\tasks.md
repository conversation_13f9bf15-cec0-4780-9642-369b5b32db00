# 实施计划

- [x] 1. 创建项目结构和核心数据模型
  - 创建新的C# WinForms项目
  - 定义EmailRecord数据模型类，包含Email、CreatedDate、Domain、Username、Status、DeactivatedDate属性
  - 定义EmailStatus枚举，包含Active和Deactivated状态
  - 创建项目文件夹结构：Models、Services、Forms
  - _需求: 4.1, 4.5_

- [x] 2. 实现配置服务功能
  - 创建ConfigurationService类，实现域名的加载和保存功能
  - 实现LoadDomain()方法，从配置文件读取默认域名
  - 实现SaveDomain()方法，将域名保存到配置文件
  - 创建配置文件的JSON序列化和反序列化逻辑
  - 编写ConfigurationService的单元测试
  - _需求: 1.1, 1.3, 1.4_

- [x] 3. 实现数据持久化服务
  - 创建DataService类，处理邮箱数据的文件存储
  - 实现LoadEmails()方法，从JSON文件加载邮箱历史记录包括状态信息
  - 实现SaveEmails()方法，将邮箱列表保存到JSON文件
  - 实现AddEmail()和DeleteEmail()方法，管理单个邮箱记录
  - 实现DeactivateEmail()和UpdateEmailStatus()方法，管理邮箱状态
  - 添加文件不存在时的自动创建逻辑
  - 编写DataService的单元测试，验证文件操作和状态管理的正确性
  - _需求: 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 4. 实现邮箱生成核心逻辑
  - 创建EmailService类，实现邮箱生成算法
  - 实现GenerateRandomUsername()方法，生成随机用户名
  - 实现IsValidDomain()方法，验证域名格式的有效性
  - 实现GenerateUniqueEmail()方法，确保生成的邮箱地址唯一性
  - 实现IsEmailUnique()方法，检查邮箱是否已存在
  - 编写EmailService的单元测试，验证生成逻辑和唯一性检查
  - _需求: 2.1, 2.2, 2.3, 2.4_

- [x] 5. 设计和创建主窗体界面
  - 创建MainForm窗体，设置窗体属性和布局
  - 添加域名输入TextBox控件（txtDomain）
  - 添加保存域名Button控件（btnSaveDomain）
  - 添加生成邮箱Button控件（btnGenerate）
  - 添加邮箱历史ListBox控件（lstEmails）
  - 添加复制Button控件（btnCopy）、作废Button控件（btnDeactivate）和删除Button控件（btnDelete）
  - 设置控件的位置、大小和样式，确保界面美观易用
  - _需求: 5.1, 5.2_

- [x] 6. 实现域名设置功能
  - 在MainForm中集成ConfigurationService
  - 实现窗体加载时自动加载保存的域名设置
  - 实现btnSaveDomain_Click事件处理器，保存用户输入的域名
  - 添加域名格式验证，显示验证错误消息
  - 实现域名保存成功的提示信息
  - 编写域名设置功能的集成测试
  - _需求: 1.1, 1.2, 1.3, 1.4_

- [x] 7. 实现邮箱生成功能
  - 在MainForm中集成EmailService和DataService
  - 实现btnGenerate_Click事件处理器，触发邮箱生成
  - 调用EmailService生成唯一邮箱地址
  - 将新生成的邮箱添加到历史记录中
  - 更新界面显示新生成的邮箱
  - 实现生成失败时的错误处理和用户提示
  - 编写邮箱生成功能的集成测试
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5_

- [x] 8. 实现邮箱历史记录显示功能
  - 实现LoadEmailHistory()方法，在窗体加载时显示历史邮箱
  - 实现RefreshEmailList()方法，更新ListBox显示内容
  - 设置ListBox的显示格式，显示邮箱地址、创建时间和状态信息
  - 实现邮箱状态的视觉区分显示（有效/失效标识）
  - 实现邮箱列表的选择状态管理
  - 处理空历史记录的显示情况
  - 编写邮箱历史显示功能的单元测试
  - _需求: 3.1, 3.2, 3.8_

- [x] 9. 实现邮箱复制功能
  - 实现btnCopy_Click事件处理器
  - 获取ListBox中选中的邮箱地址
  - 将选中的邮箱地址复制到系统剪贴板
  - 显示复制成功的提示信息
  - 处理未选中邮箱时的提示
  - 编写复制功能的单元测试
  - _需求: 3.3_

- [x] 10. 实现邮箱作废功能
  - 实现btnDeactivate_Click事件处理器
  - 获取ListBox中选中的邮箱地址
  - 调用DataService作废选中的邮箱记录
  - 更新界面显示邮箱的失效状态
  - 显示作废成功的确认信息
  - 添加作废前的确认对话框
  - 处理作废失败的错误情况
  - 编写作废功能的集成测试
  - _需求: 3.6, 3.7_

- [x] 11. 实现邮箱删除功能
  - 实现btnDelete_Click事件处理器
  - 获取ListBox中选中的邮箱地址
  - 调用DataService删除选中的邮箱记录
  - 从界面列表中移除删除的邮箱
  - 显示删除成功的确认信息
  - 添加删除前的确认对话框
  - 处理删除失败的错误情况
  - 编写删除功能的集成测试
  - _需求: 3.4, 3.5_

- [x] 12. 实现错误处理和用户提示
  - 创建ErrorMessages静态类，定义所有错误消息常量
  - 实现ShowErrorMessage()方法，统一显示错误信息
  - 实现ShowSuccessMessage()方法，显示操作成功信息
  - 在所有文件操作中添加try-catch异常处理
  - 在所有用户输入验证中添加错误提示
  - 编写错误处理机制的单元测试
  - _需求: 5.3, 5.4_

- [x] 13. 实现程序启动和初始化逻辑
  - 在Program.cs中设置应用程序入口点
  - 在MainForm构造函数中初始化所有服务实例
  - 实现窗体加载事件处理器，加载配置和历史数据
  - 设置窗体的默认状态和控件初始值
  - 添加程序异常处理和日志记录
  - 编写程序启动流程的集成测试
  - _需求: 4.3, 5.5_

- [x] 14. 优化用户界面响应性
  - 实现异步文件操作，避免UI线程阻塞
  - 在长时间操作时显示进度指示器或等待光标
  - 实现防重复点击机制，避免并发操作
  - 优化ListBox的数据绑定和刷新性能
  - 添加键盘快捷键支持（如Ctrl+C复制，Delete删除）
  - 编写UI响应性的性能测试
  - _需求: 5.5_

- [x] 15. 编写综合测试和验证
  - 创建完整的端到端测试用例
  - 测试从域名设置到邮箱生成的完整流程
  - 验证数据持久化在程序重启后的完整性
  - 测试各种边界条件和异常情况
  - 验证所有需求的实现完整性
  - 进行用户界面的可用性测试
  - _需求: 1.1, 1.2, 1.3, 1.4, 2.1, 2.2, 2.3, 2.4, 2.5, 3.1, 3.2, 3.3, 3.4, 3.5, 4.1, 4.2, 4.3, 4.4, 4.5, 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 16. 实现路径管理服务
  - 创建PathManagerService类，处理文件路径相关操作
  - 实现GetExecutableDirectory()方法，获取程序exe所在目录
  - 实现GetDefaultConfigPath()和GetDefaultDataPath()方法，返回默认路径
  - 实现IsValidPath()和IsWritablePath()方法，验证路径有效性和可写性
  - 实现CreateDirectoryIfNotExists()方法，自动创建不存在的目录
  - 实现SelectFolderDialog()方法，提供文件夹选择对话框
  - 编写PathManagerService的单元测试
  - _需求: 5.3, 5.4, 5.7_

- [x] 17. 扩展配置服务支持自定义路径
  - 修改ConfigurationService类，添加路径管理功能
  - 实现GetCustomConfigPath()和GetCustomDataPath()方法
  - 实现SetCustomConfigPath()和SetCustomDataPath()方法
  - 实现ValidatePath()方法，验证用户输入的路径
  - 实现ResetToDefaultPaths()方法，重置为默认路径
  - 更新配置文件结构，添加paths配置节
  - 修改LoadDomain()和SaveDomain()方法，支持自定义配置路径
  - 编写扩展配置服务的单元测试
  - _需求: 5.1, 5.2, 5.5, 5.6_

- [x] 18. 修改数据服务支持自定义路径
  - 修改DataService构造函数，接受自定义数据文件路径参数
  - 更新所有文件操作方法，使用配置的数据文件路径
  - 实现路径变更时的数据迁移逻辑
  - 添加路径无效时的错误处理和回退机制
  - 确保数据文件在自定义路径下正确创建和访问
  - 编写自定义路径数据服务的集成测试
  - _需求: 5.1, 5.2, 5.7, 5.8_

- [x] 19. 创建路径设置窗体
  - 创建SettingsForm窗体，提供路径配置界面
  - 添加"使用自定义路径"复选框控件
  - 添加配置文件路径和数据文件路径的输入框和浏览按钮
  - 添加当前路径状态显示区域
  - 实现路径浏览对话框功能
  - 实现路径验证和错误提示
  - 添加确定、取消和重置为默认按钮
  - 设置窗体的布局和样式
  - _需求: 6.6, 6.7_

- [x] 20. 实现路径设置功能逻辑
  - 在SettingsForm中集成PathManagerService和ConfigurationService
  - 实现窗体加载时显示当前路径配置
  - 实现复选框状态变化时的界面控制逻辑
  - 实现浏览按钮点击事件，调用文件夹选择对话框
  - 实现路径输入验证和实时错误提示
  - 实现确定按钮逻辑，保存路径配置并应用更改
  - 实现取消按钮逻辑，恢复原始设置
  - 实现重置按钮逻辑，恢复默认路径设置
  - _需求: 5.4, 5.5, 5.6, 5.8_

- [x] 21. 集成路径设置到主窗体
  - 在MainForm中添加设置按钮，打开路径设置窗体
  - 修改MainForm初始化逻辑，使用配置的自定义路径
  - 实现路径配置变更后的服务重新初始化
  - 添加路径配置变更的确认对话框
  - 处理路径变更失败时的错误恢复
  - 更新状态栏显示当前使用的路径信息
  - 编写主窗体路径设置集成的测试
  - _需求: 5.6, 5.7, 6.6, 6.7_

- [x] 22. 实现数据迁移和备份功能
  - 实现路径变更时的数据自动迁移功能
  - 创建数据备份机制，防止迁移过程中数据丢失
  - 实现迁移进度显示和用户确认
  - 添加迁移失败时的回滚机制
  - 实现配置文件的备份和恢复
  - 处理迁移过程中的各种异常情况
  - 编写数据迁移功能的集成测试
  - _需求: 5.7, 5.8_

- [x] 23. 完善错误处理和用户提示
  - 扩展ErrorMessages类，添加路径相关的错误消息
  - 实现路径无效时的详细错误提示
  - 添加路径权限不足时的处理逻辑
  - 实现路径创建失败时的用户指导
  - 添加路径配置保存失败时的回退机制
  - 完善所有路径操作的异常处理
  - 编写路径错误处理的单元测试
  - _需求: 5.4, 5.8, 6.4, 6.5_

- [x] 24. 编写自定义路径功能的综合测试
  - 创建路径功能的端到端测试用例
  - 测试默认路径和自定义路径的切换流程
  - 验证路径变更后数据的完整性和可访问性
  - 测试各种无效路径输入的处理
  - 验证数据迁移功能的正确性
  - 测试路径权限相关的边界情况
  - 进行路径设置界面的可用性测试
  - _需求: 5.1, 5.2, 5.3, 5.4, 5.5, 5.6, 5.7, 5.8, 6.6, 6.7_