using System;
using System.IO;
using EmailGenerator.Services;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// ConfigurationService的单元测试
    /// </summary>
    [TestClass]
    public class ConfigurationServiceTests
    {
        private ConfigurationService _configService;
        private string _testConfigPath;

        [TestInitialize]
        public void Setup()
        {
            _configService = new ConfigurationService();
            _testConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.json");
            
            // 清理测试环境
            if (File.Exists(_testConfigPath))
            {
                File.Delete(_testConfigPath);
            }
        }

        [TestCleanup]
        public void Cleanup()
        {
            // 清理测试文件
            if (File.Exists(_testConfigPath))
            {
                File.Delete(_testConfigPath);
            }
        }

        [TestMethod]
        public void LoadDomain_ConfigFileNotExists_ReturnsDefaultDomain()
        {
            // Act
            string domain = _configService.LoadDomain();

            // Assert
            Assert.AreEqual("example.com", domain);
        }

        [TestMethod]
        public void SaveDomain_ValidDomain_ReturnsTrue()
        {
            // Arrange
            string testDomain = "test.com";

            // Act
            bool result = _configService.SaveDomain(testDomain);

            // Assert
            Assert.IsTrue(result);
            Assert.IsTrue(File.Exists(_testConfigPath));
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public void SaveDomain_EmptyDomain_ThrowsArgumentException()
        {
            // Act
            _configService.SaveDomain("");

            // Assert is handled by ExpectedException attribute
        }

        [TestMethod]
        public void SaveDomain_InvalidDomain_ThrowsArgumentException()
        {
            // Act & Assert
            Assert.ThrowsException<ArgumentException>(() => _configService.SaveDomain(".invalid.com"));
            Assert.ThrowsException<ArgumentException>(() => _configService.SaveDomain("invalid."));
            Assert.ThrowsException<ArgumentException>(() => _configService.SaveDomain("invalid"));
        }

        [TestMethod]
        public void SaveAndLoadDomain_ValidDomain_ReturnsCorrectDomain()
        {
            // Arrange
            string testDomain = "mydomain.org";

            // Act
            bool saveResult = _configService.SaveDomain(testDomain);
            string loadedDomain = _configService.LoadDomain();

            // Assert
            Assert.IsTrue(saveResult);
            Assert.AreEqual(testDomain, loadedDomain);
        }

        [TestMethod]
        public void GetDefaultDataPath_ReturnsValidPath()
        {
            // Act
            string dataPath = _configService.GetDefaultDataPath();

            // Assert
            Assert.IsNotNull(dataPath);
            Assert.IsTrue(dataPath.EndsWith("emails.json"));
        }

        [TestMethod]
        public void GetCustomConfigPath_InitiallyEmpty_ReturnsEmptyString()
        {
            // Act
            string customPath = _configService.GetCustomConfigPath();

            // Assert
            Assert.AreEqual("", customPath);
        }

        [TestMethod]
        public void GetCustomDataPath_InitiallyEmpty_ReturnsEmptyString()
        {
            // Act
            string customPath = _configService.GetCustomDataPath();

            // Assert
            Assert.AreEqual("", customPath);
        }

        [TestMethod]
        public void SetCustomConfigPath_ValidPath_ReturnsTrue()
        {
            // Arrange
            string testPath = Path.GetTempPath();

            // Act
            bool result = _configService.SetCustomConfigPath(testPath);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void SetCustomDataPath_ValidPath_ReturnsTrue()
        {
            // Arrange
            string testPath = Path.GetTempPath();

            // Act
            bool result = _configService.SetCustomDataPath(testPath);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void SetAndGetCustomConfigPath_ValidPath_ReturnsCorrectPath()
        {
            // Arrange
            string testPath = Path.GetTempPath();

            // Act
            bool setResult = _configService.SetCustomConfigPath(testPath);
            string getResult = _configService.GetCustomConfigPath();

            // Assert
            Assert.IsTrue(setResult);
            Assert.AreEqual(testPath, getResult);
        }

        [TestMethod]
        public void SetAndGetCustomDataPath_ValidPath_ReturnsCorrectPath()
        {
            // Arrange
            string testPath = Path.GetTempPath();

            // Act
            bool setResult = _configService.SetCustomDataPath(testPath);
            string getResult = _configService.GetCustomDataPath();

            // Assert
            Assert.IsTrue(setResult);
            Assert.AreEqual(testPath, getResult);
        }

        [TestMethod]
        public void ValidatePath_ValidPath_ReturnsTrue()
        {
            // Arrange
            string validPath = Path.GetTempPath();

            // Act
            bool result = _configService.ValidatePath(validPath);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void ValidatePath_EmptyPath_ReturnsTrue()
        {
            // Act
            bool result = _configService.ValidatePath("");

            // Assert
            Assert.IsTrue(result); // 空路径被认为是有效的（使用默认路径）
        }

        [TestMethod]
        public void ValidatePath_InvalidPath_ReturnsFalse()
        {
            // Arrange
            string invalidPath = @"Z:\NonExistentDrive\InvalidPath";

            // Act
            bool result = _configService.ValidatePath(invalidPath);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void ResetToDefaultPaths_AfterSettingCustomPaths_ReturnsTrue()
        {
            // Arrange
            string testPath = Path.GetTempPath();
            _configService.SetCustomConfigPath(testPath);
            _configService.SetCustomDataPath(testPath);

            // Act
            bool result = _configService.ResetToDefaultPaths();

            // Assert
            Assert.IsTrue(result);
            Assert.AreEqual("", _configService.GetCustomConfigPath());
            Assert.AreEqual("", _configService.GetCustomDataPath());
        }

        [TestMethod]
        public void GetCurrentConfigPath_WithoutCustomPath_ReturnsDefaultPath()
        {
            // Act
            string currentPath = _configService.GetCurrentConfigPath();

            // Assert
            Assert.IsNotNull(currentPath);
            Assert.IsTrue(currentPath.EndsWith("config.json"));
        }

        [TestMethod]
        public void GetCurrentDataPath_WithoutCustomPath_ReturnsDefaultPath()
        {
            // Act
            string currentPath = _configService.GetCurrentDataPath();

            // Assert
            Assert.IsNotNull(currentPath);
            Assert.IsTrue(currentPath.EndsWith("emails.json"));
        }

        [TestMethod]
        public void GetCurrentConfigPath_WithCustomPath_ReturnsCustomPath()
        {
            // Arrange
            string testPath = Path.GetTempPath();
            _configService.SetCustomConfigPath(testPath);

            // Act
            string currentPath = _configService.GetCurrentConfigPath();

            // Assert
            Assert.IsNotNull(currentPath);
            Assert.IsTrue(currentPath.StartsWith(testPath));
            Assert.IsTrue(currentPath.EndsWith("config.json"));
        }

        [TestMethod]
        public void GetCurrentDataPath_WithCustomPath_ReturnsCustomPath()
        {
            // Arrange
            string testPath = Path.GetTempPath();
            _configService.SetCustomDataPath(testPath);

            // Act
            string currentPath = _configService.GetCurrentDataPath();

            // Assert
            Assert.IsNotNull(currentPath);
            Assert.IsTrue(currentPath.StartsWith(testPath));
            Assert.IsTrue(currentPath.EndsWith("emails.json"));
        }

        [TestMethod]
        public void PathConfiguration_IntegrationTest()
        {
            // Arrange
            string testConfigPath = Path.GetTempPath();
            string testDataPath = Path.GetTempPath();
            string testDomain = "integration.test";

            // Act & Assert
            // 1. 设置自定义路径
            Assert.IsTrue(_configService.SetCustomConfigPath(testConfigPath));
            Assert.IsTrue(_configService.SetCustomDataPath(testDataPath));

            // 2. 验证路径设置
            Assert.AreEqual(testConfigPath, _configService.GetCustomConfigPath());
            Assert.AreEqual(testDataPath, _configService.GetCustomDataPath());

            // 3. 保存域名到自定义路径
            Assert.IsTrue(_configService.SaveDomain(testDomain));

            // 4. 从自定义路径加载域名
            Assert.AreEqual(testDomain, _configService.LoadDomain());

            // 5. 重置为默认路径
            Assert.IsTrue(_configService.ResetToDefaultPaths());
            Assert.AreEqual("", _configService.GetCustomConfigPath());
            Assert.AreEqual("", _configService.GetCustomDataPath());
        }
    }
}