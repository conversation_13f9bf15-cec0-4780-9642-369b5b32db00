using Microsoft.VisualStudio.TestTools.UnitTesting;
using EmailGenerator.Forms;
using EmailGenerator.Services;
using EmailGenerator.Models;
using System;
using System.IO;
using System.Windows.Forms;
using System.Threading;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// 用户界面可用性测试类
    /// 验证界面操作的正确性和用户体验
    /// </summary>
    [TestClass]
    public class UsabilityTests
    {
        private MainForm mainForm;
        private string testConfigPath;
        private string testDataPath;

        [TestInitialize]
        public void Setup()
        {
            // 创建测试专用的临时文件路径
            testConfigPath = Path.Combine(Path.GetTempPath(), $"usability_config_{Guid.NewGuid()}.json");
            testDataPath = Path.Combine(Path.GetTempPath(), $"usability_emails_{Guid.NewGuid()}.json");
            
            // 在STA线程中创建窗体
            Thread staThread = new Thread(() =>
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                mainForm = new MainForm();
            });
            staThread.SetApartmentState(ApartmentState.STA);
            staThread.Start();
            staThread.Join();
        }

        [TestCleanup]
        public void Cleanup()
        {
            // 清理测试文件
            if (File.Exists(testConfigPath))
                File.Delete(testConfigPath);
            if (File.Exists(testDataPath))
                File.Delete(testDataPath);
                
            // 关闭窗体
            if (mainForm != null && !mainForm.IsDisposed)
            {
                mainForm.Invoke(new Action(() => mainForm.Close()));
            }
        }

        /// <summary>
        /// 测试界面初始状态
        /// 需求: 5.1, 5.2
        /// </summary>
        [TestMethod]
        public void TestInitialUIState()
        {
            Assert.IsNotNull(mainForm, "主窗体应该成功创建");
            
            // 验证窗体基本属性
            Assert.IsFalse(string.IsNullOrEmpty(mainForm.Text), "窗体标题不应该为空");
            Assert.IsTrue(mainForm.Width > 0 && mainForm.Height > 0, "窗体应该有合理的尺寸");
            
            // 验证控件存在性（通过反射或公共属性访问）
            var controls = mainForm.Controls;
            Assert.IsTrue(controls.Count > 0, "主窗体应该包含控件");
        }
    }
}