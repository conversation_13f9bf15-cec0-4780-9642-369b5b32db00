namespace EmailGenerator.Models
{
    /// <summary>
    /// 错误消息常量类
    /// </summary>
    public static class ErrorMessages
    {
        // 域名相关错误
        public const string DOMAIN_EMPTY = "请输入域名";
        public const string DOMAIN_INVALID_FORMAT = "域名格式无效，请输入正确的域名格式（如：example.com）";
        public const string DOMAIN_SAVE_FAILED = "域名保存失败，请重试";
        public const string DOMAIN_LOAD_FAILED = "加载域名配置失败";

        // 邮箱生成相关错误
        public const string EMAIL_GENERATION_FAILED = "邮箱生成失败，请重试";
        public const string EMAIL_SAVE_FAILED = "邮箱保存失败，请重试";
        public const string EMAIL_ALREADY_EXISTS = "邮箱地址已存在";
        public const string EMAIL_INVALID = "邮箱地址无效";

        // 邮箱操作相关错误
        public const string EMAIL_NOT_SELECTED = "请先选择要操作的邮箱";
        public const string EMAIL_COPY_FAILED = "复制邮箱失败，请重试";
        public const string EMAIL_DELETE_FAILED = "删除邮箱失败，请重试";
        public const string EMAIL_DEACTIVATE_FAILED = "作废邮箱失败，请重试";
        public const string EMAIL_NOT_FOUND = "找不到指定的邮箱记录";
        public const string EMAIL_ALREADY_DEACTIVATED = "该邮箱已经是作废状态";

        // 文件操作相关错误
        public const string FILE_ACCESS_ERROR = "无法访问数据文件，请检查文件权限";
        public const string FILE_READ_ERROR = "读取文件失败";
        public const string FILE_WRITE_ERROR = "写入文件失败";
        public const string FILE_CREATE_ERROR = "创建文件失败";
        public const string FILE_DELETE_ERROR = "删除文件失败";

        // 数据相关错误
        public const string DATA_LOAD_FAILED = "加载数据失败";
        public const string DATA_SAVE_FAILED = "保存数据失败";
        public const string DATA_PARSE_ERROR = "数据解析失败，文件可能已损坏";
        public const string DATA_VALIDATION_ERROR = "数据验证失败";

        // 配置相关错误
        public const string CONFIG_LOAD_FAILED = "加载配置失败";
        public const string CONFIG_SAVE_FAILED = "保存配置失败";
        public const string CONFIG_INVALID = "配置文件无效";

        // 系统相关错误
        public const string SYSTEM_ERROR = "系统错误，请重试";
        public const string UNKNOWN_ERROR = "发生未知错误";
        public const string OPERATION_CANCELLED = "操作已取消";
        public const string OPERATION_TIMEOUT = "操作超时";

        // 路径相关错误
        public const string PATH_INVALID = "路径无效，请选择有效的文件夹路径";
        public const string PATH_NOT_WRITABLE = "路径不可写，请检查文件夹权限或选择其他路径";
        public const string PATH_CREATE_FAILED = "无法创建目录，请检查权限或选择其他路径";
        public const string PATH_MIGRATION_FAILED = "数据迁移失败，将回退到原路径";
        public const string PATH_BACKUP_FAILED = "创建数据备份失败";
        public const string PATH_ROLLBACK_FAILED = "回滚到原路径失败";
        
        // 详细路径错误消息
        public const string PATH_EMPTY = "路径不能为空";
        public const string PATH_TOO_LONG = "路径长度超过系统限制";
        public const string PATH_CONTAINS_INVALID_CHARS = "路径包含无效字符";
        public const string PATH_NOT_ABSOLUTE = "请提供完整的绝对路径";
        public const string PATH_NETWORK_NOT_SUPPORTED = "不支持网络路径，请选择本地路径";
        public const string PATH_DRIVE_NOT_EXIST = "指定的驱动器不存在";
        public const string PATH_DIRECTORY_NOT_EXIST = "指定的目录不存在";
        public const string PATH_ACCESS_DENIED = "访问路径被拒绝，请检查权限设置";
        public const string PATH_DISK_FULL = "磁盘空间不足，无法在此路径创建文件";
        public const string PATH_READ_ONLY = "路径为只读，无法写入文件";
        public const string PATH_RESERVED_NAME = "路径包含系统保留名称";
        
        // 路径权限相关错误
        public const string PATH_PERMISSION_DENIED = "没有访问此路径的权限";
        public const string PATH_PERMISSION_INSUFFICIENT = "权限不足，无法在此路径进行操作";
        public const string PATH_PERMISSION_ADMIN_REQUIRED = "此操作需要管理员权限";
        public const string PATH_PERMISSION_USER_GUIDANCE = "请右键选择'以管理员身份运行'或选择其他有权限的路径";
        
        // 路径创建失败详细错误
        public const string PATH_CREATE_PERMISSION_DENIED = "创建目录失败：权限不足";
        public const string PATH_CREATE_DISK_FULL = "创建目录失败：磁盘空间不足";
        public const string PATH_CREATE_NAME_TOO_LONG = "创建目录失败：路径名称过长";
        public const string PATH_CREATE_INVALID_NAME = "创建目录失败：目录名称包含无效字符";
        public const string PATH_CREATE_ALREADY_EXISTS_AS_FILE = "创建目录失败：同名文件已存在";
        public const string PATH_CREATE_PARENT_NOT_EXIST = "创建目录失败：父目录不存在";
        
        // 路径配置保存失败回退机制
        public const string PATH_CONFIG_SAVE_FAILED_FALLBACK = "路径配置保存失败，已回退到默认路径";
        public const string PATH_CONFIG_INVALID_FALLBACK = "路径配置无效，已回退到默认路径";
        public const string PATH_CONFIG_CORRUPTED_FALLBACK = "路径配置文件损坏，已重置为默认设置";
        public const string PATH_CONFIG_PERMISSION_FALLBACK = "无权限保存路径配置，已使用默认路径";
        
        // 用户指导消息
        public const string PATH_GUIDANCE_SELECT_VALID = "请选择一个有效的本地文件夹路径";
        public const string PATH_GUIDANCE_CHECK_PERMISSION = "请检查文件夹权限，确保程序可以读写该路径";
        public const string PATH_GUIDANCE_CREATE_FOLDER = "如果文件夹不存在，程序将尝试自动创建";
        public const string PATH_GUIDANCE_ADMIN_RIGHTS = "如需访问系统文件夹，请以管理员身份运行程序";
        public const string PATH_GUIDANCE_ALTERNATIVE = "建议选择用户文档文件夹或桌面等有权限的位置";

        // 数据迁移相关错误
        public const string MIGRATION_VALIDATION_FAILED = "迁移路径验证失败";
        public const string MIGRATION_BACKUP_FAILED = "创建迁移备份失败";
        public const string MIGRATION_EXECUTION_FAILED = "执行数据迁移失败";
        public const string MIGRATION_ROLLBACK_FAILED = "迁移回滚失败";
        public const string MIGRATION_INTEGRITY_CHECK_FAILED = "迁移后数据完整性检查失败";
        public const string MIGRATION_USER_CANCELLED = "用户取消了数据迁移操作";
        public const string MIGRATION_CLEANUP_FAILED = "清理迁移备份文件失败";
        public const string MIGRATION_RESTORE_FAILED = "从备份恢复数据失败";
        public const string MIGRATION_PROGRESS_ERROR = "迁移进度更新失败";

        // 输入验证错误
        public const string INPUT_EMPTY = "输入不能为空";
        public const string INPUT_INVALID = "输入格式无效";
        public const string INPUT_TOO_LONG = "输入内容过长";
        public const string INPUT_TOO_SHORT = "输入内容过短";
    }
}