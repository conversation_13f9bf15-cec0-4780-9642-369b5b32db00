using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using EmailGenerator.Models;
using EmailGenerator.Services;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// DataService自定义路径功能的集成测试
    /// </summary>
    [TestClass]
    public class DataServiceCustomPathTests
    {
        private string _testDirectory;
        private string _customDataPath;
        private string _defaultDataPath;
        private PathManagerService _pathManager;
        private ConfigurationService _configService;
        private DataService _dataService;

        [TestInitialize]
        public void Setup()
        {
            // 创建测试目录
            _testDirectory = Path.Combine(Path.GetTempPath(), "EmailGeneratorTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);

            _customDataPath = Path.Combine(_testDirectory, "CustomData");
            _defaultDataPath = Path.Combine(_testDirectory, "DefaultData");
            
            Directory.CreateDirectory(_customDataPath);
            Directory.CreateDirectory(_defaultDataPath);

            _pathManager = new PathManagerService();
            _configService = new ConfigurationService(_pathManager);
            
            // 使用默认数据路径创建DataService，确保测试环境干净
            string cleanDataPath = Path.Combine(_defaultDataPath, "emails.json");
            _dataService = new DataService(cleanDataPath);
        }

        [TestCleanup]
        public void Cleanup()
        {
            try
            {
                if (Directory.Exists(_testDirectory))
                {
                    Directory.Delete(_testDirectory, true);
                }
            }
            catch
            {
                // 清理失败不影响测试结果
            }
        }

        [TestMethod]
        public void Constructor_WithCustomPath_ShouldUseCustomPath()
        {
            // Arrange
            string customPath = Path.Combine(_customDataPath, "emails.json");

            // Act
            var dataService = new DataService(customPath);

            // Assert
            Assert.AreEqual(customPath, dataService.GetCurrentDataFilePath());
        }

        [TestMethod]
        public void Constructor_WithConfigurationService_ShouldUseConfiguredPath()
        {
            // Arrange
            var tempConfigService = new ConfigurationService(_pathManager);
            tempConfigService.SetCustomDataPath(_customDataPath);

            // Act
            var dataService = new DataService(tempConfigService, _pathManager);

            // Assert
            string expectedPath = Path.Combine(_customDataPath, "emails.json");
            Assert.AreEqual(expectedPath, dataService.GetCurrentDataFilePath());
        }

        [TestMethod]
        public void UpdateDataPath_WithValidPath_ShouldUpdateSuccessfully()
        {
            // Arrange
            string newPath = _customDataPath;

            // Act
            bool result = _dataService.UpdateDataPath(newPath);

            // Assert
            Assert.IsTrue(result);
            string expectedPath = Path.Combine(newPath, "emails.json");
            Assert.AreEqual(expectedPath, _dataService.GetCurrentDataFilePath());
        }

        [TestMethod]
        public void UpdateDataPath_WithInvalidPath_ShouldReturnFalse()
        {
            // Arrange
            string invalidPath = "Z:\\NonExistentPath\\Invalid";

            // Act
            bool result = _dataService.UpdateDataPath(invalidPath);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void UpdateDataPath_WithExistingData_ShouldMigrateData()
        {
            // Arrange
            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                CreatedDate = DateTime.Now,
                Domain = "example.com",
                Username = "test",
                Status = EmailStatus.Active
            };

            // 先在默认路径添加数据
            _dataService.AddEmail(testEmail);
            var originalEmails = _dataService.LoadEmails();
            Assert.AreEqual(1, originalEmails.Count);

            // Act - 迁移到新路径
            bool result = _dataService.UpdateDataPath(_customDataPath);

            // Assert
            Assert.IsTrue(result);
            
            // 验证数据已迁移
            var migratedEmails = _dataService.LoadEmails();
            Assert.AreEqual(1, migratedEmails.Count);
            Assert.AreEqual(testEmail.Email, migratedEmails[0].Email);
            
            // 验证新路径文件存在
            string newDataFile = Path.Combine(_customDataPath, "emails.json");
            Assert.IsTrue(File.Exists(newDataFile));
        }

        [TestMethod]
        public void LoadEmails_WithCustomPath_ShouldLoadFromCorrectPath()
        {
            // Arrange
            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                CreatedDate = DateTime.Now,
                Domain = "example.com",
                Username = "custom",
                Status = EmailStatus.Active
            };

            _dataService.UpdateDataPath(_customDataPath);
            _dataService.AddEmail(testEmail);

            // Act
            var emails = _dataService.LoadEmails();

            // Assert
            Assert.AreEqual(1, emails.Count);
            Assert.AreEqual(testEmail.Email, emails[0].Email);
        }

        [TestMethod]
        public void SaveEmails_WithCustomPath_ShouldSaveToCorrectPath()
        {
            // Arrange
            var testEmails = new List<EmailRecord>
            {
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    CreatedDate = DateTime.Now,
                    Domain = "example.com",
                    Username = "save1",
                    Status = EmailStatus.Active
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    CreatedDate = DateTime.Now,
                    Domain = "example.com",
                    Username = "save2",
                    Status = EmailStatus.Deactivated,
                    DeactivatedDate = DateTime.Now
                }
            };

            _dataService.UpdateDataPath(_customDataPath);

            // Act
            bool result = _dataService.SaveEmails(testEmails);

            // Assert
            Assert.IsTrue(result);
            
            // 验证文件存在于正确路径
            string dataFile = Path.Combine(_customDataPath, "emails.json");
            Assert.IsTrue(File.Exists(dataFile));
            
            // 验证数据正确保存
            var loadedEmails = _dataService.LoadEmails();
            Assert.AreEqual(2, loadedEmails.Count);
        }

        [TestMethod]
        public void SafeExecuteWithFallback_WhenCustomPathFails_ShouldFallbackToDefault()
        {
            // Arrange
            // 设置一个无效的自定义路径
            string invalidPath = Path.Combine("Z:\\InvalidPath", "emails.json");
            var dataService = new DataService(invalidPath);

            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                CreatedDate = DateTime.Now,
                Domain = "example.com",
                Username = "fallback",
                Status = EmailStatus.Active
            };

            // Act & Assert - 应该能够正常工作（回退到默认路径）
            bool addResult = dataService.AddEmail(testEmail);
            Assert.IsTrue(addResult);

            var emails = dataService.LoadEmails();
            Assert.AreEqual(1, emails.Count);
            Assert.AreEqual(testEmail.Email, emails[0].Email);
        }

        [TestMethod]
        public void RefreshDataPath_ShouldUpdateToCurrentConfiguredPath()
        {
            // Arrange
            var tempConfigService = new ConfigurationService(_pathManager);
            var dataService = new DataService(tempConfigService, _pathManager);
            string originalPath = dataService.GetCurrentDataFilePath();
            
            // 更改配置
            tempConfigService.SetCustomDataPath(_customDataPath);

            // Act
            bool result = dataService.RefreshDataPath();

            // Assert
            Assert.IsTrue(result);
            string newPath = dataService.GetCurrentDataFilePath();
            Assert.AreNotEqual(originalPath, newPath);
            Assert.IsTrue(newPath.Contains(_customDataPath));
        }

        [TestMethod]
        public void ValidateCurrentPath_WithValidPath_ShouldReturnTrue()
        {
            // Arrange
            _dataService.UpdateDataPath(_customDataPath);

            // Act
            bool result = _dataService.ValidateCurrentPath();

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void ValidateCurrentPath_WithInvalidPath_ShouldReturnFalse()
        {
            // Arrange
            string invalidPath = Path.Combine("Z:\\InvalidPath", "emails.json");
            var dataService = new DataService(invalidPath);

            // Act
            bool result = dataService.ValidateCurrentPath();

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void DataMigration_WhenSourceFileNotExists_ShouldCreateEmptyFileAtDestination()
        {
            // Arrange
            string sourcePath = Path.Combine(_defaultDataPath, "emails.json");
            string destPath = _customDataPath;
            
            // 确保源文件不存在
            if (File.Exists(sourcePath))
            {
                File.Delete(sourcePath);
            }

            // Act
            bool result = _dataService.UpdateDataPath(destPath);

            // Assert
            Assert.IsTrue(result);
            
            // 验证目标文件被创建
            string destFile = Path.Combine(destPath, "emails.json");
            Assert.IsTrue(File.Exists(destFile));
            
            // 验证文件内容为空列表
            var emails = _dataService.LoadEmails();
            Assert.AreEqual(0, emails.Count);
        }

        [TestMethod]
        public void DataMigration_WithBackupAndRestore_ShouldHandleFailureCorrectly()
        {
            // Arrange
            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                CreatedDate = DateTime.Now,
                Domain = "example.com",
                Username = "backup",
                Status = EmailStatus.Active
            };

            // 添加测试数据
            _dataService.AddEmail(testEmail);
            var originalEmails = _dataService.LoadEmails();
            Assert.AreEqual(1, originalEmails.Count);

            // 创建一个只读目录来模拟迁移失败
            string readOnlyPath = Path.Combine(_testDirectory, "ReadOnly");
            Directory.CreateDirectory(readOnlyPath);
            
            try
            {
                // 设置目录为只读（在某些系统上可能不起作用）
                DirectoryInfo dirInfo = new DirectoryInfo(readOnlyPath);
                dirInfo.Attributes |= FileAttributes.ReadOnly;

                // Act - 尝试迁移到只读目录
                bool result = _dataService.UpdateDataPath(readOnlyPath);

                // Assert - 迁移应该失败，但原数据应该保持完整
                // 注意：在某些系统上，只读属性可能不会阻止写入，所以这个测试可能会通过
                var emailsAfterFailedMigration = _dataService.LoadEmails();
                Assert.AreEqual(1, emailsAfterFailedMigration.Count);
                Assert.AreEqual(testEmail.Email, emailsAfterFailedMigration[0].Email);
            }
            finally
            {
                // 清理：移除只读属性
                try
                {
                    DirectoryInfo dirInfo = new DirectoryInfo(readOnlyPath);
                    dirInfo.Attributes &= ~FileAttributes.ReadOnly;
                }
                catch
                {
                    // 忽略清理错误
                }
            }
        }

        [TestMethod]
        public void MultipleOperations_WithCustomPath_ShouldWorkCorrectly()
        {
            // Arrange
            _dataService.UpdateDataPath(_customDataPath);

            var email1 = new EmailRecord
            {
                Email = "<EMAIL>",
                CreatedDate = DateTime.Now,
                Domain = "example.com",
                Username = "multi1",
                Status = EmailStatus.Active
            };

            var email2 = new EmailRecord
            {
                Email = "<EMAIL>",
                CreatedDate = DateTime.Now,
                Domain = "example.com",
                Username = "multi2",
                Status = EmailStatus.Active
            };

            // Act & Assert
            // 添加邮箱
            Assert.IsTrue(_dataService.AddEmail(email1));
            Assert.IsTrue(_dataService.AddEmail(email2));

            // 验证加载
            var emails = _dataService.LoadEmails();
            Assert.AreEqual(2, emails.Count);

            // 作废一个邮箱
            Assert.IsTrue(_dataService.DeactivateEmail(email1.Email));

            // 验证状态更新
            emails = _dataService.LoadEmails();
            var deactivatedEmail = emails.FirstOrDefault(e => e.Email == email1.Email);
            Assert.IsNotNull(deactivatedEmail);
            Assert.AreEqual(EmailStatus.Deactivated, deactivatedEmail.Status);
            Assert.IsNotNull(deactivatedEmail.DeactivatedDate);

            // 删除一个邮箱
            Assert.IsTrue(_dataService.DeleteEmail(email2.Email));

            // 验证删除
            emails = _dataService.LoadEmails();
            Assert.AreEqual(1, emails.Count);
            Assert.AreEqual(email1.Email, emails[0].Email);

            // 验证文件存在于正确路径
            string dataFile = Path.Combine(_customDataPath, "emails.json");
            Assert.IsTrue(File.Exists(dataFile));
        }
    }
}