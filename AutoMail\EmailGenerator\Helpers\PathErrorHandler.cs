using System;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using EmailGenerator.Models;

namespace EmailGenerator.Helpers
{
    /// <summary>
    /// 路径错误处理类，提供详细的路径错误分析和用户指导
    /// </summary>
    public static class PathErrorHandler
    {
        /// <summary>
        /// 分析路径错误并返回详细的错误消息
        /// </summary>
        /// <param name="path">要分析的路径</param>
        /// <param name="exception">发生的异常（可选）</param>
        /// <returns>详细的错误消息</returns>
        public static string AnalyzePathError(string path, Exception exception = null)
        {
            try
            {
                // 检查路径是否为空
                if (string.IsNullOrWhiteSpace(path))
                {
                    return ErrorMessages.PATH_EMPTY;
                }

                // 检查路径长度
                if (path.Length > 260) // Windows路径长度限制
                {
                    return ErrorMessages.PATH_TOO_LONG;
                }

                // 检查是否为网络路径
                if (path.StartsWith(@"\\"))
                {
                    return ErrorMessages.PATH_NETWORK_NOT_SUPPORTED;
                }

                // 检查是否为绝对路径
                if (!Path.IsPathRooted(path))
                {
                    return ErrorMessages.PATH_NOT_ABSOLUTE;
                }

                // 检查是否包含无效字符（只检查路径字符，不检查文件名字符）
                char[] invalidPathChars = Path.GetInvalidPathChars();
                
                if (path.Any(c => invalidPathChars.Contains(c)))
                {
                    return ErrorMessages.PATH_CONTAINS_INVALID_CHARS;
                }

                // 检查是否包含文件名无效字符（对于包含<>等字符的路径）
                char[] invalidFileNameChars = Path.GetInvalidFileNameChars();
                string[] pathParts = path.Split(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar);
                foreach (string part in pathParts)
                {
                    if (!string.IsNullOrEmpty(part) && part.Any(c => invalidFileNameChars.Contains(c)))
                    {
                        return ErrorMessages.PATH_CONTAINS_INVALID_CHARS;
                    }
                }

                // 检查是否包含保留名称
                try
                {
                    string fileName = Path.GetFileName(path);
                    string[] reservedNames = { "CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9" };
                    if (!string.IsNullOrEmpty(fileName) && reservedNames.Contains(fileName.ToUpper()))
                    {
                        return ErrorMessages.PATH_RESERVED_NAME;
                    }
                }
                catch
                {
                    // 如果获取文件名失败，可能是路径格式问题
                    return ErrorMessages.PATH_CONTAINS_INVALID_CHARS;
                }

                // 检查驱动器是否存在
                try
                {
                    string root = Path.GetPathRoot(path);
                    if (!string.IsNullOrEmpty(root) && !Directory.Exists(root))
                    {
                        return ErrorMessages.PATH_DRIVE_NOT_EXIST;
                    }
                }
                catch
                {
                    return ErrorMessages.PATH_DRIVE_NOT_EXIST;
                }

                // 根据异常类型提供具体错误信息
                if (exception != null)
                {
                    return AnalyzeExceptionError(path, exception);
                }

                // 如果没有明显错误，检查目录是否存在
                if (!Directory.Exists(path))
                {
                    return ErrorMessages.PATH_DIRECTORY_NOT_EXIST;
                }

                return ErrorMessages.PATH_INVALID;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"分析路径错误时发生异常: {ex.Message}");
                return ErrorMessages.PATH_INVALID;
            }
        }

        /// <summary>
        /// 根据异常类型分析错误
        /// </summary>
        /// <param name="path">路径</param>
        /// <param name="exception">异常</param>
        /// <returns>错误消息</returns>
        private static string AnalyzeExceptionError(string path, Exception exception)
        {
            switch (exception)
            {
                case UnauthorizedAccessException _:
                    return ErrorMessages.PATH_ACCESS_DENIED;
                
                case DirectoryNotFoundException _:
                    return ErrorMessages.PATH_DIRECTORY_NOT_EXIST;
                
                case DriveNotFoundException _:
                    return ErrorMessages.PATH_DRIVE_NOT_EXIST;
                
                case PathTooLongException _:
                    return ErrorMessages.PATH_TOO_LONG;
                
                case IOException ioEx when ioEx.Message.Contains("disk") || ioEx.Message.Contains("space"):
                    return ErrorMessages.PATH_DISK_FULL;
                
                case IOException ioEx when ioEx.Message.Contains("read-only") || ioEx.Message.Contains("readonly"):
                    return ErrorMessages.PATH_READ_ONLY;
                
                case ArgumentException _:
                    return ErrorMessages.PATH_CONTAINS_INVALID_CHARS;
                
                default:
                    return $"{ErrorMessages.PATH_INVALID}: {exception.Message}";
            }
        }

        /// <summary>
        /// 分析路径创建失败的原因
        /// </summary>
        /// <param name="path">要创建的路径</param>
        /// <param name="exception">创建时发生的异常</param>
        /// <returns>详细的错误消息</returns>
        public static string AnalyzePathCreationError(string path, Exception exception)
        {
            try
            {
                if (exception is UnauthorizedAccessException)
                {
                    return ErrorMessages.PATH_CREATE_PERMISSION_DENIED;
                }

                if (exception is PathTooLongException)
                {
                    return ErrorMessages.PATH_CREATE_NAME_TOO_LONG;
                }

                if (exception is ArgumentException)
                {
                    return ErrorMessages.PATH_CREATE_INVALID_NAME;
                }

                if (exception is DirectoryNotFoundException)
                {
                    return ErrorMessages.PATH_CREATE_PARENT_NOT_EXIST;
                }

                if (exception is IOException ioEx)
                {
                    if (ioEx.Message.Contains("disk") || ioEx.Message.Contains("space"))
                    {
                        return ErrorMessages.PATH_CREATE_DISK_FULL;
                    }
                    
                    if (ioEx.Message.Contains("already exists") && File.Exists(path))
                    {
                        return ErrorMessages.PATH_CREATE_ALREADY_EXISTS_AS_FILE;
                    }
                }

                return $"{ErrorMessages.PATH_CREATE_FAILED}: {exception.Message}";
            }
            catch
            {
                return ErrorMessages.PATH_CREATE_FAILED;
            }
        }

        /// <summary>
        /// 获取路径权限错误的用户指导消息
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>用户指导消息</returns>
        public static string GetPermissionGuidance(string path)
        {
            try
            {
                // 检查是否为系统目录
                string[] systemPaths = { 
                    Environment.GetFolderPath(Environment.SpecialFolder.Windows),
                    Environment.GetFolderPath(Environment.SpecialFolder.System),
                    Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles),
                    Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86)
                };

                if (systemPaths.Any(sysPath => !string.IsNullOrEmpty(sysPath) && path.StartsWith(sysPath, StringComparison.OrdinalIgnoreCase)))
                {
                    return ErrorMessages.PATH_GUIDANCE_ADMIN_RIGHTS;
                }

                return ErrorMessages.PATH_GUIDANCE_CHECK_PERMISSION;
            }
            catch
            {
                return ErrorMessages.PATH_GUIDANCE_CHECK_PERMISSION;
            }
        }

        /// <summary>
        /// 显示路径错误的详细对话框
        /// </summary>
        /// <param name="path">出错的路径</param>
        /// <param name="exception">异常信息</param>
        /// <param name="owner">父窗体</param>
        public static void ShowPathErrorDialog(string path, Exception exception = null, IWin32Window owner = null)
        {
            try
            {
                string errorMessage = AnalyzePathError(path, exception);
                string guidance = GetPermissionGuidance(path);
                
                string fullMessage = $"{errorMessage}\n\n路径: {path}\n\n{guidance}\n\n{ErrorMessages.PATH_GUIDANCE_ALTERNATIVE}";
                
                MessageHelper.ShowErrorMessage(fullMessage, "路径错误", owner);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示路径错误对话框失败: {ex.Message}");
                MessageHelper.ShowErrorMessage(ErrorMessages.PATH_INVALID, "路径错误", owner);
            }
        }

        /// <summary>
        /// 显示路径创建失败的详细对话框
        /// </summary>
        /// <param name="path">要创建的路径</param>
        /// <param name="exception">创建时的异常</param>
        /// <param name="owner">父窗体</param>
        public static void ShowPathCreationErrorDialog(string path, Exception exception, IWin32Window owner = null)
        {
            try
            {
                string errorMessage = AnalyzePathCreationError(path, exception);
                string guidance = GetPermissionGuidance(path);
                
                string fullMessage = $"{errorMessage}\n\n路径: {path}\n\n{guidance}\n\n{ErrorMessages.PATH_GUIDANCE_CREATE_FOLDER}";
                
                MessageHelper.ShowErrorMessage(fullMessage, "创建目录失败", owner);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示路径创建错误对话框失败: {ex.Message}");
                MessageHelper.ShowErrorMessage(ErrorMessages.PATH_CREATE_FAILED, "创建目录失败", owner);
            }
        }

        /// <summary>
        /// 验证路径并提供详细的验证结果
        /// </summary>
        /// <param name="path">要验证的路径</param>
        /// <returns>验证结果</returns>
        public static PathValidationResult ValidatePathDetailed(string path)
        {
            var result = new PathValidationResult
            {
                IsValid = false,
                Path = path,
                ErrorMessage = string.Empty,
                Guidance = string.Empty
            };

            try
            {
                // 基本验证
                if (string.IsNullOrWhiteSpace(path))
                {
                    result.ErrorMessage = ErrorMessages.PATH_EMPTY;
                    result.Guidance = ErrorMessages.PATH_GUIDANCE_SELECT_VALID;
                    return result;
                }

                // 详细验证
                string errorMessage = AnalyzePathError(path);
                if (errorMessage != ErrorMessages.PATH_INVALID)
                {
                    result.ErrorMessage = errorMessage;
                    result.Guidance = GetPermissionGuidance(path);
                    return result;
                }

                // 检查写权限
                if (!IsPathWritable(path))
                {
                    result.ErrorMessage = ErrorMessages.PATH_NOT_WRITABLE;
                    result.Guidance = GetPermissionGuidance(path);
                    return result;
                }

                result.IsValid = true;
                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = AnalyzePathError(path, ex);
                result.Guidance = GetPermissionGuidance(path);
                return result;
            }
        }

        /// <summary>
        /// 检查路径是否可写
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <returns>是否可写</returns>
        private static bool IsPathWritable(string path)
        {
            try
            {
                // 如果是文件路径，获取其目录
                string directoryPath = path;
                if (Path.HasExtension(path))
                {
                    directoryPath = Path.GetDirectoryName(path);
                }

                if (string.IsNullOrEmpty(directoryPath))
                    return false;

                // 找到第一个存在的父目录
                string currentPath = directoryPath;
                while (!Directory.Exists(currentPath))
                {
                    string parentPath = Path.GetDirectoryName(currentPath);
                    if (string.IsNullOrEmpty(parentPath) || parentPath == currentPath)
                    {
                        return false;
                    }
                    currentPath = parentPath;
                }

                // 在存在的目录中测试写权限
                string tempFile = Path.Combine(currentPath, Path.GetRandomFileName());
                try
                {
                    using (FileStream fs = File.Create(tempFile))
                    {
                        // 文件创建成功，说明有写权限
                    }
                    File.Delete(tempFile);
                    return true;
                }
                catch
                {
                    return false;
                }
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// 路径验证结果
    /// </summary>
    public class PathValidationResult
    {
        /// <summary>
        /// 路径是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 验证的路径
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 用户指导消息
        /// </summary>
        public string Guidance { get; set; }
    }
}