using System;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using EmailGenerator.Forms;
using EmailGenerator.Services;
using EmailGenerator.Models;

namespace EmailGenerator.Tests
{
    [TestClass]
    public class EmailGenerationIntegrationTests
    {
        private MainForm _mainForm;
        private string _testConfigPath;
        private string _testDataPath;
        private string _originalConfigPath;
        private string _originalDataPath;

        [TestInitialize]
        public void Setup()
        {
            _originalConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.json");
            _originalDataPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "emails.json");
            _testConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "test_config.json");
            _testDataPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "test_emails.json");
            
            BackupOriginalFiles();
            _mainForm = new MainForm();
        }

        [TestCleanup]
        public void Cleanup()
        {
            CleanupTestFiles();
            RestoreOriginalFiles();
            _mainForm?.Dispose();
        }

        [TestMethod]
        public void TestEmailGenerationWithValidDomain()
        {
            var configService = new ConfigurationService();
            var dataService = new DataService();
            var emailService = new EmailService(dataService);
            
            string testDomain = "test.example.com";
            configService.SaveDomain(testDomain);

            var emailRecord = emailService.CreateEmailRecord(testDomain);

            Assert.IsNotNull(emailRecord, "邮箱记录不应为null");
            Assert.IsTrue(emailRecord.Email.EndsWith($"@{testDomain}"), "邮箱应该使用指定的域名");
            Assert.AreEqual(EmailStatus.Active, emailRecord.Status, "新生成的邮箱状态应该是Active");
            Assert.IsTrue(emailRecord.CreatedDate <= DateTime.Now, "创建时间应该是当前时间或之前");
            Assert.IsNull(emailRecord.DeactivatedDate, "新邮箱的作废时间应该为null");
        }

        [TestMethod]
        public void TestEmailGenerationUniqueness()
        {
            var dataService = new DataService();
            var emailService = new EmailService(dataService);
            string testDomain = "unique.test.com";

            var email1 = emailService.GenerateUniqueEmail(testDomain);
            var email2 = emailService.GenerateUniqueEmail(testDomain);

            Assert.IsNotNull(email1, "第一个邮箱不应为null");
            Assert.IsNotNull(email2, "第二个邮箱不应为null");
            Assert.AreNotEqual(email1, email2, "生成的邮箱应该是唯一的");
        }

        [TestMethod]
        public void TestEmailGenerationWithExistingEmails()
        {
            var dataService = new DataService();
            var emailService = new EmailService(dataService);
            string testDomain = "existing.test.com";

            var existingRecord = new EmailRecord
            {
                Email = "<EMAIL>",
                Username = "existing",
                Domain = testDomain,
                CreatedDate = DateTime.Now,
                Status = EmailStatus.Active
            };
            
            dataService.AddEmail(existingRecord);

            var newEmail = emailService.GenerateUniqueEmail(testDomain);

            Assert.IsNotNull(newEmail, "新邮箱不应为null");
            Assert.AreNotEqual("<EMAIL>", newEmail, "新邮箱不应与现有邮箱重复");
            Assert.IsTrue(newEmail.EndsWith($"@{testDomain}"), "新邮箱应该使用指定的域名");
        }

        [TestMethod]
        public void TestEmailGenerationWithInvalidDomain()
        {
            var dataService = new DataService();
            var emailService = new EmailService(dataService);

            string[] invalidDomains = { "", "   ", "invalid", ".invalid.com", "invalid.com.", "invalid..com" };

            foreach (string invalidDomain in invalidDomains)
            {
                var emailRecord = emailService.CreateEmailRecord(invalidDomain);
                Assert.IsNull(emailRecord, $"无效域名 '{invalidDomain}' 不应生成邮箱记录");
            }
        }

        [TestMethod]
        public void TestEmailGenerationAndPersistence()
        {
            var configService = new ConfigurationService();
            var dataService = new DataService();
            var emailService = new EmailService(dataService);
            
            string testDomain = "persist.test.com";
            configService.SaveDomain(testDomain);

            var emailRecord = emailService.CreateEmailRecord(testDomain);
            Assert.IsNotNull(emailRecord, "邮箱记录不应为null");

            bool saveResult = dataService.AddEmail(emailRecord);
            Assert.IsTrue(saveResult, "邮箱保存应该成功");

            var loadedEmails = dataService.LoadEmails();
            var savedEmail = loadedEmails.FirstOrDefault(e => e.Email == emailRecord.Email);
            
            Assert.IsNotNull(savedEmail, "保存的邮箱应该能够被加载");
            Assert.AreEqual(emailRecord.Email, savedEmail.Email, "邮箱地址应该一致");
            Assert.AreEqual(emailRecord.Domain, savedEmail.Domain, "域名应该一致");
            Assert.AreEqual(emailRecord.Username, savedEmail.Username, "用户名应该一致");
            Assert.AreEqual(EmailStatus.Active, savedEmail.Status, "状态应该是Active");
        }

        [TestMethod]
        public void TestMultipleEmailGeneration()
        {
            var dataService = new DataService();
            var emailService = new EmailService(dataService);
            string testDomain = "multi.test.com";

            var emails = emailService.GenerateMultipleEmails(testDomain, 5);

            Assert.AreEqual(5, emails.Count, "应该生成5个邮箱");
            
            var emailAddresses = emails.Select(e => e.Email).ToList();
            var uniqueEmails = emailAddresses.Distinct().ToList();
            Assert.AreEqual(5, uniqueEmails.Count, "所有生成的邮箱应该是唯一的");

            foreach (var email in emails)
            {
                Assert.IsTrue(email.Email.EndsWith($"@{testDomain}"), "所有邮箱都应该使用指定的域名");
                Assert.AreEqual(EmailStatus.Active, email.Status, "所有邮箱状态都应该是Active");
            }
        }

        [TestMethod]
        public void TestEmailGenerationErrorHandling()
        {
            var dataService = new DataService();
            var emailService = new EmailService(dataService);

            var result1 = emailService.CreateEmailRecord(null);
            Assert.IsNull(result1, "null域名应该返回null");

            var result2 = emailService.CreateEmailRecord("");
            Assert.IsNull(result2, "空域名应该返回null");

            var result3 = emailService.GenerateMultipleEmails("invalid", 5);
            Assert.AreEqual(0, result3.Count, "无效域名应该返回空列表");

            var result4 = emailService.GenerateMultipleEmails("valid.com", 0);
            Assert.AreEqual(0, result4.Count, "数量为0应该返回空列表");
        }

        private void BackupOriginalFiles()
        {
            if (File.Exists(_originalConfigPath))
            {
                File.Copy(_originalConfigPath, _originalConfigPath + ".backup", true);
                File.Delete(_originalConfigPath);
            }

            if (File.Exists(_originalDataPath))
            {
                File.Copy(_originalDataPath, _originalDataPath + ".backup", true);
                File.Delete(_originalDataPath);
            }
        }

        private void RestoreOriginalFiles()
        {
            if (File.Exists(_originalConfigPath + ".backup"))
            {
                File.Move(_originalConfigPath + ".backup", _originalConfigPath);
            }

            if (File.Exists(_originalDataPath + ".backup"))
            {
                File.Move(_originalDataPath + ".backup", _originalDataPath);
            }
        }

        private void CleanupTestFiles()
        {
            string[] testFiles = { _testConfigPath, _testDataPath, _originalConfigPath, _originalDataPath };
            
            foreach (string file in testFiles)
            {
                if (File.Exists(file))
                {
                    File.Delete(file);
                }
            }
        }
    }
}