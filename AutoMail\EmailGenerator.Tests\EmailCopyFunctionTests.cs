using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using EmailGenerator.Forms;
using EmailGenerator.Services;
using EmailGenerator.Models;

namespace EmailGenerator.Tests
{
    [TestClass]
    public class EmailCopyFunctionTests
    {
        private MainForm _mainForm;
        private string _testConfigPath;
        private string _testDataPath;
        private string _originalConfigPath;
        private string _originalDataPath;

        [TestInitialize]
        public void Setup()
        {
            _originalConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.json");
            _originalDataPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "emails.json");
            _testConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "test_config.json");
            _testDataPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "test_emails.json");
            
            BackupOriginalFiles();
            _mainForm = new MainForm();
        }

        [TestCleanup]
        public void Cleanup()
        {
            CleanupTestFiles();
            RestoreOriginalFiles();
            _mainForm?.Dispose();
        }

        [TestMethod]
        public void TestCopySelectedEmailToClipboard()
        {
            var dataService = new DataService();
            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                Username = "test",
                Domain = "copy.com",
                CreatedDate = DateTime.Now,
                Status = EmailStatus.Active
            };

            dataService.SaveEmails(new List<EmailRecord> { testEmail });

            using (var form = new MainForm())
            {
                var lstEmails = GetPrivateField<ListBox>(form, "lstEmails");
                
                // 选择第一个邮箱
                lstEmails.SelectedIndex = 0;
                
                // 测试GetSelectedEmailRecord方法
                var getSelectedMethod = typeof(MainForm).GetMethod("GetSelectedEmailRecord", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                var selectedEmail = getSelectedMethod?.Invoke(form, null) as EmailRecord;
                
                Assert.IsNotNull(selectedEmail, "应该能获取到选中的邮箱记录");
                Assert.AreEqual("<EMAIL>", selectedEmail.Email, "选中的邮箱应该是正确的");
                
                // 验证邮箱记录的其他属性
                Assert.AreEqual("test", selectedEmail.Username, "用户名应该正确");
                Assert.AreEqual("copy.com", selectedEmail.Domain, "域名应该正确");
                Assert.AreEqual(EmailStatus.Active, selectedEmail.Status, "状态应该是Active");
            }
        }

        [TestMethod]
        public void TestCopyWithNoEmailSelected()
        {
            var dataService = new DataService();
            dataService.SaveEmails(new List<EmailRecord>());

            using (var form = new MainForm())
            {
                var lstEmails = GetPrivateField<ListBox>(form, "lstEmails");
                
                // 确保没有选择任何项
                lstEmails.SelectedIndex = -1;
                
                // 测试GetSelectedEmailRecord方法
                var getSelectedMethod = typeof(MainForm).GetMethod("GetSelectedEmailRecord", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                var selectedEmail = getSelectedMethod?.Invoke(form, null) as EmailRecord;
                
                Assert.IsNull(selectedEmail, "没有选择时应该返回null");
            }
        }

        [TestMethod]
        public void TestCopyWithMultipleEmails()
        {
            var dataService = new DataService();
            var testEmails = new List<EmailRecord>
            {
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Username = "first",
                    Domain = "test.com",
                    CreatedDate = DateTime.Now,
                    Status = EmailStatus.Active
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Username = "second",
                    Domain = "test.com",
                    CreatedDate = DateTime.Now.AddMinutes(-1),
                    Status = EmailStatus.Deactivated
                }
            };

            dataService.SaveEmails(testEmails);

            using (var form = new MainForm())
            {
                var lstEmails = GetPrivateField<ListBox>(form, "lstEmails");
                var getSelectedMethod = typeof(MainForm).GetMethod("GetSelectedEmailRecord", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                // 测试选择第一个邮箱（按时间倒序，应该是**************）
                lstEmails.SelectedIndex = 0;
                var selectedEmail1 = getSelectedMethod?.Invoke(form, null) as EmailRecord;
                
                Assert.IsNotNull(selectedEmail1, "应该能获取到第一个邮箱");
                Assert.AreEqual("<EMAIL>", selectedEmail1.Email, "第一个邮箱应该是最新的");
                
                // 测试选择第二个邮箱
                lstEmails.SelectedIndex = 1;
                var selectedEmail2 = getSelectedMethod?.Invoke(form, null) as EmailRecord;
                
                Assert.IsNotNull(selectedEmail2, "应该能获取到第二个邮箱");
                Assert.AreEqual("<EMAIL>", selectedEmail2.Email, "第二个邮箱应该是较早的");
            }
        }

        [TestMethod]
        public void TestCopyActiveEmail()
        {
            var dataService = new DataService();
            var activeEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                Username = "active",
                Domain = "test.com",
                CreatedDate = DateTime.Now,
                Status = EmailStatus.Active
            };

            dataService.SaveEmails(new List<EmailRecord> { activeEmail });

            using (var form = new MainForm())
            {
                var lstEmails = GetPrivateField<ListBox>(form, "lstEmails");
                var getSelectedMethod = typeof(MainForm).GetMethod("GetSelectedEmailRecord", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                lstEmails.SelectedIndex = 0;
                var selectedEmail = getSelectedMethod?.Invoke(form, null) as EmailRecord;
                
                Assert.IsNotNull(selectedEmail, "应该能获取到有效邮箱");
                Assert.AreEqual(EmailStatus.Active, selectedEmail.Status, "邮箱状态应该是Active");
                Assert.AreEqual("<EMAIL>", selectedEmail.Email, "邮箱地址应该正确");
                
                // 验证有效邮箱的属性
                Assert.IsNull(selectedEmail.DeactivatedDate, "有效邮箱的作废时间应该为null");
                Assert.AreEqual("active", selectedEmail.Username, "用户名应该正确");
            }
        }

        [TestMethod]
        public void TestCopyDeactivatedEmail()
        {
            var dataService = new DataService();
            var deactivatedEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                Username = "deactivated",
                Domain = "test.com",
                CreatedDate = DateTime.Now,
                Status = EmailStatus.Deactivated,
                DeactivatedDate = DateTime.Now.AddMinutes(-10)
            };

            dataService.SaveEmails(new List<EmailRecord> { deactivatedEmail });

            using (var form = new MainForm())
            {
                var lstEmails = GetPrivateField<ListBox>(form, "lstEmails");
                var getSelectedMethod = typeof(MainForm).GetMethod("GetSelectedEmailRecord", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                lstEmails.SelectedIndex = 0;
                var selectedEmail = getSelectedMethod?.Invoke(form, null) as EmailRecord;
                
                Assert.IsNotNull(selectedEmail, "应该能获取到已作废邮箱");
                Assert.AreEqual(EmailStatus.Deactivated, selectedEmail.Status, "邮箱状态应该是Deactivated");
                Assert.AreEqual("<EMAIL>", selectedEmail.Email, "邮箱地址应该正确");
                
                // 验证已作废邮箱的属性
                Assert.IsNotNull(selectedEmail.DeactivatedDate, "已作废邮箱应该有作废时间");
                Assert.AreEqual("deactivated", selectedEmail.Username, "用户名应该正确");
            }
        }

        [TestMethod]
        public void TestCopyButtonEnabledState()
        {
            var dataService = new DataService();
            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                Username = "enabled",
                Domain = "test.com",
                CreatedDate = DateTime.Now,
                Status = EmailStatus.Active
            };

            dataService.SaveEmails(new List<EmailRecord> { testEmail });

            using (var form = new MainForm())
            {
                var lstEmails = GetPrivateField<ListBox>(form, "lstEmails");
                var btnCopy = GetPrivateField<Button>(form, "btnCopy");
                
                // 初始状态：没有选择，复制按钮应该被禁用
                Assert.IsFalse(btnCopy.Enabled, "复制按钮初始应被禁用");
                
                // 选择邮箱后，复制按钮应该启用
                lstEmails.SelectedIndex = 0;
                
                // 手动触发选择变化事件
                var selectionMethod = typeof(MainForm).GetMethod("LstEmails_SelectedIndexChanged", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                selectionMethod?.Invoke(form, new object[] { lstEmails, EventArgs.Empty });
                
                Assert.IsTrue(btnCopy.Enabled, "选择邮箱后复制按钮应启用");
            }
        }

        [TestMethod]
        public void TestCopyFunctionLogic()
        {
            var dataService = new DataService();
            var testEmails = new List<EmailRecord>
            {
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Username = "logic1",
                    Domain = "test.com",
                    CreatedDate = DateTime.Now,
                    Status = EmailStatus.Active
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Username = "logic2",
                    Domain = "test.com",
                    CreatedDate = DateTime.Now.AddMinutes(-1),
                    Status = EmailStatus.Deactivated
                }
            };

            dataService.SaveEmails(testEmails);

            using (var form = new MainForm())
            {
                var lstEmails = GetPrivateField<ListBox>(form, "lstEmails");
                var getSelectedMethod = typeof(MainForm).GetMethod("GetSelectedEmailRecord", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                // 测试复制逻辑：选择不同的邮箱应该返回不同的结果
                lstEmails.SelectedIndex = 0;
                var selectedEmail1 = getSelectedMethod?.Invoke(form, null) as EmailRecord;
                Assert.AreEqual("<EMAIL>", selectedEmail1?.Email, "第一个邮箱应该正确");
                
                lstEmails.SelectedIndex = 1;
                var selectedEmail2 = getSelectedMethod?.Invoke(form, null) as EmailRecord;
                Assert.AreEqual("<EMAIL>", selectedEmail2?.Email, "第二个邮箱应该正确");
                
                // 验证复制功能可以处理不同状态的邮箱
                Assert.AreEqual(EmailStatus.Active, selectedEmail1?.Status, "第一个邮箱状态应该是Active");
                Assert.AreEqual(EmailStatus.Deactivated, selectedEmail2?.Status, "第二个邮箱状态应该是Deactivated");
            }
        }

        private void BackupOriginalFiles()
        {
            if (File.Exists(_originalConfigPath))
            {
                File.Copy(_originalConfigPath, _originalConfigPath + ".backup", true);
                File.Delete(_originalConfigPath);
            }

            if (File.Exists(_originalDataPath))
            {
                File.Copy(_originalDataPath, _originalDataPath + ".backup", true);
                File.Delete(_originalDataPath);
            }
        }

        private void RestoreOriginalFiles()
        {
            if (File.Exists(_originalConfigPath + ".backup"))
            {
                File.Move(_originalConfigPath + ".backup", _originalConfigPath);
            }

            if (File.Exists(_originalDataPath + ".backup"))
            {
                File.Move(_originalDataPath + ".backup", _originalDataPath);
            }
        }

        private void CleanupTestFiles()
        {
            string[] testFiles = { _testConfigPath, _testDataPath, _originalConfigPath, _originalDataPath };
            
            foreach (string file in testFiles)
            {
                if (File.Exists(file))
                {
                    File.Delete(file);
                }
            }
        }

        private T GetPrivateField<T>(object obj, string fieldName)
        {
            var field = obj.GetType().GetField(fieldName, 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (field == null)
            {
                throw new ArgumentException($"Field '{fieldName}' not found");
            }

            return (T)field.GetValue(obj);
        }
    }
}