namespace EmailGenerator.Models
{
    /// <summary>
    /// 成功消息常量类
    /// </summary>
    public static class SuccessMessages
    {
        // 域名相关成功消息
        public const string DOMAIN_SAVED = "域名保存成功！";
        public const string DOMAIN_LOADED = "域名配置加载成功";

        // 邮箱生成相关成功消息
        public const string EMAIL_GENERATED = "邮箱生成成功！";
        public const string EMAIL_SAVED = "邮箱保存成功";

        // 邮箱操作相关成功消息
        public const string EMAIL_COPIED = "邮箱地址已复制到剪贴板！";
        public const string EMAIL_DELETED = "邮箱已成功删除";
        public const string EMAIL_DEACTIVATED = "邮箱已成功作废";

        // 数据操作相关成功消息
        public const string DATA_LOADED = "数据加载成功";
        public const string DATA_SAVED = "数据保存成功";

        // 配置相关成功消息
        public const string CONFIG_LOADED = "配置加载成功";
        public const string CONFIG_SAVED = "配置保存成功";

        // 系统相关成功消息
        public const string OPERATION_COMPLETED = "操作完成";
        public const string SYSTEM_READY = "系统就绪";
    }
}