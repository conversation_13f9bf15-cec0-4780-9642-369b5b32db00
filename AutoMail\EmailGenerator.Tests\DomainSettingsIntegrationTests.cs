using System;
using System.IO;
using System.Windows.Forms;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using EmailGenerator.Forms;
using EmailGenerator.Services;

namespace EmailGenerator.Tests
{
    [TestClass]
    public class DomainSettingsIntegrationTests
    {
        private MainForm _mainForm;
        private string _testConfigPath;
        private string _originalConfigPath;

        [TestInitialize]
        public void Setup()
        {
            _originalConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.json");
            _testConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "test_config.json");
            
            if (File.Exists(_originalConfigPath))
            {
                File.Copy(_originalConfigPath, _originalConfigPath + ".backup", true);
                File.Delete(_originalConfigPath);
            }

            _mainForm = new MainForm();
        }

        [TestCleanup]
        public void Cleanup()
        {
            if (File.Exists(_testConfigPath))
            {
                File.Delete(_testConfigPath);
            }

            if (File.Exists(_originalConfigPath))
            {
                File.Delete(_originalConfigPath);
            }

            if (File.Exists(_originalConfigPath + ".backup"))
            {
                File.Move(_originalConfigPath + ".backup", _originalConfigPath);
            }

            _mainForm?.Dispose();
        }

        [TestMethod]
        public void TestDomainLoadingOnFormInitialization()
        {
            var configService = new ConfigurationService();
            configService.SaveDomain("test.example.com");

            using (var form = new MainForm())
            {
                var txtDomain = GetPrivateField<TextBox>(form, "txtDomain");
                Assert.IsNotNull(txtDomain);
                Assert.AreEqual("test.example.com", txtDomain.Text);
            }
        }

        [TestMethod]
        public void TestDomainSavingWithValidDomain()
        {
            // 直接测试ConfigurationService的保存功能
            var configService = new ConfigurationService();
            string testDomain = "valid.domain.com";
            
            bool saveResult = configService.SaveDomain(testDomain);
            Assert.IsTrue(saveResult, "域名保存应该成功");

            string savedDomain = configService.LoadDomain();
            Assert.AreEqual(testDomain, savedDomain, "保存的域名应该与输入的域名一致");
        }

        [TestMethod]
        public void TestDomainValidationWithInvalidDomain()
        {
            string[] invalidDomains = { "", "   ", "invalid", ".invalid.com", "invalid.com.", "invalid..com" };

            foreach (string invalidDomain in invalidDomains)
            {
                var configService = new ConfigurationService();
                bool saveResult = configService.SaveDomain(invalidDomain);
                Assert.IsFalse(saveResult, $"Invalid domain '{invalidDomain}' should not save successfully");
            }
        }

        [TestMethod]
        public void TestDomainValidationWithValidDomains()
        {
            string[] validDomains = { "example.com", "test.example.com", "sub.domain.example.org", "my-domain.com" };
            var configService = new ConfigurationService();

            foreach (string validDomain in validDomains)
            {
                bool saveResult = configService.SaveDomain(validDomain);
                Assert.IsTrue(saveResult, $"Valid domain '{validDomain}' should save successfully");
                
                string loadedDomain = configService.LoadDomain();
                Assert.AreEqual(validDomain.ToLower(), loadedDomain);
            }
        }

        [TestMethod]
        public void TestDomainPersistenceAcrossApplicationRestart()
        {
            var configService = new ConfigurationService();
            string testDomain = "persistence.test.com";

            bool saveResult = configService.SaveDomain(testDomain);
            Assert.IsTrue(saveResult);

            var newConfigService = new ConfigurationService();
            string loadedDomain = newConfigService.LoadDomain();

            Assert.AreEqual(testDomain, loadedDomain);
        }

        [TestMethod]
        public void TestDefaultDomainWhenNoConfigurationExists()
        {
            string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.json");
            if (File.Exists(configPath))
            {
                File.Delete(configPath);
            }

            var configService = new ConfigurationService();
            string loadedDomain = configService.LoadDomain();

            Assert.AreEqual("example.com", loadedDomain);
        }

        [TestMethod]
        public void TestConfigurationFileCreation()
        {
            string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.json");
            if (File.Exists(configPath))
            {
                File.Delete(configPath);
            }

            var configService = new ConfigurationService();
            string testDomain = "filetest.example.com";

            bool saveResult = configService.SaveDomain(testDomain);

            Assert.IsTrue(saveResult);
            Assert.IsTrue(File.Exists(configPath));

            string fileContent = File.ReadAllText(configPath);
            Assert.IsTrue(fileContent.Contains(testDomain));
        }

        private T GetPrivateField<T>(object obj, string fieldName)
        {
            var field = obj.GetType().GetField(fieldName, 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (field == null)
            {
                throw new ArgumentException($"Field '{fieldName}' not found");
            }

            return (T)field.GetValue(obj);
        }
    }
}