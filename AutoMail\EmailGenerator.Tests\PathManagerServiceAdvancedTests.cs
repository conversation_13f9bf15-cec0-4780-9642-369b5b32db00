using System;
using System.IO;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using EmailGenerator.Services;
using EmailGenerator.Models;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// PathManagerService高级功能测试类
    /// </summary>
    [TestClass]
    public class PathManagerServiceAdvancedTests
    {
        private PathManagerService _pathManager;
        private string _testDirectory;
        private string _tempFile;

        [TestInitialize]
        public void Setup()
        {
            _pathManager = new PathManagerService();
            
            // 创建测试目录
            _testDirectory = Path.Combine(Path.GetTempPath(), "EmailGeneratorAdvancedTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);
            
            _tempFile = Path.Combine(_testDirectory, "test.txt");
        }

        [TestCleanup]
        public void Cleanup()
        {
            try
            {
                if (File.Exists(_tempFile))
                    File.Delete(_tempFile);
                
                if (Directory.Exists(_testDirectory))
                    Directory.Delete(_testDirectory, true);
            }
            catch
            {
                // 清理失败不影响测试结果
            }
        }

        #region SafeExecutePathOperationWithDetails 测试

        [TestMethod]
        public void SafeExecutePathOperationWithDetails_SuccessfulOperation_ReturnsSuccessResult()
        {
            // Arrange
            string validPath = _testDirectory;
            Func<string, bool> operation = path => Directory.Exists(path);

            // Act
            var result = _pathManager.SafeExecutePathOperationWithDetails(operation, validPath, false);

            // Assert
            Assert.IsTrue(result.Success);
            Assert.IsTrue(result.Value);
            Assert.AreEqual(validPath, result.Path);
            Assert.AreEqual(string.Empty, result.ErrorMessage);
        }

        [TestMethod]
        public void SafeExecutePathOperationWithDetails_FailedOperation_ReturnsFailureResult()
        {
            // Arrange
            string invalidPath = "C:\\InvalidPath<>|";
            Func<string, bool> operation = path => Directory.Exists(path);

            // Act
            var result = _pathManager.SafeExecutePathOperationWithDetails(operation, invalidPath, false);

            // Assert
            Assert.IsFalse(result.Success);
            Assert.IsFalse(result.Value);
            Assert.AreEqual(invalidPath, result.Path);
            Assert.IsNotNull(result.ErrorMessage);
            Assert.IsTrue(result.ErrorMessage.Length > 0);
        }

        [TestMethod]
        public void SafeExecutePathOperationWithDetails_ExceptionInOperation_ReturnsFailureResult()
        {
            // Arrange
            string path = _testDirectory;
            Func<string, bool> operation = p => throw new UnauthorizedAccessException("Access denied");

            // Act
            var result = _pathManager.SafeExecutePathOperationWithDetails(operation, path, false);

            // Assert
            Assert.IsFalse(result.Success);
            Assert.IsFalse(result.Value);
            Assert.AreEqual(path, result.Path);
            Assert.AreEqual(ErrorMessages.PATH_ACCESS_DENIED, result.ErrorMessage);
        }

        #endregion

        #region SafeDeleteFile 测试

        [TestMethod]
        public void SafeDeleteFile_ExistingFile_ReturnsSuccessResult()
        {
            // Arrange
            File.WriteAllText(_tempFile, "test content");
            Assert.IsTrue(File.Exists(_tempFile));

            // Act
            var result = _pathManager.SafeDeleteFile(_tempFile);

            // Assert
            Assert.IsTrue(result.Success);
            Assert.AreEqual(_tempFile, result.Path);
            Assert.AreEqual(string.Empty, result.ErrorMessage);
            Assert.IsFalse(File.Exists(_tempFile));
        }

        [TestMethod]
        public void SafeDeleteFile_NonExistentFile_ReturnsSuccessResult()
        {
            // Arrange
            string nonExistentFile = Path.Combine(_testDirectory, "nonexistent.txt");
            Assert.IsFalse(File.Exists(nonExistentFile));

            // Act
            var result = _pathManager.SafeDeleteFile(nonExistentFile);

            // Assert
            Assert.IsTrue(result.Success);
            Assert.AreEqual(nonExistentFile, result.Path);
            Assert.AreEqual(string.Empty, result.ErrorMessage);
        }

        [TestMethod]
        public void SafeDeleteFile_EmptyPath_ReturnsFailureResult()
        {
            // Arrange
            string emptyPath = "";

            // Act
            var result = _pathManager.SafeDeleteFile(emptyPath);

            // Assert
            Assert.IsFalse(result.Success);
            Assert.AreEqual(emptyPath, result.Path);
            Assert.AreEqual(ErrorMessages.PATH_EMPTY, result.ErrorMessage);
        }

        [TestMethod]
        public void SafeDeleteFile_NullPath_ReturnsFailureResult()
        {
            // Arrange
            string nullPath = null;

            // Act
            var result = _pathManager.SafeDeleteFile(nullPath);

            // Assert
            Assert.IsFalse(result.Success);
            Assert.AreEqual(nullPath, result.Path);
            Assert.AreEqual(ErrorMessages.PATH_EMPTY, result.ErrorMessage);
        }

        #endregion

        #region SafeCopyFile 测试

        [TestMethod]
        public void SafeCopyFile_ValidSourceAndDestination_ReturnsSuccessResult()
        {
            // Arrange
            string sourceFile = _tempFile;
            string destinationFile = Path.Combine(_testDirectory, "copy.txt");
            File.WriteAllText(sourceFile, "test content");

            // Act
            var result = _pathManager.SafeCopyFile(sourceFile, destinationFile);

            // Assert
            Assert.IsTrue(result.Success);
            Assert.AreEqual(destinationFile, result.Path);
            Assert.AreEqual(string.Empty, result.ErrorMessage);
            Assert.IsTrue(File.Exists(destinationFile));
            Assert.AreEqual("test content", File.ReadAllText(destinationFile));
        }

        [TestMethod]
        public void SafeCopyFile_NonExistentSource_ReturnsFailureResult()
        {
            // Arrange
            string sourceFile = Path.Combine(_testDirectory, "nonexistent.txt");
            string destinationFile = Path.Combine(_testDirectory, "copy.txt");

            // Act
            var result = _pathManager.SafeCopyFile(sourceFile, destinationFile);

            // Assert
            Assert.IsFalse(result.Success);
            Assert.AreEqual(sourceFile, result.Path);
            Assert.AreEqual(ErrorMessages.FILE_READ_ERROR, result.ErrorMessage);
        }

        [TestMethod]
        public void SafeCopyFile_EmptySourcePath_ReturnsFailureResult()
        {
            // Arrange
            string sourceFile = "";
            string destinationFile = Path.Combine(_testDirectory, "copy.txt");

            // Act
            var result = _pathManager.SafeCopyFile(sourceFile, destinationFile);

            // Assert
            Assert.IsFalse(result.Success);
            Assert.AreEqual(sourceFile, result.Path);
            Assert.AreEqual(ErrorMessages.PATH_EMPTY, result.ErrorMessage);
        }

        [TestMethod]
        public void SafeCopyFile_EmptyDestinationPath_ReturnsFailureResult()
        {
            // Arrange
            string sourceFile = _tempFile;
            string destinationFile = "";
            File.WriteAllText(sourceFile, "test content");

            // Act
            var result = _pathManager.SafeCopyFile(sourceFile, destinationFile);

            // Assert
            Assert.IsFalse(result.Success);
            Assert.AreEqual(sourceFile, result.Path);
            Assert.AreEqual(ErrorMessages.PATH_EMPTY, result.ErrorMessage);
        }

        [TestMethod]
        public void SafeCopyFile_DestinationDirectoryNotExist_CreatesDirectoryAndCopies()
        {
            // Arrange
            string sourceFile = _tempFile;
            string newDirectory = Path.Combine(_testDirectory, "newdir");
            string destinationFile = Path.Combine(newDirectory, "copy.txt");
            File.WriteAllText(sourceFile, "test content");
            Assert.IsFalse(Directory.Exists(newDirectory));

            // Act
            var result = _pathManager.SafeCopyFile(sourceFile, destinationFile);

            // Assert
            Assert.IsTrue(result.Success);
            Assert.AreEqual(destinationFile, result.Path);
            Assert.AreEqual(string.Empty, result.ErrorMessage);
            Assert.IsTrue(Directory.Exists(newDirectory));
            Assert.IsTrue(File.Exists(destinationFile));
            Assert.AreEqual("test content", File.ReadAllText(destinationFile));
        }

        #endregion

        #region SafeMoveFile 测试

        [TestMethod]
        public void SafeMoveFile_ValidSourceAndDestination_ReturnsSuccessResult()
        {
            // Arrange
            string sourceFile = _tempFile;
            string destinationFile = Path.Combine(_testDirectory, "moved.txt");
            File.WriteAllText(sourceFile, "test content");

            // Act
            var result = _pathManager.SafeMoveFile(sourceFile, destinationFile);

            // Assert
            Assert.IsTrue(result.Success);
            Assert.AreEqual(destinationFile, result.Path);
            Assert.AreEqual(string.Empty, result.ErrorMessage);
            Assert.IsFalse(File.Exists(sourceFile));
            Assert.IsTrue(File.Exists(destinationFile));
            Assert.AreEqual("test content", File.ReadAllText(destinationFile));
        }

        [TestMethod]
        public void SafeMoveFile_NonExistentSource_ReturnsFailureResult()
        {
            // Arrange
            string sourceFile = Path.Combine(_testDirectory, "nonexistent.txt");
            string destinationFile = Path.Combine(_testDirectory, "moved.txt");

            // Act
            var result = _pathManager.SafeMoveFile(sourceFile, destinationFile);

            // Assert
            Assert.IsFalse(result.Success);
            Assert.AreEqual(sourceFile, result.Path);
            Assert.AreEqual(ErrorMessages.FILE_READ_ERROR, result.ErrorMessage);
        }

        [TestMethod]
        public void SafeMoveFile_DestinationDirectoryNotExist_CreatesDirectoryAndMoves()
        {
            // Arrange
            string sourceFile = _tempFile;
            string newDirectory = Path.Combine(_testDirectory, "newdir");
            string destinationFile = Path.Combine(newDirectory, "moved.txt");
            File.WriteAllText(sourceFile, "test content");
            Assert.IsFalse(Directory.Exists(newDirectory));

            // Act
            var result = _pathManager.SafeMoveFile(sourceFile, destinationFile);

            // Assert
            Assert.IsTrue(result.Success);
            Assert.AreEqual(destinationFile, result.Path);
            Assert.AreEqual(string.Empty, result.ErrorMessage);
            Assert.IsTrue(Directory.Exists(newDirectory));
            Assert.IsFalse(File.Exists(sourceFile));
            Assert.IsTrue(File.Exists(destinationFile));
            Assert.AreEqual("test content", File.ReadAllText(destinationFile));
        }

        #endregion

        #region GetDiskSpaceInfo 测试

        [TestMethod]
        public void GetDiskSpaceInfo_ValidPath_ReturnsValidInfo()
        {
            // Arrange
            string validPath = _testDirectory;

            // Act
            var result = _pathManager.GetDiskSpaceInfo(validPath);

            // Assert
            Assert.IsTrue(result.IsValid);
            Assert.AreEqual(validPath, result.Path);
            Assert.IsTrue(result.TotalBytes > 0);
            Assert.IsTrue(result.FreeBytes >= 0);
            Assert.IsTrue(result.UsedBytes >= 0);
            Assert.AreEqual(result.TotalBytes, result.FreeBytes + result.UsedBytes);
        }

        [TestMethod]
        public void GetDiskSpaceInfo_EmptyPath_ReturnsInvalidInfo()
        {
            // Arrange
            string emptyPath = "";

            // Act
            var result = _pathManager.GetDiskSpaceInfo(emptyPath);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual(emptyPath, result.Path);
            Assert.AreEqual(0, result.TotalBytes);
            Assert.AreEqual(0, result.FreeBytes);
            Assert.AreEqual(0, result.UsedBytes);
        }

        [TestMethod]
        public void GetDiskSpaceInfo_NullPath_ReturnsInvalidInfo()
        {
            // Arrange
            string nullPath = null;

            // Act
            var result = _pathManager.GetDiskSpaceInfo(nullPath);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual(nullPath, result.Path);
            Assert.AreEqual(0, result.TotalBytes);
            Assert.AreEqual(0, result.FreeBytes);
            Assert.AreEqual(0, result.UsedBytes);
        }

        [TestMethod]
        public void GetDiskSpaceInfo_ValidPath_CalculatesPercentagesCorrectly()
        {
            // Arrange
            string validPath = _testDirectory;

            // Act
            var result = _pathManager.GetDiskSpaceInfo(validPath);

            // Assert
            if (result.IsValid && result.TotalBytes > 0)
            {
                double expectedFreePercentage = (double)result.FreeBytes / result.TotalBytes * 100;
                double expectedUsedPercentage = (double)result.UsedBytes / result.TotalBytes * 100;
                
                Assert.AreEqual(expectedFreePercentage, result.FreeSpacePercentage, 0.01);
                Assert.AreEqual(expectedUsedPercentage, result.UsedSpacePercentage, 0.01);
                Assert.AreEqual(100.0, result.FreeSpacePercentage + result.UsedSpacePercentage, 0.01);
            }
        }

        #endregion

        #region HasSufficientDiskSpace 测试

        [TestMethod]
        public void HasSufficientDiskSpace_SmallRequirement_ReturnsTrue()
        {
            // Arrange
            string validPath = _testDirectory;
            long smallRequirement = 1024; // 1KB

            // Act
            bool result = _pathManager.HasSufficientDiskSpace(validPath, smallRequirement);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void HasSufficientDiskSpace_ZeroRequirement_ReturnsTrue()
        {
            // Arrange
            string validPath = _testDirectory;
            long zeroRequirement = 0;

            // Act
            bool result = _pathManager.HasSufficientDiskSpace(validPath, zeroRequirement);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void HasSufficientDiskSpace_InvalidPath_ReturnsFalse()
        {
            // Arrange
            string invalidPath = "";
            long requirement = 1024;

            // Act
            bool result = _pathManager.HasSufficientDiskSpace(invalidPath, requirement);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void HasSufficientDiskSpace_ExtremelyLargeRequirement_ReturnsFalse()
        {
            // Arrange
            string validPath = _testDirectory;
            long extremeRequirement = long.MaxValue;

            // Act
            bool result = _pathManager.HasSufficientDiskSpace(validPath, extremeRequirement);

            // Assert
            Assert.IsFalse(result);
        }

        #endregion

        #region CreateDirectoryIfNotExistsWithDetails 测试

        [TestMethod]
        public void CreateDirectoryIfNotExistsWithDetails_ExistingDirectory_ReturnsSuccessResult()
        {
            // Arrange
            string existingDirectory = _testDirectory;
            Assert.IsTrue(Directory.Exists(existingDirectory));

            // Act
            var result = _pathManager.CreateDirectoryIfNotExistsWithDetails(existingDirectory);

            // Assert
            Assert.IsTrue(result.Success);
            Assert.AreEqual(existingDirectory, result.Path);
            Assert.AreEqual(string.Empty, result.ErrorMessage);
        }

        [TestMethod]
        public void CreateDirectoryIfNotExistsWithDetails_NewDirectory_CreatesAndReturnsSuccess()
        {
            // Arrange
            string newDirectory = Path.Combine(_testDirectory, "newsubdir");
            Assert.IsFalse(Directory.Exists(newDirectory));

            // Act
            var result = _pathManager.CreateDirectoryIfNotExistsWithDetails(newDirectory);

            // Assert
            Assert.IsTrue(result.Success);
            Assert.AreEqual(newDirectory, result.Path);
            Assert.AreEqual(string.Empty, result.ErrorMessage);
            Assert.IsTrue(Directory.Exists(newDirectory));
        }

        [TestMethod]
        public void CreateDirectoryIfNotExistsWithDetails_EmptyPath_ReturnsFailureResult()
        {
            // Arrange
            string emptyPath = "";

            // Act
            var result = _pathManager.CreateDirectoryIfNotExistsWithDetails(emptyPath);

            // Assert
            Assert.IsFalse(result.Success);
            Assert.AreEqual(emptyPath, result.Path);
            Assert.AreEqual(ErrorMessages.PATH_EMPTY, result.ErrorMessage);
        }

        [TestMethod]
        public void CreateDirectoryIfNotExistsWithDetails_FilePathInput_ExtractsDirectoryAndCreates()
        {
            // Arrange
            string newDirectory = Path.Combine(_testDirectory, "newsubdir");
            string filePath = Path.Combine(newDirectory, "test.txt");
            Assert.IsFalse(Directory.Exists(newDirectory));

            // Act
            var result = _pathManager.CreateDirectoryIfNotExistsWithDetails(filePath);

            // Assert
            Assert.IsTrue(result.Success);
            Assert.AreEqual(newDirectory, result.Path);
            Assert.AreEqual(string.Empty, result.ErrorMessage);
            Assert.IsTrue(Directory.Exists(newDirectory));
        }

        #endregion

        #region ValidatePathWithDetails 测试

        [TestMethod]
        public void ValidatePathWithDetails_ValidPath_ReturnsValidResult()
        {
            // Arrange
            string validPath = _testDirectory;

            // Act
            var result = _pathManager.ValidatePathWithDetails(validPath);

            // Assert
            Assert.IsTrue(result.IsValid);
            Assert.AreEqual(validPath, result.Path);
            Assert.AreEqual(string.Empty, result.ErrorMessage);
            Assert.AreEqual(string.Empty, result.Guidance);
        }

        [TestMethod]
        public void ValidatePathWithDetails_InvalidPath_ReturnsInvalidResult()
        {
            // Arrange
            string invalidPath = "C:\\test<>path";

            // Act
            var result = _pathManager.ValidatePathWithDetails(invalidPath);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual(invalidPath, result.Path);
            Assert.IsNotNull(result.ErrorMessage);
            Assert.IsTrue(result.ErrorMessage.Length > 0);
            Assert.IsNotNull(result.Guidance);
        }

        #endregion
    }
}