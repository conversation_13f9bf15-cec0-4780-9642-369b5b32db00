using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using EmailGenerator.Models;
using EmailGenerator.Helpers;

namespace EmailGenerator.Services
{
    /// <summary>
    /// 邮箱服务类，负责邮箱生成和验证逻辑
    /// </summary>
    public class EmailService
    {
        private readonly Random _random;
        private readonly DataService _dataService;

        // 用于生成用户名的字符集
        private const string LOWERCASE_CHARS = "abcdefghijklmnopqrstuvwxyz";
        private const string NUMBERS = "0123456789";
        private const string ALL_CHARS = LOWERCASE_CHARS + NUMBERS;

        public EmailService(DataService dataService)
        {
            _random = new Random();
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
        }

        /// <summary>
        /// 生成唯一的邮箱地址
        /// </summary>
        /// <param name="domain">邮箱域名</param>
        /// <param name="existingEmails">已存在的邮箱列表（可选，如果不提供则从数据服务获取）</param>
        /// <returns>生成的唯一邮箱地址，如果生成失败则返回null</returns>
        /// <exception cref="ArgumentException">当域名无效时抛出</exception>
        /// <exception cref="InvalidOperationException">当生成失败时抛出</exception>
        public string GenerateUniqueEmail(string domain, List<string> existingEmails = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(domain))
                {
                    throw new ArgumentException(ErrorMessages.DOMAIN_EMPTY);
                }

                if (!IsValidDomain(domain))
                {
                    throw new ArgumentException(ErrorMessages.DOMAIN_INVALID_FORMAT);
                }

                // 如果没有提供现有邮箱列表，从数据服务获取
                if (existingEmails == null)
                {
                    try
                    {
                        var allEmails = _dataService.LoadEmails();
                        existingEmails = allEmails.Select(e => e.Email).ToList();
                    }
                    catch (Exception ex)
                    {
                        throw new InvalidOperationException($"{ErrorMessages.DATA_LOAD_FAILED}: {ex.Message}");
                    }
                }

                // 最多尝试100次生成唯一邮箱
                for (int attempt = 0; attempt < 100; attempt++)
                {
                    string username = GenerateRandomUsername();
                    string email = $"{username}@{domain.ToLower()}";

                    if (IsEmailUnique(email, existingEmails))
                    {
                        return email;
                    }
                }

                // 如果100次都没有生成成功，使用GUID确保唯一性
                string guidUsername = GenerateGuidBasedUsername();
                return $"{guidUsername}@{domain.ToLower()}";
            }
            catch (Exception ex) when (!(ex is ArgumentException || ex is InvalidOperationException))
            {
                System.Diagnostics.Debug.WriteLine($"生成邮箱失败: {ex.Message}");
                throw new InvalidOperationException($"{ErrorMessages.EMAIL_GENERATION_FAILED}: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证域名格式是否有效
        /// </summary>
        /// <param name="domain">要验证的域名</param>
        /// <returns>域名是否有效</returns>
        public bool IsValidDomain(string domain)
        {
            if (string.IsNullOrWhiteSpace(domain))
                return false;

            domain = domain.Trim();

            // 域名不能以点开头或结尾
            if (domain.StartsWith(".") || domain.EndsWith("."))
                return false;

            // 域名必须包含至少一个点
            if (!domain.Contains("."))
                return false;

            // 域名长度检查
            if (domain.Length < 3 || domain.Length > 253)
                return false;

            // 检查域名各部分
            string[] parts = domain.Split('.');
            foreach (string part in parts)
            {
                if (string.IsNullOrEmpty(part) || part.Length > 63)
                    return false;

                // 每部分不能以连字符开头或结尾
                if (part.StartsWith("-") || part.EndsWith("-"))
                    return false;

                // 检查字符是否合法
                foreach (char c in part)
                {
                    if (!char.IsLetterOrDigit(c) && c != '-')
                        return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 生成随机用户名
        /// </summary>
        /// <returns>随机生成的用户名</returns>
        private string GenerateRandomUsername()
        {
            // 生成6-12位的随机用户名
            int length = _random.Next(6, 13);
            StringBuilder username = new StringBuilder(length);

            // 第一个字符必须是字母
            username.Append(LOWERCASE_CHARS[_random.Next(LOWERCASE_CHARS.Length)]);

            // 其余字符可以是字母或数字
            for (int i = 1; i < length; i++)
            {
                username.Append(ALL_CHARS[_random.Next(ALL_CHARS.Length)]);
            }

            return username.ToString();
        }

        /// <summary>
        /// 基于GUID生成用户名（确保唯一性）
        /// </summary>
        /// <returns>基于GUID的用户名</returns>
        private string GenerateGuidBasedUsername()
        {
            string guid = Guid.NewGuid().ToString("N"); // 32位十六进制字符串
            
            // 取前8位并确保以字母开头
            string username = guid.Substring(0, 8);
            
            // 如果第一个字符是数字，替换为字母
            if (char.IsDigit(username[0]))
            {
                char[] chars = username.ToCharArray();
                chars[0] = LOWERCASE_CHARS[username[0] - '0' % LOWERCASE_CHARS.Length];
                username = new string(chars);
            }

            return username;
        }

        /// <summary>
        /// 检查邮箱是否唯一
        /// </summary>
        /// <param name="email">要检查的邮箱地址</param>
        /// <param name="existingEmails">已存在的邮箱列表</param>
        /// <returns>邮箱是否唯一</returns>
        private bool IsEmailUnique(string email, List<string> existingEmails)
        {
            if (existingEmails == null || existingEmails.Count == 0)
                return true;

            return !existingEmails.Any(e => e.Equals(email, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 创建完整的邮箱记录
        /// </summary>
        /// <param name="domain">域名</param>
        /// <returns>创建的邮箱记录，如果创建失败则返回null</returns>
        /// <exception cref="ArgumentException">当域名无效时抛出</exception>
        /// <exception cref="InvalidOperationException">当创建失败时抛出</exception>
        public EmailRecord CreateEmailRecord(string domain)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(domain))
                {
                    throw new ArgumentException(ErrorMessages.DOMAIN_EMPTY);
                }

                string email = GenerateUniqueEmail(domain);
                if (string.IsNullOrEmpty(email))
                {
                    throw new InvalidOperationException(ErrorMessages.EMAIL_GENERATION_FAILED);
                }

                string[] parts = email.Split('@');
                if (parts.Length != 2)
                {
                    throw new InvalidOperationException(ErrorMessages.EMAIL_INVALID);
                }

                return new EmailRecord
                {
                    Email = email,
                    Username = parts[0],
                    Domain = parts[1],
                    CreatedDate = DateTime.Now,
                    Status = EmailStatus.Active,
                    DeactivatedDate = null
                };
            }
            catch (Exception ex) when (!(ex is ArgumentException || ex is InvalidOperationException))
            {
                System.Diagnostics.Debug.WriteLine($"创建邮箱记录失败: {ex.Message}");
                throw new InvalidOperationException($"{ErrorMessages.EMAIL_GENERATION_FAILED}: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量生成邮箱地址
        /// </summary>
        /// <param name="domain">域名</param>
        /// <param name="count">要生成的数量</param>
        /// <returns>生成的邮箱记录列表</returns>
        public List<EmailRecord> GenerateMultipleEmails(string domain, int count)
        {
            var results = new List<EmailRecord>();
            
            if (count <= 0 || !IsValidDomain(domain))
            {
                return results;
            }

            var existingEmails = _dataService.LoadEmails().Select(e => e.Email).ToList();

            for (int i = 0; i < count; i++)
            {
                string email = GenerateUniqueEmail(domain, existingEmails);
                if (!string.IsNullOrEmpty(email))
                {
                    string[] parts = email.Split('@');
                    if (parts.Length == 2)
                    {
                        var record = new EmailRecord
                        {
                            Email = email,
                            Username = parts[0],
                            Domain = parts[1],
                            CreatedDate = DateTime.Now,
                            Status = EmailStatus.Active,
                            DeactivatedDate = null
                        };
                        
                        results.Add(record);
                        existingEmails.Add(email); // 添加到已存在列表中，避免重复
                    }
                }
            }

            return results;
        }
    }
}