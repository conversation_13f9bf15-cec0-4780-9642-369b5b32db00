using Microsoft.VisualStudio.TestTools.UnitTesting;
using EmailGenerator.Services;
using EmailGenerator.Models;
using EmailGenerator.Helpers;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// 需求验证测试类
    /// 系统性验证所有需求的实现完整性
    /// </summary>
    [TestClass]
    public class RequirementsValidationTests
    {
        private ConfigurationService configService;
        private DataService dataService;
        private EmailService emailService;
        private string testConfigPath;
        private string testDataPath;

        [TestInitialize]
        public void Setup()
        {
            testConfigPath = Path.Combine(Path.GetTempPath(), $"req_config_{Guid.NewGuid()}.json");
            testDataPath = Path.Combine(Path.GetTempPath(), $"req_emails_{Guid.NewGuid()}.json");
            
            configService = new ConfigurationService();
            dataService = new DataService(testDataPath);
            emailService = new EmailService(dataService);
        }

        [TestCleanup]
        public void Cleanup()
        {
            if (File.Exists(testConfigPath)) File.Delete(testConfigPath);
            if (File.Exists(testDataPath)) File.Delete(testDataPath);
        }

        /// <summary>
        /// 验证需求1：域名设置功能
        /// 需求: 1.1, 1.2, 1.3, 1.4
        /// </summary>
        [TestMethod]
        public void ValidateRequirement1_DomainSettings()
        {
            // 1.1: 系统应当提供一个输入框用于设置邮箱域名
            // 这个需求通过UI测试验证，此处验证配置服务功能
            
            // 1.2: 系统应当验证域名格式的有效性
            Assert.IsTrue(emailService.IsValidDomain("example.com"), "有效域名应该通过验证");
            Assert.IsFalse(emailService.IsValidDomain(""), "空域名应该验证失败");
            Assert.IsFalse(emailService.IsValidDomain("invalid"), "无效域名应该验证失败");
            
            // 1.3: 系统应当将域名保存到配置文件中
            string testDomain = "test-requirement.com";
            configService.SaveDomain(testDomain);
            Assert.IsTrue(File.Exists(testConfigPath), "配置文件应该被创建");
            
            // 1.4: 系统应当自动加载之前保存的域名设置
            string loadedDomain = configService.LoadDomain();
            Assert.AreEqual(testDomain, loadedDomain, "应该能正确加载保存的域名");
        }

        /// <summary>
        /// 验证需求2：邮箱生成功能
        /// 需求: 2.1, 2.2, 2.3, 2.4, 2.5
        /// </summary>
        [TestMethod]
        public void ValidateRequirement2_EmailGeneration()
        {
            string testDomain = "generation-test.com";
            var existingEmails = new List<string>();
            
            // 2.1: 系统应当自动生成一个唯一的邮箱用户名
            string email1 = emailService.GenerateUniqueEmail(testDomain, existingEmails);
            Assert.IsNotNull(email1, "应该成功生成邮箱");
            Assert.IsTrue(email1.Contains("@"), "生成的邮箱应该包含@符号");
            
            // 2.2: 系统应当结合用户设置的域名形成完整的邮箱地址
            Assert.IsTrue(email1.EndsWith($"@{testDomain}"), "邮箱应该使用指定的域名");
            
            // 2.3: 系统应当检查该地址是否已存在于历史记录中
            existingEmails.Add(email1);
            string email2 = emailService.GenerateUniqueEmail(testDomain, existingEmails);
            Assert.AreNotEqual(email1, email2, "第二个邮箱应该与第一个不同");
            
            // 2.4: 如果邮箱地址已存在，系统应当重新生成直到获得唯一地址
            // 通过生成多个邮箱验证唯一性
            var generatedEmails = new HashSet<string>();
            for (int i = 0; i < 10; i++)
            {
                string email = emailService.GenerateUniqueEmail(testDomain, generatedEmails.ToList());
                Assert.IsFalse(generatedEmails.Contains(email), $"第{i+1}个邮箱应该是唯一的");
                generatedEmails.Add(email);
            }
            
            // 2.5: 当生成成功时，系统应当在界面上显示新生成的邮箱地址
            // 这个需求通过UI集成测试验证
        }

        /// <summary>
        /// 验证需求3：邮箱历史记录管理功能
        /// 需求: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8
        /// </summary>
        [TestMethod]
        public void ValidateRequirement3_EmailHistoryManagement()
        {
            string testDomain = "history-test.com";
            
            // 3.1: 系统应当加载并显示所有历史生成的邮箱地址
            var initialEmails = dataService.LoadEmails();
            Assert.IsNotNull(initialEmails, "应该能加载邮箱历史记录");
            
            // 添加测试邮箱
            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                CreatedDate = DateTime.Now,
                Domain = testDomain,
                Username = "test",
                Status = EmailStatus.Active
            };
            dataService.AddEmail(testEmail);
            
            // 3.2: 系统应当将新邮箱添加到显示列表中
            var updatedEmails = dataService.LoadEmails();
            Assert.AreEqual(initialEmails.Count + 1, updatedEmails.Count, "新邮箱应该被添加到列表中");
            
            // 3.3: 系统应当提供复制到剪贴板的功能
            // 这个需求通过UI测试验证，此处验证数据准备
            var emailToCopy = updatedEmails.Find(e => e.Email == testEmail.Email);
            Assert.IsNotNull(emailToCopy, "应该能找到要复制的邮箱");
            
            // 3.4, 3.5: 系统应当提供删除选定邮箱的功能
            dataService.DeleteEmail(testEmail.Email);
            var emailsAfterDeletion = dataService.LoadEmails();
            Assert.AreEqual(initialEmails.Count, emailsAfterDeletion.Count, "删除后邮箱数量应该恢复");
            Assert.IsNull(emailsAfterDeletion.Find(e => e.Email == testEmail.Email), "被删除的邮箱不应该存在");
            
            // 重新添加邮箱用于作废测试
            dataService.AddEmail(testEmail);
            
            // 3.6, 3.7: 系统应当提供作废选定邮箱的功能
            dataService.DeactivateEmail(testEmail.Email);
            var emailsAfterDeactivation = dataService.LoadEmails();
            var deactivatedEmail = emailsAfterDeactivation.Find(e => e.Email == testEmail.Email);
            Assert.IsNotNull(deactivatedEmail, "作废的邮箱应该仍然存在");
            Assert.AreEqual(EmailStatus.Deactivated, deactivatedEmail.Status, "邮箱状态应该是失效");
            Assert.IsNotNull(deactivatedEmail.DeactivatedDate, "失效邮箱应该有失效日期");
            
            // 3.8: 系统应当区分显示有效和失效的邮箱状态
            var activeEmails = emailsAfterDeactivation.FindAll(e => e.Status == EmailStatus.Active);
            var deactivatedEmails = emailsAfterDeactivation.FindAll(e => e.Status == EmailStatus.Deactivated);
            Assert.IsTrue(deactivatedEmails.Count > 0, "应该有失效的邮箱");
        }

        /// <summary>
        /// 验证需求4：数据持久化功能
        /// 需求: 4.1, 4.2, 4.3, 4.4, 4.5
        /// </summary>
        [TestMethod]
        public void ValidateRequirement4_DataPersistence()
        {
            string testDomain = "persistence-test.com";
            
            // 4.1: 系统应当将邮箱数据保存到本地文件中
            var testEmail = new EmailRecord
            {
                Email = "<EMAIL>",
                CreatedDate = DateTime.Now,
                Domain = testDomain,
                Username = "persist",
                Status = EmailStatus.Active
            };
            dataService.AddEmail(testEmail);
            Assert.IsTrue(File.Exists(testDataPath), "数据文件应该被创建");
            
            // 4.2: 系统应当从本地文件中移除对应的数据
            dataService.DeleteEmail(testEmail.Email);
            var emailsAfterDeletion = dataService.LoadEmails();
            Assert.IsNull(emailsAfterDeletion.Find(e => e.Email == testEmail.Email), "删除的邮箱不应该存在于文件中");
            
            // 重新添加用于状态更新测试
            dataService.AddEmail(testEmail);
            
            // 4.3: 系统应当更新本地文件中邮箱的状态信息
            dataService.UpdateEmailStatus(testEmail.Email, EmailStatus.Deactivated);
            var emailsAfterStatusUpdate = dataService.LoadEmails();
            var updatedEmail = emailsAfterStatusUpdate.Find(e => e.Email == testEmail.Email);
            Assert.IsNotNull(updatedEmail, "更新状态的邮箱应该存在");
            Assert.AreEqual(EmailStatus.Deactivated, updatedEmail.Status, "邮箱状态应该被正确更新");
            
            // 4.4: 系统应当从本地文件中加载所有邮箱数据包括状态信息
            var loadedEmails = dataService.LoadEmails();
            Assert.IsTrue(loadedEmails.Count > 0, "应该能加载邮箱数据");
            var loadedEmail = loadedEmails.Find(e => e.Email == testEmail.Email);
            Assert.IsNotNull(loadedEmail, "应该能加载特定邮箱");
            Assert.AreEqual(EmailStatus.Deactivated, loadedEmail.Status, "应该正确加载邮箱状态");
            
            // 4.5: 如果本地文件不存在，系统应当创建新的存储文件
            string newFilePath = Path.Combine(Path.GetTempPath(), $"new_file_{Guid.NewGuid()}.json");
            var newDataService = new DataService(newFilePath);
            var newEmails = newDataService.LoadEmails();
            Assert.IsNotNull(newEmails, "不存在的文件应该返回空列表");
            Assert.AreEqual(0, newEmails.Count, "新文件应该包含空列表");
            
            // 清理新创建的文件
            if (File.Exists(newFilePath))
                File.Delete(newFilePath);
        }

        /// <summary>
        /// 验证需求5：用户界面功能
        /// 需求: 5.1, 5.2, 5.3, 5.4, 5.5
        /// </summary>
        [TestMethod]
        public void ValidateRequirement5_UserInterface()
        {
            // 5.1, 5.2: 系统应当显示清晰的用户界面布局和明确的按钮标签
            // 这些需求主要通过UI测试验证，此处验证支持功能
            
            // 5.3, 5.4: 系统应当显示成功和错误提示信息
            // 验证错误消息常量存在
            Assert.IsFalse(string.IsNullOrEmpty(ErrorMessages.DOMAIN_EMPTY), "应该有域名为空的错误消息");
            Assert.IsFalse(string.IsNullOrEmpty(ErrorMessages.EMAIL_GENERATION_FAILED), "应该有邮箱生成失败的错误消息");
            
            // 验证成功消息常量存在
            Assert.IsFalse(string.IsNullOrEmpty(SuccessMessages.DOMAIN_SAVED), "应该有域名保存成功的消息");
            Assert.IsFalse(string.IsNullOrEmpty(SuccessMessages.EMAIL_GENERATED), "应该有邮箱生成成功的消息");
            
            // 5.5: 系统应当保持界面响应性
            // 通过性能测试验证异步操作
            string testDomain = "responsive-test.com";
            var startTime = DateTime.Now;
            
            // 执行多个操作，验证响应时间
            for (int i = 0; i < 10; i++)
            {
                var existingEmails = dataService.LoadEmails().ConvertAll(e => e.Email);
                string email = emailService.GenerateUniqueEmail(testDomain, existingEmails);
                var record = new EmailRecord
                {
                    Email = email,
                    CreatedDate = DateTime.Now,
                    Domain = testDomain,
                    Username = email.Split('@')[0],
                    Status = EmailStatus.Active
                };
                dataService.AddEmail(record);
            }
            
            var endTime = DateTime.Now;
            var duration = endTime - startTime;
            Assert.IsTrue(duration.TotalSeconds < 5, "10个邮箱的生成和保存应该在5秒内完成，保证界面响应性");
        }
    }
}