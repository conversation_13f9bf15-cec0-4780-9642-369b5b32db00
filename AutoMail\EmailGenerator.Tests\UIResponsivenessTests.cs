using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using EmailGenerator.Models;
using EmailGenerator.Services;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// UI响应性性能测试类
    /// </summary>
    [TestClass]
    public class UIResponsivenessTests
    {
        private string _testDataPath = string.Empty;
        private DataService _dataService = null!;

        [TestInitialize]
        public void Setup()
        {
            // 创建测试数据文件路径
            _testDataPath = Path.Combine(Path.GetTempPath(), $"test_emails_{Guid.NewGuid()}.json");
            _dataService = new DataService(_testDataPath);
        }

        [TestCleanup]
        public void Cleanup()
        {
            // 清理测试文件
            if (File.Exists(_testDataPath))
            {
                File.Delete(_testDataPath);
            }
        }

        /// <summary>
        /// 测试文件操作的性能
        /// </summary>
        [TestMethod]
        public void TestFileOperationPerformance()
        {
            // 准备测试数据
            var testEmails = GenerateTestEmails(1000);

            // 测试保存性能
            var stopwatch = Stopwatch.StartNew();
            bool success = _dataService.SaveEmails(testEmails);
            stopwatch.Stop();

            Assert.IsTrue(success, "保存应该成功");
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 10000, $"保存1000条记录应该在10秒内完成，实际用时: {stopwatch.ElapsedMilliseconds}ms");

            // 测试加载性能
            stopwatch.Restart();
            var loadedEmails = _dataService.LoadEmails();
            stopwatch.Stop();

            Assert.AreEqual(testEmails.Count, loadedEmails.Count, "加载的邮箱数量应该与保存的一致");
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 5000, $"加载1000条记录应该在5秒内完成，实际用时: {stopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// 测试批量操作的性能
        /// </summary>
        [TestMethod]
        public void TestBatchOperationPerformance()
        {
            var testEmails = GenerateTestEmails(500);

            // 测试批量保存性能
            var batchStopwatch = Stopwatch.StartNew();
            bool batchSuccess = _dataService.SaveEmails(testEmails);
            var batchLoadedEmails = _dataService.LoadEmails();
            batchStopwatch.Stop();

            Assert.IsTrue(batchSuccess, "批量操作应该成功");
            Assert.AreEqual(testEmails.Count, batchLoadedEmails.Count, "批量加载的数据应该正确");

            // 清理文件以便单个操作测试
            File.Delete(_testDataPath);

            // 测试单个操作性能
            var individualStopwatch = Stopwatch.StartNew();
            foreach (var email in testEmails)
            {
                _dataService.AddEmail(email);
            }
            var individualLoadedEmails = _dataService.LoadEmails();
            individualStopwatch.Stop();

            Assert.AreEqual(testEmails.Count, individualLoadedEmails.Count, "单个操作加载的数据应该正确");

            // 批量操作应该比单个操作更快
            Assert.IsTrue(batchStopwatch.ElapsedMilliseconds < individualStopwatch.ElapsedMilliseconds, 
                $"批量操作应该比单个操作更快。批量: {batchStopwatch.ElapsedMilliseconds}ms, 单个: {individualStopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// 测试重复操作的内存使用情况
        /// </summary>
        [TestMethod]
        public void TestRepeatedOperationMemoryUsage()
        {
            var initialMemory = GC.GetTotalMemory(true);

            // 执行多次操作
            for (int i = 0; i < 100; i++)
            {
                var testEmails = GenerateTestEmails(10);
                _dataService.SaveEmails(testEmails);
                var loadedEmails = _dataService.LoadEmails();
                
                Assert.AreEqual(testEmails.Count, loadedEmails.Count, "操作应该正确保存和加载数据");
                
                // 清理文件
                File.Delete(_testDataPath);
            }

            // 强制垃圾回收
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var finalMemory = GC.GetTotalMemory(true);
            var memoryIncrease = finalMemory - initialMemory;

            // 内存增长不应该超过10MB
            Assert.IsTrue(memoryIncrease < 10 * 1024 * 1024, $"重复操作内存使用增长过多: {memoryIncrease / 1024 / 1024}MB");
        }

        /// <summary>
        /// 生成测试邮箱数据
        /// </summary>
        /// <param name="count">生成数量</param>
        /// <returns>邮箱记录列表</returns>
        private List<EmailRecord> GenerateTestEmails(int count)
        {
            var emails = new List<EmailRecord>();
            var random = new Random();

            for (int i = 0; i < count; i++)
            {
                emails.Add(new EmailRecord
                {
                    Email = $"test{i}@example.com",
                    CreatedDate = DateTime.Now.AddMinutes(-random.Next(0, 10000)),
                    Domain = "example.com",
                    Username = $"test{i}",
                    Status = random.Next(0, 2) == 0 ? EmailStatus.Active : EmailStatus.Deactivated,
                    DeactivatedDate = random.Next(0, 2) == 0 ? DateTime.Now.AddMinutes(-random.Next(0, 1000)) : null
                });
            }

            return emails;
        }
    }
}