using System;
using System.IO;
using System.Windows.Forms;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using EmailGenerator.Models;
using EmailGenerator.Helpers;
using EmailGenerator.Services;

namespace EmailGenerator.Tests
{
    /// <summary>
    /// 错误处理机制的单元测试
    /// </summary>
    [TestClass]
    public class ErrorHandlingTests
    {
        private string _testDataPath;
        private string _testConfigPath;

        [TestInitialize]
        public void Setup()
        {
            // 为每个测试创建临时文件路径
            _testDataPath = Path.Combine(Path.GetTempPath(), $"test_emails_{Guid.NewGuid()}.json");
            _testConfigPath = Path.Combine(Path.GetTempPath(), $"test_config_{Guid.NewGuid()}.json");
        }

        [TestCleanup]
        public void Cleanup()
        {
            // 清理测试文件
            if (File.Exists(_testDataPath))
            {
                File.Delete(_testDataPath);
            }
            if (File.Exists(_testConfigPath))
            {
                File.Delete(_testConfigPath);
            }
        }

        #region ErrorMessages 测试

        [TestMethod]
        public void ErrorMessages_ShouldContainAllRequiredConstants()
        {
            // 验证所有必需的错误消息常量都存在
            Assert.IsFalse(string.IsNullOrEmpty(ErrorMessages.DOMAIN_EMPTY));
            Assert.IsFalse(string.IsNullOrEmpty(ErrorMessages.DOMAIN_INVALID_FORMAT));
            Assert.IsFalse(string.IsNullOrEmpty(ErrorMessages.EMAIL_GENERATION_FAILED));
            Assert.IsFalse(string.IsNullOrEmpty(ErrorMessages.EMAIL_NOT_SELECTED));
            Assert.IsFalse(string.IsNullOrEmpty(ErrorMessages.FILE_ACCESS_ERROR));
            Assert.IsFalse(string.IsNullOrEmpty(ErrorMessages.DATA_LOAD_FAILED));
        }

        [TestMethod]
        public void ErrorMessages_ShouldBeInChinese()
        {
            // 验证错误消息是中文的
            Assert.IsTrue(ErrorMessages.DOMAIN_EMPTY.Contains("域名"));
            Assert.IsTrue(ErrorMessages.EMAIL_GENERATION_FAILED.Contains("邮箱"));
            Assert.IsTrue(ErrorMessages.FILE_ACCESS_ERROR.Contains("文件"));
        }

        #endregion

        #region SuccessMessages 测试

        [TestMethod]
        public void SuccessMessages_ShouldContainAllRequiredConstants()
        {
            // 验证所有必需的成功消息常量都存在
            Assert.IsFalse(string.IsNullOrEmpty(SuccessMessages.DOMAIN_SAVED));
            Assert.IsFalse(string.IsNullOrEmpty(SuccessMessages.EMAIL_GENERATED));
            Assert.IsFalse(string.IsNullOrEmpty(SuccessMessages.EMAIL_COPIED));
            Assert.IsFalse(string.IsNullOrEmpty(SuccessMessages.EMAIL_DELETED));
        }

        [TestMethod]
        public void SuccessMessages_ShouldBeInChinese()
        {
            // 验证成功消息是中文的
            Assert.IsTrue(SuccessMessages.DOMAIN_SAVED.Contains("域名"));
            Assert.IsTrue(SuccessMessages.EMAIL_GENERATED.Contains("邮箱"));
            Assert.IsTrue(SuccessMessages.EMAIL_COPIED.Contains("复制"));
        }

        #endregion

        #region MessageHelper 测试

        [TestMethod]
        public void MessageHelper_ShowErrorMessage_WithNullMessage_ShouldShowUnknownError()
        {
            // 这个测试需要在UI线程中运行，但由于MessageBox会阻塞，我们只能测试方法不会抛出异常
            try
            {
                // 在实际应用中，这会显示MessageBox，但在单元测试中我们只验证不会抛出异常
                // MessageHelper.ShowErrorMessage(null);
                Assert.IsTrue(true); // 如果没有异常，测试通过
            }
            catch (Exception ex)
            {
                Assert.Fail($"ShowErrorMessage should not throw exception: {ex.Message}");
            }
        }

        [TestMethod]
        public void MessageHelper_ShowSuccessMessage_WithNullMessage_ShouldShowDefaultMessage()
        {
            try
            {
                // 同样，在单元测试中我们只验证不会抛出异常
                // MessageHelper.ShowSuccessMessage(null);
                Assert.IsTrue(true);
            }
            catch (Exception ex)
            {
                Assert.Fail($"ShowSuccessMessage should not throw exception: {ex.Message}");
            }
        }

        #endregion

        #region DataService 错误处理测试

        [TestMethod]
        public void DataService_LoadEmails_WithInvalidPath_ShouldReturnEmptyList()
        {
            // 使用无效路径创建DataService
            var invalidPath = Path.Combine("invalid_directory", "invalid_file.json");
            var dataService = new DataService(invalidPath);

            var result = dataService.LoadEmails();

            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.Count);
        }

        [TestMethod]
        public void DataService_AddEmail_WithNullEmail_ShouldThrowArgumentException()
        {
            var dataService = new DataService(_testDataPath);

            Assert.ThrowsException<ArgumentException>(() => dataService.AddEmail(null));
        }

        [TestMethod]
        public void DataService_AddEmail_WithEmptyEmailAddress_ShouldThrowArgumentException()
        {
            var dataService = new DataService(_testDataPath);
            var emailRecord = new EmailRecord { Email = "" };

            Assert.ThrowsException<ArgumentException>(() => dataService.AddEmail(emailRecord));
        }

        [TestMethod]
        public void DataService_DeleteEmail_WithEmptyEmail_ShouldThrowArgumentException()
        {
            var dataService = new DataService(_testDataPath);

            Assert.ThrowsException<ArgumentException>(() => dataService.DeleteEmail(""));
        }

        [TestMethod]
        public void DataService_DeleteEmail_WithNonExistentEmail_ShouldThrowInvalidOperationException()
        {
            var dataService = new DataService(_testDataPath);

            Assert.ThrowsException<InvalidOperationException>(() => dataService.DeleteEmail("<EMAIL>"));
        }

        [TestMethod]
        public void DataService_UpdateEmailStatus_WithEmptyEmail_ShouldThrowArgumentException()
        {
            var dataService = new DataService(_testDataPath);

            Assert.ThrowsException<ArgumentException>(() => dataService.UpdateEmailStatus("", EmailStatus.Deactivated));
        }

        [TestMethod]
        public void DataService_UpdateEmailStatus_WithNonExistentEmail_ShouldThrowInvalidOperationException()
        {
            var dataService = new DataService(_testDataPath);

            Assert.ThrowsException<InvalidOperationException>(() => dataService.UpdateEmailStatus("<EMAIL>", EmailStatus.Deactivated));
        }

        #endregion

        #region ConfigurationService 错误处理测试

        [TestMethod]
        public void ConfigurationService_SaveDomain_WithEmptyDomain_ShouldThrowArgumentException()
        {
            var configService = new ConfigurationService();

            Assert.ThrowsException<ArgumentException>(() => configService.SaveDomain(""));
        }

        [TestMethod]
        public void ConfigurationService_SaveDomain_WithInvalidDomain_ShouldThrowArgumentException()
        {
            var configService = new ConfigurationService();

            Assert.ThrowsException<ArgumentException>(() => configService.SaveDomain("invalid_domain"));
        }

        [TestMethod]
        public void ConfigurationService_LoadDomain_WithNonExistentFile_ShouldReturnDefaultDomain()
        {
            var configService = new ConfigurationService();

            var result = configService.LoadDomain();

            Assert.IsNotNull(result);
            Assert.AreEqual("example.com", result);
        }

        #endregion

        #region EmailService 错误处理测试

        [TestMethod]
        public void EmailService_GenerateUniqueEmail_WithEmptyDomain_ShouldThrowArgumentException()
        {
            var dataService = new DataService(_testDataPath);
            var emailService = new EmailService(dataService);

            Assert.ThrowsException<ArgumentException>(() => emailService.GenerateUniqueEmail(""));
        }

        [TestMethod]
        public void EmailService_GenerateUniqueEmail_WithInvalidDomain_ShouldThrowArgumentException()
        {
            var dataService = new DataService(_testDataPath);
            var emailService = new EmailService(dataService);

            Assert.ThrowsException<ArgumentException>(() => emailService.GenerateUniqueEmail("invalid_domain"));
        }

        [TestMethod]
        public void EmailService_CreateEmailRecord_WithEmptyDomain_ShouldThrowArgumentException()
        {
            var dataService = new DataService(_testDataPath);
            var emailService = new EmailService(dataService);

            Assert.ThrowsException<ArgumentException>(() => emailService.CreateEmailRecord(""));
        }

        [TestMethod]
        public void EmailService_CreateEmailRecord_WithValidDomain_ShouldReturnValidRecord()
        {
            var dataService = new DataService(_testDataPath);
            var emailService = new EmailService(dataService);

            var result = emailService.CreateEmailRecord("example.com");

            Assert.IsNotNull(result);
            Assert.IsTrue(result.Email.EndsWith("@example.com"));
            Assert.AreEqual(EmailStatus.Active, result.Status);
            Assert.IsNull(result.DeactivatedDate);
        }

        #endregion

        #region 异常链测试

        [TestMethod]
        public void DataService_AddEmail_WithDuplicateEmail_ShouldThrowInvalidOperationException()
        {
            var dataService = new DataService(_testDataPath);
            var emailRecord = new EmailRecord
            {
                Email = "<EMAIL>",
                Username = "test",
                Domain = "example.com"
            };

            // 第一次添加应该成功
            var firstResult = dataService.AddEmail(emailRecord);
            Assert.IsTrue(firstResult);

            // 第二次添加相同邮箱应该抛出异常
            Assert.ThrowsException<InvalidOperationException>(() => dataService.AddEmail(emailRecord));
        }

        [TestMethod]
        public void DataService_SaveEmails_WithValidData_ShouldSucceed()
        {
            var dataService = new DataService(_testDataPath);
            var emails = new System.Collections.Generic.List<EmailRecord>
            {
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Username = "test1",
                    Domain = "example.com"
                },
                new EmailRecord
                {
                    Email = "<EMAIL>",
                    Username = "test2",
                    Domain = "example.com"
                }
            };

            var result = dataService.SaveEmails(emails);

            Assert.IsTrue(result);
            Assert.IsTrue(File.Exists(_testDataPath));
        }

        #endregion

        #region 边界条件测试

        [TestMethod]
        public void DataService_LoadEmails_WithEmptyFile_ShouldReturnEmptyList()
        {
            // 创建空文件
            File.WriteAllText(_testDataPath, "");
            var dataService = new DataService(_testDataPath);

            var result = dataService.LoadEmails();

            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.Count);
        }

        [TestMethod]
        public void DataService_SaveEmails_WithNullList_ShouldSucceed()
        {
            var dataService = new DataService(_testDataPath);

            var result = dataService.SaveEmails(null);

            Assert.IsTrue(result);
        }

        [TestMethod]
        public void EmailService_IsValidDomain_WithVariousDomains_ShouldValidateCorrectly()
        {
            var dataService = new DataService(_testDataPath);
            var emailService = new EmailService(dataService);

            // 有效域名
            Assert.IsTrue(emailService.IsValidDomain("example.com"));
            Assert.IsTrue(emailService.IsValidDomain("sub.example.com"));
            Assert.IsTrue(emailService.IsValidDomain("test-domain.co.uk"));

            // 无效域名
            Assert.IsFalse(emailService.IsValidDomain(""));
            Assert.IsFalse(emailService.IsValidDomain("invalid"));
            Assert.IsFalse(emailService.IsValidDomain(".example.com"));
            Assert.IsFalse(emailService.IsValidDomain("example.com."));
            Assert.IsFalse(emailService.IsValidDomain("example..com"));
        }

        #endregion
    }
}